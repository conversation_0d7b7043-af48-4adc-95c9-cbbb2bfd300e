// helper-pages/pages/mine/privacyAgreement/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
      scrollHeight:'600',
      showBtnLayout:'true',
      agreeTxt:"同意",
      content: `进销存隐私政策
      本隐私政策于 2023 年 08 月 01 日更新。
      请您阅读和熟悉我们的隐私政策，如您有任何问题，可与我们联系。
      引言
      新界泵业(浙江)有限公司（下文简称“新界”或“我们”）高度重视您的隐私。“进销存”是由新界为您提供业务拜访管理的平台，以及终端管理的平台。本隐私政策是针对新界公司进销存小程序做出的隐私方面的陈述与承诺。
      
      本隐私政策在制定时充分考虑到您的需求，您全面了解我们的个人信息收集和使用惯例，同时确保您最终能控制提供给我们的个人信息，这一点至关重要。本隐私政策规定我们如何收集、使用、披露、处理和存储您使用我们的进销存时提供给我们的信息。本隐私政策下“个人信息”指通过信息本身或通过关联其他信息后能够识别特定个人的数据。我们将严格遵守本隐私政策来使用这些信息。
      
      我们希望为您带来更好的用户体验。如果您对本隐私政策中描述的个人信息处理实践有任何疑问，请与我们联系。我们很乐意收到您的任何反馈。
      一、我们收集哪些信息以及如何使用
      （1）您须授权我们收集和使用您个人信息之情形
      收集个人信息的目的在于向您提供产品和/或服务，并且保证我们遵守适用的相关法律、法规及其他规范性文件。您有权自行选择是否提供该信息，但多数情况下，如果您不提供，我们可能无法向您提供相应的服务，也无法及时回应您所遇到的问题。我们会做最小范围的收集及合理使用，这些功能包括：
      1.1.1 为确保您获得更为流畅、轻松的使用体验， 在您使用小程序提供的相关服务时， 我们会通过小型数据文件“Cookie”识别您的身份， 以帮助您减少信息录入的次数和频率，或协助判断您账户的安全状况。Cookie通常包含标识符、站点名称以及一些号码和字符，借助于Cookie，网站能够存储您的偏好信息用于改善产品服务及用户体验，或及时发现并防范安全风险，为您提供更好的服务。我们不会将Cookie用于本政策所述目的之外的任何用途，您可根据自己的偏好留存或删除Cookie。您可清除软件内保存的所有Cookie，当您手动清除后您的相关信息即已删除。
      1.1.2 基于相机/摄像头的附加服务：您可在开启相机/摄像头权限后使用该功能进行扫码用于登录、扫一扫、签到、拍摄照片或视频用于实际拜访业务进行打卡签到。
      1.1.3 基于相册（图片库/视频库）的图片/视频访问及上传的附加服务：您可在开启相册权限后使用该功能上传您的图片/视频，以实现实际业务场景中，帮你查看执行事项的记录。
      1.1.4 基于麦克风的语音技术相关附加服务：您可在开启麦克风权限后使用麦克风实现和其它用户之间进行语音聊天以及语音搜索功能。请您知晓，即使您已同意开启麦克风权限，我们也仅会在您主动点击小程序内麦克风图标或录制视频时通过麦克风获取语音信息。
      1.1.5 基于日历的相关附加服务：您可以用于日历添加功能，可以方便快捷建立系统拜访计划。
      1.1.6 基于位置的相关附加服务：您可以在开启位置权限后获取手机GPS用于签到的定位，拒绝后定位信息将受到影响，基于位置的签到功能将无法使用。
      1.1.7 基于存储的相关附加服务：您可以在开启存储权限后将执行事项缓存到记录做存储，以及获得相关系统错误日志的记录能力。
      二、您可选择是否授权我们收集和使用您的个人信息的情形
      为使您更好的享受进销存为您提供的拜访管理、终端管理等，我们的以下附加功能中可能会收集和使用您的个人信息。如果您不提供这些个人信息，您依然可以使用进销存的基础服务。这些附加功能包括：我们可能收集您发给我们的反馈的问题、反馈日志，以及您填写的电话号码或邮箱，签到信息，上传的照片信息存储方便您的拜访执行操作。这些信息将用于让我们更好的了解您遇到的问题，以及联系您。您可以自行选择不使用这些附加功能，或不填写不上传这些信息。
      三、我们从第三方SDK和微信小程序中收集获得您个人信息
      在一些法律允许的情况下，我们将从微信小程序或第三方SDK获得您的个人信息，信息的内容包括小程序运行时的必要信息，这些信息将用于小程序的bug收集与优化。例如，在您授权的前提下，我们可能从页面收集您的浏览时长、按钮点击等埋点数据，不断优化您的使用体验。您可以在“自动化”页面下，您可手动取消设定的智能联动规则。
      
      我们如何使用技术
      移动分析：我们遵守适用的相关法律、法规及其他规范性文件，需要记录以下信息：您使用小程序的频率、业务程序内使用拜访执行步骤的频次、性能数据及应用程序崩溃发生的位置。我们不会将存储于分析软件内的信息关联到您在移动应用程序中提交的任何个人信息。
      
      四、保留政策
      我们基于本隐私政策中所述的信息收集的目的所必需的期间，或者遵守适用的相关法律要求保留个人信息。个人信息在完成收集目的，或在我们确认您的删除或注销申请后，或我们终止运营相应产品或服务后，我们将停止保留，并做删除或匿名化处理。如果是出于公众利益、科学、历史研究或统计的目的，我们将基于适用的法律继续保留相关数据，即使进一步的数据处理与原有的收集目的无关。
      五、您的权利
      （1）控制设置
      我们承认每个人对隐私权的关注各不相同。因此，我们提供了一些示例，说明小程序提供的各种方式，供您选择，以限制收集、使用、披露或处理您的个人信息，并控制您的隐私权设置： 打开或者关闭位置访问、相机、录音和读写手机存储权限；
      •	在小程序中点击“我的-设置-个人资料”修改您的个人密码， 
      •	登入或登出进销存账户。
      如果您之前因为上述目的同意我们使用您的个人信息，您可以随时与我们联系来改变您的决定。
      （2）您对您的个人信息享有的权利
      根据您所适用的国家或地区法律法规，您有权要求访问、更正、删除我们持有的与您相关的任何个人信息（以下简称请求）。
      与您进销存帐号中的个人信息相关的更多详细信息，您可以联系我们。 大多数法律要求个人信息主体提出的请求应遵循特定要求，本隐私政策要求您的请求应当符合以下情形：
      • 访问您的个人信息：如果您希望访问或编辑您的账户中的个人资料信息、更改您的密码，您可以登录您的账户后通过设置访问您的信息。
      • 更正您的个人信息：当您发现我们处理您的个人信息有错误时，您有权要求我们做出更正或者补充。您可以通过访问个人信息设置页面进行更正或补充说明或者直接联系我们。
      • 删除您的个人信息：在下列情况中，您可以提出删除个人信息请求：
      ① 如果我们处理个人信息的行为违反法律法规。
      ② 如果我们手机、使用您的个人信息，却未征得您的明确同意。
      ③ 如果我们处理个人信息的行为严重违反了与您的约定。
      ④ 如果您不需要使用我们的产品与服务，或您主动注销了系统账号。
      ⑤ 如果永久不需要我们提供产品或服务。
      ⑥ 如果我们与他人共享或者转让您的个人信息，却未征得您的明确同意，您有权要求我们及第三方删除。
      ⑦ 如果我们违反与您的约定，公开披露您的个人信息，您有权要求我们立即停止公开批露的行为，并发布通知要求相关接收方删除相应的信息。
      （3）注销服务或账号
      如您希望注销进销存帐号，可在离职后注销。由于注销进销存帐号的操作将使您无法使用进销存全线产品和服务，请您谨慎操作。我们为了保护您或他人的合法权益会结合您对进销存各产品和服务的使用情况判断是否支持您的注销请求。
      六、联系我们
      如果您对本隐私政策有任何意见或问题，或者您对我们收集、使用或披露您的个人信息有任何问题，请联系我们，并提及“隐私政策”。针对您关于个人信息相关的权利请求、问题咨询等时，我们有专业的团队解决你的问题。如果您咨询我们，我们会根据您的实际情况，提供可能适用的相关投诉途径的信息。
      
      新界泵业(浙江)有限公司
      浙江省温岭市大溪大洋城工业区
      感谢您对本隐私政策的细致阅读和支持！
      `
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      showBtnLayout:!wx.getStorageSync('protocolChecked')
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#content-box').boundingClientRect()
    query.select('#btn-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        scrollHeight: res[0].height -res[1].top - (this.data.showBtnLayout?res[2].height:0) -  36*2*getApp().globalData.rpxToPxRatio// 36是上下间隔
      })
    })
  },

  handleAgreeClick(){
    wx.setStorageSync('protocolChecked',true)
    wx.navigateBack({
      delta: 1,
    })
  },
  handleCancelClick(){
    wx.setStorageSync('protocolChecked',false)
    wx.navigateBack({
      delta: 1,
    })
  }
})