<!--pages/sob/index.wxml-->
<view class="root-layout" id="page-layout">
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onListPullRefresh" bind:loadmore="onListLoadmore">
    <block wx:if="{{sobList.length > 0}}">
      <view class="card-layout " wx:for="{{sobList}}" wx:key="gIndex" wx:for-index="gIndex"  data-sob-id="{{item.id}}">
          <view class="flex align-items-center">
            <viwe class=" flexbox font-main">{{item.name}}</viwe>
            <van-checkbox checked-color="#00b9c3" shape="round" value="{{item.id===checkSobId}}"  data-sob-id="{{item.id}}" bind:change="onClickItem" icon-size="40rpx"></van-checkbox>
          </view>
          <viwe class="flex font-sub m-t-24">
            <view class="font-sub-hint hint-layout">账套ID</view>
            <view>{{item.id}}</view>
          </viwe>
          <view class="flex align-items-center">
            <view class="flexbox flex font-sub m-t-24">
              <view class="font-sub-hint hint-layout">账套名称</view>
              <view>{{item.name}}</view>
            </view>
            <view class=" flex align-items-center">
              <viwe class="font-sub-hint m-r-8">设为默认</viwe> 
              <van-switch checked="{{ item.id===switchSobId }}" size="24rpx"  data-sob-id="{{item.id}}" bindchange="onClickSwitch"/>
            </view>
          </view>
      </view>
    </block>
  </listView>
  <footer-button btnWord="确认" id="footer-layout" bind:mainClick="onConfirmClick"></footer-button>
</view>