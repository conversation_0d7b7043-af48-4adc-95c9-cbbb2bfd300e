// pages/sob/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    switchSobId: '',
    checkSobId: '',
    listViewH: 475,
    sobList: [],
    pageIndex: 1,
    pageSize: 10
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      checkSobId: wx.getStorageSync('checkSetsOfBooksId')
    })
    this.getListData()

  },
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#footer-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH: res[0].height - res[1].height
      })
    })
  },
  onClickItem(e) {
    const {
      sobId
    } = e.currentTarget.dataset
    if (sobId) {
      this.setData({
        checkSobId: sobId
      })
    }
  },
  onClickSwitch(e) {
    const {
      sobId
    } = e.currentTarget.dataset
    if (sobId) {
      this.setData({
        switchSobId: sobId
      })
    }
  },
  onListPullRefresh() {
    this.setData({
      pageIndex: 1,
      sobList:[]
    })
    this.getListData()
  },
  onListLoadmore() {
    this.data.pageIndex = this.data.pageIndex + 1
    this.getListData()
  },
  async getListData() {
    const param = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {}
    }
    const sobList = await App.getHttp()._post('/api/sys/setsOfBooks/page', param)
    if (Array.isArray(sobList) && sobList.length > 0) {
      const findSob = sobList.find(findItem => findItem.isDefault === 2)
      this.setData({
        sobList: this.data.sobList.concat(sobList),
        switchSobId: findSob && findSob.id ? findSob.id : '',
      })
    }
  },
  async onConfirmClick() {
    if (!this.data.checkSobId) {
      wx.showToast({ title: '请选择一个账套', icon: 'none' });
      return
    }
    // 获取账套信息并保存
    wx.setStorageSync('checkSetsOfBooksId', this.data.checkSobId)
    const result = await App.getHttp()._post('/api/sys/myx/reloadSetsOfBooksId', {
      setsOfBooksId: this.data.checkSobId,
      defaultSetsOfBooksId: this.data.switchSobId,
      defaultSetBookFlag: 2
    })
    wx.setStorageSync('supInfo', { setsOfBooksId: result.id, setsOfBooksName: result.name, type: result.type })
    wx.removeStorageSync('custInfo')
    wx.removeStorageSync('vendorInfo')
    // 获取默认客户信息并保存
    const custList = await App.getHttp()._post('/api/mmd/common/setsOfBooksCust/getCustRelateInfo', { sobId: wx.getStorageSync('checkSetsOfBooksId') })
    if (Array.isArray(custList) && custList.length > 0) {
      wx.setStorageSync('custInfo', custList[0])
    } 
    // 获取菜单并保存
    const menus = await App.getHttp()._post('/api/sys/myx/menu', {})
    if (menus.length == 0) {
      wx.removeStorageSync('custMenus')
      App.setDefaultUserBench()
      wx.showToast({
        title: '无任何菜单权限,请联系管理员',
        icon: 'none',
        duration: 3000
      })
    } else {
      wx.setStorageSync('custMenus', menus)
      App.setDefaultUserBench()
      wx.reLaunch({
        url: '/pages/ccs/workbench/index',
      })
    }
  }
})