<view class='page'>
  <!-- <image class="page-bg" src='/asset/imgs/login_bg.png' mode="aspectFit"></image> -->
  <view class="page-box flex column align-items-center">
    <view class="login-bg-box flex_center">
      <image class="logo" src='/asset/imgs/logo.png' mode="aspectFit"></image>
    </view>

    <view class="page-form">
      <view class="{{loginType ==='account'?'account-login-show':'account-login-hidden'}}">
        <van-field value="{{acc}}" name="tel" title-width="116rpx" clearable custom-style="font-family: SanFranciscoText-Semibold;color: #161C24;" label="账号" placeholder="请输入账号" focus="{{accFocus}}" error-message="{{accErrorMsg}}" bind:change="onChangeAcc">
          <!--<view slot="right-icon" style="width: 24px;text-align: center;" bindtap="clickAccount">
             <van-icon class="right-icon" size="19" name="/asset/svgs/account.svg" color="#bfbfbf" /> 
          </view>-->
        </van-field>
        <van-field value="{{psw}}" name="psw" title-width="116rpx" password="{{showPws}}" focus="{{pswFocus}}" clearable custom-style="margin-top: 64rpx;font-family: SanFranciscoText-Semibold;color: #161C24;" label="密码" placeholder="请输入密码" error-message="{{pswErrorMsg}}" bind:change="onChangePsw">
          <van-icon class="right-icon" slot="right-icon" bindtap="clickSee" size="24" name="{{showPws?'/asset/svgs/eye-see.svg':'/asset/svgs/eye.svg'}}" />
        </van-field>
        <view class="check-box">
          <van-checkbox checked-color="#00b9c3" shape="square" value="{{checked}}" bind:change="onClickCheckBox" icon-size="32rpx"><text class="check-label">记住密码</text></van-checkbox>
          <!-- <view class="forget-password" bindtap="onForgetPassWord">忘记密码?</view> -->
        </view>
      </view>
    </view>
    <button class="submit-box" style="background:{{psw.length>0?'#00b9c3':'#68babf'}}" bindtap="onLogIn">登录</button>
    <button wx:if="{{EVNval != '正式服'}}" class="submit-box" style="background:#008080" bindtap="onClickShowActionUrl">切换环境</button>
    <view class="protocol-check-box">
      <van-checkbox checked-color="#00b9c3" shape="round" value="{{protocolChecked}}" bind:change="handleProtocolChecked" icon-size="40rpx">
        <view class="check-label">
          我已阅读并同意
          <text class="protocol-btn"  catchtap="toCustAgreement">《服务条款和隐私政策》</text>
        </view>
      </van-checkbox>
    </view>
  </view>
  <van-popup show="{{ agreeWarnShow }}" position="bottom" bind:close="onAccountPickerClose">
    <view class="agreement-layout">
      <view class="title">请阅读并同意以下条款</view>
      <view class="link" bindtap="toCustAgreement">《服务条款和隐私政策》</view>
      <van-button type="info" block bind:click="toCusProtocol">同意</van-button>
    </view>
  </van-popup>
  <van-action-sheet show="{{ showActionUrl }}" actions="{{ actionUrls }}" cancel-text="取消" bind:close="onCloseAction" bind:select="onSelectAction" bind:cancel="onCloseAction" />
</view>