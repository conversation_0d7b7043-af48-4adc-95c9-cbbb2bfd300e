// pages/login/index.js
//获取应用实例
const App = getApp()
const dict = require('../../utils/dict')
const util = require('../../utils/util')
const {
  baseUrl
} = require('../../utils/api.js')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    EVNval: '开发',
    showPws: true,
    checked: false,
    protocolChecked: false,
    openid: '',
    tel: '',
    psw: '',
    acc: '',
    verifyCode: '',
    imageVerifyCode: '',
    telErrorMsg: '',
    pswErrorMsg: '',
    accErrorMsg: '',
    vcodeErrorMsg: '',
    imageVcodeErrorMsg: '',
    imageVerify: '',
    accFocus: false,
    telFocus: false,
    codeFocus: false,
    pswFocus: false,
    loginType: 'account',
    currentTime: 60,
    time: '获取验证码',
    agreeWarnShow: false,
    sessionId: '',
    showActionUrl: false,
    actionUrls: [
      { name: 'sit环境', value: 'https://ccs-sit-stable-pub.meicloud.com' },
      { name: 'uat_50443', value: 'https://youyou.shimge.com:50443' },
      { name: '生产_8443', value: 'https://youyou.shimge.com:8443' },
    ]
  },
  onLoad: function (options) {
    this.setData({
      acc: wx.getStorageSync('acc'),
      psw: wx.getStorageSync('psw'),
      checked: !!wx.getStorageSync('psw'),
      EVNval: App.globalData.EVNval
    })
    this.modalConfirm()
  },
  /* --------------- methods ----------------------*/
  onChangeAcc(event) {
    this.setData({
      acc: event.detail,
      accErrorMsg: ''
    })
  },
  onChangeTel(event) {
    this.setData({
      tel: event.detail,
      telErrorMsg: ''
    })
  },
  onChangePsw(event) {
    this.setData({
      psw: event.detail,
      pswErrorMsg: ''
    })
  },
  onImageVerifyCode(event) {
    this.setData({
      imageVerifyCode: event.detail,
      imageVcodeErrorMsg: ''
    })
  },
  onChangeVcode(event) {
    this.setData({
      verifyCode: event.detail,
      vcodeErrorMsg: ''
    })
  },
  clickSee() {
    this.setData({
      showPws: !this.data.showPws
    })
  },
  onClickCheckBox() {
    this.setData({
      checked: !this.data.checked,
    })
  },
  handleProtocolChecked() {
    const protocolChecked = !this.data.protocolChecked
    wx.setStorageSync('protocolChecked', protocolChecked)
    this.setData({
      protocolChecked,
    })
  },
  modalConfirm() {
    let _this = this
    // 登录
    wx.login({
      success: wxRes => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        App.getHttp()._post('/api/sys/getUserInfoByMyxCode', {
          code: wxRes.code
        }, false, false).then(res => {
          if (res && res.mobileTelephone) {
            _this.setData({
              openid: res.openid,
              tel: res.mobileTelephone,
              psw: res.passWord
            })
          }
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.setData({
      protocolChecked: wx.getStorageSync('protocolChecked', false),
    })
  },
  getVerifyCode() {
    let that = this
    let currentTime = that.data.currentTime;
    if (!this.data.tel) {
      this.setData({
        telErrorMsg: '手机号不能为空',
        telFocus: true
      })
      return
    }
    if (!this.data.imageVerifyCode) {
      this.setData({
        imageVcodeErrorMsg: '图片验证码不能为空',
      })
      return
    }
    if (!/^1[3456789]\d{9}$/.test(this.data.tel)) {
      this.setData({
        telErrorMsg: '手机号输入有误!',
        telFocus: true
      })
      return
    }
    if (that.data.time.includes('s')) {
      return
    }

    let tel = this.data.tel
    wx.request({
      url: `${baseUrl}myx/bus-admin-web/visit/sendMessage?telphone=${tel}&code=${this.data.imageVerifyCode}`,
      data: {},
      method: 'GET',
      header: {
        'content-type': 'application/json',
        Cookie: 'MEICLOUDCCSSESSIONID=' + this.data.sessionId,
        'SETS_OF_BOOKS_ID': wx.getStorageSync('userInfo').setsOfBooksId
      },
      complete: (res) => {
        if (res.data.success) {
          that.setData({
            time: currentTime + 's'
          })
          let interval = setInterval(function () {
            currentTime--;
            that.setData({
              time: currentTime + 's'
            })
            if (currentTime <= 0) {
              clearInterval(interval)
              that.setData({
                time: '获取验证码',
                currentTime: 60,
                isClick: false
              })
            }
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.chnDesc,
            icon: 'none',
            duration: 3000
          })
        }
      }
    })
  },
  async onLogIn() {
    const self = this;
    if (!this.data.protocolChecked) {
      // 提醒未勾选
      this.setData({
        agreeWarnShow: true
      })
      return
    }
    if (this.data.loginType === 'account') {
      if (this.data.acc.length == 0) {
        this.setData({
          accErrorMsg: '账号不能为空',
          accFocus: true
        })
        return
      }
      if (this.data.psw.length == 0) {
        this.setData({
          pswErrorMsg: '密码不能为空',
          pswFocus: true
        })
        return
      }
      this.data.acc = this.data.acc.trim()
      wx.login({
        success: async wxRes => {
          let params = {
            loginId: this.data.acc,
            loginSystem: 2,
            code: wxRes.code,
            password: util.encodePassword(this.data.psw)
          }
          const res = await App.getHttp()._post('/api/sys/updateMyxUserRelate', params, false, false)
          wx.setStorageSync('userInfo', res)
          wx.setStorageSync('psw', this.data.checked ? this.data.psw : '')
          wx.setStorageSync('acc', this.data.acc)
          this.getDictMap()
          const result = await App.getHttp()._post('/api/sys/queryDefaultSetBook', {})
          wx.setStorageSync('checkSetsOfBooksId', result)
          if (result) {
            this.checkJurisdiction()
          } else {
            wx.reLaunch({
              url: '/pages/sob/index',
            })
          }
        }
      })
    } else if (this.data.loginType === 'verifyCode') {
      if (this.data.tel.length == 0) {
        this.setData({
          telErrorMsg: '手机号不能为空',
          telFocus: true
        })
        return
      }
      if (this.data.verifyCode.length == 0) {
        this.setData({
          vcodeErrorMsg: '验证码不能为空',
          codeFocus: true
        })
        return
      }
      let code = this.data.verifyCode
      let tel = this.data.tel

      App.getHttp()._get(`myx/bus-admin-web/visit/plogin?code=${code}&phone=${tel}`).then(res => {
        if (res.firstLogin == true) {
          wx.showModal({
            showCancel: false,
            title: "温馨提示",
            content: "首次登录,请修改密码",
            success: function (ctx) {
              if (ctx.confirm) {
                wx.setStorageSync('userInfo', res)
                wx.setStorageSync('acc', self.data.acc)
                wx.navigateTo({
                  url: '/helper-pages/pages/mine/changePwd/index',
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '登录成功',
            icon: 'success',
          })
          wx.setStorageSync('userInfo', res)
          wx.setStorageSync('psw', this.data.checked ? this.data.psw : '')
          wx.setStorageSync('acc', this.data.acc)
          this.checkJurisdiction()
        }
      })
    }

  },
  //获取字典
  async getDictMap() {
    const res = await App.getHttp()._post('/api/sys/dict/findDictItemByCodes', {
      codes: App.globalData.dicts
    })
    if (Array.isArray(res) && res.length) {
      let map = {}
      res.forEach(item => {
        map = Object.assign({}, map, item)
      })
      wx.setStorageSync('dictMap', map)
    }
  },
  toCusProtocol() {
    this.setData({
      agreeWarnShow: false
    })
    this.handleProtocolChecked()
  },
  toCustAgreement() {
    this.setData({
      agreeWarnShow: false
    })
    wx.navigateTo({
      url: '/pages/agreement/index',
    })
  },
  //检查权限
  async checkJurisdiction() {
    // 获取账套信息并保存
    const result = await App.getHttp()._post('/api/sys/myx/reloadSetsOfBooksId', {
      setsOfBooksId: wx.getStorageSync('checkSetsOfBooksId'),
    })
    wx.setStorageSync('supInfo', { setsOfBooksId: result.id, setsOfBooksName: result.name, type: result.type })
    wx.removeStorageSync('custInfo')
    wx.removeStorageSync('vendorInfo')
    // 获取默认客户信息并保存
    const custList = await App.getHttp()._post('/api/mmd/common/setsOfBooksCust/getCustRelateInfo', { sobId: wx.getStorageSync('checkSetsOfBooksId') })
    if (Array.isArray(custList) && custList.length > 0) {
      wx.setStorageSync('custInfo', custList[0])
    }
    // 获取菜单并保存
    const menus = await App.getHttp()._post('/api/sys/myx/menu', {})
    if (menus.length == 0) {
      wx.removeStorageSync('custMenus')
      App.setDefaultUserBench()
      wx.showToast({
        title: '无任何菜单权限,请联系管理员',
        icon: 'none',
        duration: 3000
      })
    } else {
      wx.setStorageSync('custMenus', menus)
      App.setDefaultUserBench()
      wx.reLaunch({
        url: '/pages/ccs/workbench/index',
      })
    }
  },
  // 忘记密码
  onForgetPassWord() {
    // wx.navigateTo({
    //   url: `./changePwd/index?acc=${this.data.acc}`,
    // })
  },
  // 获取图片验证码
  async getImageVerify() {
    const self = this
    wx.request({
      url: `${baseUrl}myx/bus-admin-web/visit/kaptcha/codes`,
      data: {},
      method: 'GET',
      header: {
        'content-type': 'application/json',
        Cookie: 'MEICLOUDCCSSESSIONID=' + this.data.sessionId,
        'SETS_OF_BOOKS_ID': wx.getStorageSync('userInfo').setsOfBooksId
      },
      complete: (res) => {
        const url = 'data:image/png;base64,' + res.data.content.imageBase64
        if (!self.data.sessionId) {
          self.setData({
            sessionId: res.data.content.sessionId
          })
        }
        self.setData({
          imageVerify: url
        })
      }
    })
  },
  onClickShowActionUrl() {
    this.setData({
      showActionUrl: true
    })
  },
  onCloseAction() {
    this.setData({
      showActionUrl: false
    })
  },
  onSelectAction(e) {
    const value = e.detail.value
    wx.clearStorageSync()
    wx.setStorageSync('host', value)
  }
})