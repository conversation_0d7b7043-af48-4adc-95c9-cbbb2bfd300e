<!--pages/sob/index.wxml-->
<view class="root-layout" id="page-layout">
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onListPullRefresh" >
    <block wx:if="{{custList.length > 0}}">
      <view class="card-layout " wx:for="{{custList}}" wx:key="gIndex" wx:for-index="gIndex"  data-cust-id="{{item.custId}}">
          <view class="flex align-items-center">
            <viwe class=" flexbox font-main">{{item.custName}}</viwe>
            <van-checkbox checked-color="#00b9c3" shape="round" value="{{item.custId===checkCustId}}"  data-cust-id="{{item.custId}}" bind:change="onClickItem" icon-size="40rpx"></van-checkbox>
          </view>
          <viwe class="flex font-sub m-t-24">
            <view class="font-sub-hint hint-layout">客户编码</view>
            <view>{{item.custCode}}</view>
          </viwe>
          <view class="flex align-items-center">
            <view class="flexbox flex font-sub m-t-24">
              <view class="font-sub-hint hint-layout">客户类型</view>
              <view>{{item.custTypeName}}</view>
            </view>
          </view>
      </view>
    </block>
  </listView>
  <footer-button btnWord="确认" id="footer-layout" bind:mainClick="onConfirmClick"></footer-button>
</view>