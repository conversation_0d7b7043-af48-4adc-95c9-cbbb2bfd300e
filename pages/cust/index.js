// pages/sob/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    switchSobId: '',
    checkCustId: '',
    listViewH:475,
    custList: [],
    pageIndex: 1,
    pageSize: 10
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      checkCustId: wx.getStorageSync('custInfo')?.custId
    })
    this.getListData()

  },
  onReady:function(){
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#footer-layout').boundingClientRect()
    query.exec((res)=>{
      this.setData({
        listViewH:res[0].height-res[1].height
      })
    })
  },
  onClickItem(e) {
    const {
      custId
    } = e.currentTarget.dataset
    if (custId) {
      this.setData({
        checkCustId: custId
      })
    }
  },
  onListPullRefresh: function () {
    this.setData({
      pageIndex: 1
    })
    this.getListData()
  },
  async getListData() {
    const param = {
      sobId: wx.getStorageSync('checkSetsOfBooksId')
    }
    const custList = await App.getHttp()._post('/api/mmd/common/setsOfBooksCust/getCustRelateInfo', param)
    if(Array.isArray(custList)&&custList.length>0){
      this.setData({
        custList:custList
      })
    }
  },
  async onConfirmClick(){
    if(!this.data.checkCustId){
      wx.showToast({ title: '请选择一个客户', icon: 'none' });
      return 
    }
    wx.removeStorageSync('custInfo')
    wx.removeStorageSync('vendorInfo')
    const findCust = this.data.custList.find(res=>res.custId==this.data.checkCustId)
    // 获取默认客户信息并保存
    wx.setStorageSync('custInfo', findCust)
    // 获取菜单并保存
    const menus = await App.getHttp()._post('/api/sys/myx/menu', {})
    if (menus.length == 0) {
      wx.removeStorageSync('custMenus')
      App.setDefaultUserBench()
      wx.showToast({
        title: '无任何菜单权限,请联系管理员',
        icon: 'none',
        duration: 3000
      })
    } else {
      wx.setStorageSync('custMenus', menus)
      App.setDefaultUserBench()
      wx.reLaunch({
        url: '/pages/ccs/workbench/index',
      })
    }
  }
})