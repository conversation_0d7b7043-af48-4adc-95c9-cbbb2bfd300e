// pages/ccs/workbench/index.js
import * as echarts from '../echarts/ec-canvas/echarts.min'
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    windowWidthRpx: "375", // 轮播广告高度
    custName: '未选择客户',
    benchList: [],
    swiperList: [],
    newsList: [],
    newsNum: 0,
    navMarginTop: 24,
    navMarginBottom: 8,
    navMarginRight: 100,
    scrollHeight: 675,
    unReadMessageCount: '',
    activeName: 'custanalyse',
    typeList: [
      {
        id: 'saleanalyse',
        title: '销售分析',
        name: 'saleanalyse',
      },
      {
        id: 'stockanalyse',
        title: '库存分析',
        name: 'stockanalyse',
      }
    ],
    haveAdData: true,
    scrollIntoView: '',
    scrollTop: 0,
    offsetTop: 0,
    ec: {
      lazyLoad: true,
    },
    dataList: [],
    totalCount: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 头部区域可以提前设置
    const menuRect = App.globalData.menuButtonClientReact
    const navMarginBottomPx = 8
    this.setData({
      navMarginBottom: navMarginBottomPx,
      navMarginTop: (menuRect.top),
      navMarginRight: (App.globalData.windowWidth - menuRect.left + 24),//24为补充的间隙
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 设置可滑动区域
    const query = wx.createSelectorQuery()
    query.select('#scroller').boundingClientRect()
    let windowHeight = App.globalData.screenHeight;
    query.exec((res) => {
      this.setData({
        scrollHeight: App.globalData.screenHeight - res[0].top - App.globalData.deviceBottomOccPx - 100 * App.globalData.rpxToPxRatio, //100自定义tabbar的高度的rpx单位
        // windowWidthRpx: windowHeight
      })
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 全局自定义custom-tab-bar需要
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      // this.getTabBar().setData({
      //   selected: 0
      // })
      this.getTabBar().initSelected(getApp().tabbarIndex);
    }
    const supInfo = wx.getStorageSync('custInfo')
    // ...断开与globalData的关联
    let userBench = [...wx.getStorageSync('userBench') || App.globalData.defaultUserBench]
    console.log(userBench, 'userBenchuserBench', wx.getStorageSync('userBench'), App.globalData.defaultUserBench)
    userBench.push({
      icon: '/asset/imgs/bench/more.png',
      text: '更多应用',
      to: '/pages/ccs/benchCenter/index'
    })
    this.setData({
      custName: `${supInfo.custName}`,
      benchList: userBench
    })
    // 获取公告
    this.getNewsList()
    // 获取未读消息,一起不做消息
    // this.getUnreadCount()
    this.getAnalysisData()
  },

  getUnreadMsgCount() {
    const param = {
      custCode: wx.getStorageSync('mastCode'),
      readStatus: 1
    }
    App.getHttp()._post('myx/ccs-mobile-web/msg/notice/getUnreadMessageNum', param).then(res => {
      this.setData({
        unReadMessageCount: res && res.count == 0 ? '' : res.count
      })
    })
  },

  linkToMessage() {
    wx.navigateTo({
      url: '/pages/ccs/message/msgList/msgList',
    })
  },
  noteAd() {
    console.log('cccccc')
    this.setData({
      haveAdData: false
    })
  },

  onClickCustMore() {
    wx.navigateTo({
      url: '/pages/cust/index',
    })
  },

  onClickNewsMore() {
    wx.navigateTo({
      url: '/pages/ccs/newsCenter/newsList/newsList?type=1',
    })
  },
  getNewsList() {
    const userInfo = wx.getStorageSync('userInfo')
    const param = {
      pageIndex: 1,
      pageSize: 9,
      param: {
        receiveUserId: userInfo.userId,
        type: 3
      }
    }
    App.getHttp()._post('/api/mms/news/myx/page', param, true).then(res => {
      this.setData({
        newsList: res.recordList,
        swiperList: this.handleSwiperList(res.recordList),
        newsNum: res.totalRecord
      })
    })
  },
  handleSwiperList(list) {
    let arr = []
    list.forEach((item, index) => {
      let index2 = Math.floor(index / 3)
      if (index2 > 2) {
        return;
      }
      if (!arr[index2]) {
        arr[index2] = { newsList: [] }
      }
      arr[index2].newsList.push(item)
    })
    return arr;
  },
  // 切换分析模块导航
  onChangeTab(e) {
    this.setData({
      activeName: e.detail.name,
      scrollIntoView: e.detail.name
    })
    if (e.detail.name == 'saleanalyse') {
      this.selectComponent('#sales-analysis').init()
    } else if (e.detail.name == 'stockanalyse') {
      this.getAnalysisData()
      // this.selectComponent('#inventory-analysis').getAnalysisData()
    } else {
      this.selectComponent('#sales-analysis').init()
      this.getAnalysisData()
      // this.selectComponent('#inventory-analysis').getAnalysisData()
    }
  },
  getUnreadCount() {
    const userInfo = wx.getStorageSync('userInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param = {
      isRead: 1,
      receiveUserId: userInfo.userId,
      receiveUserSetsId: supInfo.setsOfBooksId,
    }
    App.getHttp()._post('/api/message/notifyMessage/gerCurrentUserUnreadCount', param, true).then(res => {
      this.setData({
        unReadMessageCount: res.content && res.content == 0 ? '' : res.content
      })
    })
  },
  // 滚动事件 给tab计算上边距
  onScroll(event) {
    wx.createSelectorQuery()
      .select('#scroller')
      .boundingClientRect((res) => {
        this.setData({
          scrollTop: event.detail.scrollTop,
          offsetTop: res.top,
        });
      })
      .exec();
  },
  initChart() {
    const that = this
    this.selectComponent('#inventory-chart').init((canvas, width, height, dpr) => {
      // 初始化图表
      let variable = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr, // 像素
      });
      variable.setOption(that.optionFunc());
      return variable//一定要return 否则展示会有问题
    });
  },
  optionFunc(chartData) {
    const colors = ['#A0DD80', '#5D95F7', '#FAAE16']
    var option = {
      tooltip: {
        show: false
      },
      title: {
        text: `${this.data.totalCount}\n库存数量`,
        top: 'middle',
        left: 'center',
        textStyle: {
          fontSize: 12,
          fontFamily: 'PingFang SC-Regular, PingFang SC',
          fontweight: 400,
          color: '#242424'
        }
      },

      legend: {
        bottom: 0,
        itemWidth: 8,
        itemGap: 16,
        data: this.data.dataList.map((item, index) => {
          return {
            name: item.warehouseName,
            icon: 'circle'
          }
        })
      },
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: ['40%', '60%'],
          avoidLabelOverlap: false,
          label: {
            formatter: '{c}',
          },
          itemStyle: {
            borderWidth: 2, //设置border的宽度有多大
            borderColor: '#fff',
          },
          data: this.data.dataList.map((item, index) => {
            return {
              value: item.qtyOnhand,
              name: item.warehouseName,
              itemStyle: { color: colors[index] }
            }
          })
        }
      ]
    };
    return option;
  },
  onClickToDetail() {
    wx.navigateTo({
      url: '/pages/ccs/analysis/inventoryAnalysis/index'
    })
  },
  getAnalysisData() {
    const url = '/api/psi/currentInvSearch/myx/pageSum'
    const supInfo = wx.getStorageSync('supInfo')
    const params = {
      param: {
        setsOfBooksId: supInfo.setsOfBooksId,
      }
    }
    App.getHttp()._post(url, params).then(res => {
      if (res && res.length > 0) {
        let totalCount = res.reduce((pre, cur) => {
          return pre + parseFloat(cur.qtyOnhand)
        }, 0)
        this.setData({
          dataList: res,
          totalCount
        })
        this.initChart()
      }
    })
  }
})