/* pages/ccs/workbench/index.wxss */
.page-self-full{
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  color: rgba(0,0,0,0.85);
}
.root-layout { 
  background-color: #F2F2F2;
}
::-webkit-scrollbar{
  width: 0;
  height: 0;
  color: transparent;
}
.msg-inner-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #f97d4e;
  position: absolute;
  right: 0;
  top: 0;
}
.top-layout {
  background-color: #fff;
  width: 100%;
  box-shadow: 0 1rpx 5rpx 0 rgba(230,230,230,0.2);
  margin-bottom: 1rpx;
}
.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.nav-left {
  display: flex;
  align-items: center;
}
.top-nav-text {
  margin-left: 16rpx;
  line-height: 44rpx;
  font-size: 36rpx;
  font-weight: 400;
}
.sob-becnch-layout{
  padding: 20rpx 0 36rpx;
  background-color: white;
}
.sob-layout{
  padding: 0 32rpx;
}
.sob-layout .info-box{
  padding: 0 30rpx;
  height: 92rpx;
  box-shadow: 0 6rpx 22rpx 0 rgba(116,116,116,0.21);
  border-radius: 48rpx;
}
.sob-layout .info-box .left-image{
  width: 50rpx;
  height: 50rpx;
  margin-right: 14rpx;
}
.sob-layout .info-box .cust-info{
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 44rpx;
}
.sob-layout .info-box .switch-info{
  width: 150rpx;
  height: 44rpx;
  background: rgba(249,125,78,0.2);
  border-radius: 22rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #161C24;
  line-height: 34rpx;
}
.sob-layout .info-box .switch-info .switch_icon{
  width: 22rpx;
  height: 16rpx;
  margin-left: 6rpx;
}

.bench-layout {
  margin-top: 22rpx;
}
.msg-layout{
  position: relative;
  width: 48rpx;
  height: 48rpx;
  margin-left: 24rpx
}
.msg-img{
  width: 48rpx;
  height: 48rpx;
}
.msg-inner-dot{
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #F97D4E;
  position: absolute;
  right: 0;
  top: 0;
}

.news-layout{
  background-color: #fff;
  margin-top: 10rpx;
}
.newsList-title-box{
  padding: 0 32rpx;
  border-bottom:2rpx solid #E6E6E6;
}
.newsList-title{
  display: flex;
  height: 96rpx;
  line-height: 96rpx;
  justify-content: space-between;
}
.newsList-title-txt{
  font-size: 32rpx;
  color: #000;
}
.swiper-bottom-height{
  height: 74rpx;
}
.more-txt{
  display: inline-block;
  margin-right: 2rpx;
}
.arrow-right-box{
  display: inline-block;
  width: 34rpx;
  height: 30rpx;
}
.arrow-right{
  display: inline-block;
  width: 15.36rpx;
  height: 15.36rpx;
  border-top: 2rpx solid #8A8A8A;
  border-right: 2rpx solid #8A8A8A;
  transform: rotate(45deg);
}
.newsList-more{
  font-size: 28rpx;
  color: #8A8A8A;
}

.bg-white{
  width: 100%;
  background-color: white;
}

.anay-layout{
  margin-top: 24rpx;
}

.anay-layout .draw-box{
  width: 100%;
  height: 300rpx;
  background-color: #FFFFFF;
}
.anay-layout .title-box{
  padding: 24rpx 32rpx;
}
.tab-layout {
  width: 100%;
}
.cmp-layout {
  background-color: #fff;
}
.canvas-box {
  height: 480rpx;
  width: 100%;
}
.analysis-head {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 44rpx;
  padding: 26rpx 32rpx;
  border-bottom: 1px solid #E6E6E6;
}
.title-layout {
display: flex;
align-items: center;
justify-content: space-between;
padding: 28rpx 32rpx;
font-size: 28rpx;
}
.title-text {
font-family: PingFang SC-Medium, PingFang SC;
font-weight: 500;
color: #242424;
line-height: 32rpx;
}
.title-right {
display: flex;
align-items: center;
}
.icon-text {
font-family: PingFang SC-Regular, PingFang SC;
font-weight: 400;
color: #707070;
line-height: 32rpx;
}
.placeholder-layout{
  width: 100%;
  height: 32rpx;
}