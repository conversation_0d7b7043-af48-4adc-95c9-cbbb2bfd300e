<!--pages/ccs/workbench/index.wxml-->
<view class="page-self-full root-layout" id="root-layout">
<!-- 顶部标题区域 -->
  <view class="top-layout" id="top-layout">
    <view class="top-nav" style="padding: {{navMarginTop}}px {{navMarginRight}}px {{navMarginBottom}}px 24rpx">
      <view class="nav-left">
        <!-- <van-icon name="arrow-left" size="18px" bindtap="onClickArrow" color="#242424" /> -->
        <view class="top-nav-text">工作台</view>
      </view>
      <!-- <van-icon name="/asset/svgs/message-red.svg" size="28px" info="{{unReadMessageCount}}" bindtap="linkToMessage"/> -->
    </view>
  </view>
  
  <scroll-view id="scroller" scroll-y scroll-into-view="{{scrollIntoView}}" bind:scroll="onScroll" style="height: {{scrollHeight}}px;"  enhanced="{{true}}"	enable-passive="{{true}}"	show-scrollbar="{{false}}">
    <view wx:if="{{haveAdData}}" class="filter-layout">
      <adSwiper wx:if="{{true}}" radiusRpx="{{0}}" heightRpx="{{windowWidthRpx}}" adType="{{2}}" bind:noteAd="noteAd"></adSwiper>
    </view>
    <view class="sob-becnch-layout">
      <!-- <view class="sob-layout">
        <view class="info-box  flex align-items-center">
          <image class="left-image" src="/asset/imgs/cust_avatar.png" mode="aspectFit" />
          <view class="flexbox cust-info single-line-ellipsis">{{custName}}</view>
          <view class="switch-info flex_center" bindtap="onClickCustMore">
            <view>切换客户</view>
            <image class="switch_icon" src="/asset/imgs/sob_switch.png" mode="aspectFit" />
          </view>
        </view>
      </view> -->
      <!-- 10功能区订单 -->
      <view class="bench-layout">
        <gridLayout columnNum="4" iconSize="96rpx" layoutList="{{benchList}}" showBadge />
      </view>
    </view>
    <!-- <view>咨询公告区域</view> -->
    <!-- 公告 -->
    <view class="news-layout">
      <view class="newsList-title-box">
        <view class="newsList-title">
          <view class="newsList-title-txt">公告 ({{newsNum}})</view>
          <view class="newsList-more" bindtap="onClickNewsMore">
            <view class="more-txt">更多</view>
            <view class="arrow-right-box">
              <view class="arrow-right"></view>
            </view>
          </view>
        </view>
      </view>
      <swiper indicator-dots="{{swiperList.length>1}}" indicator-color="#E5E5E5" indicator-active-color="#00b9c3" autoplay="{{false}}" circular interval="3600" duration="1200" style="height: 580rpx" wx:if="{{newsNum>0}}">
        <swiper-item wx:for="{{swiperList}}" wx:key="adId" wx:for-item="item">
          <new-carts wx:for="{{item.newsList}}" wx:for-item="item2" wx:for-key="key2" wx:key="index2" data-value="{{index2}}" itemInfo="{{item2}}"></new-carts>
          <view class="swiper-bottom-height"></view>
        </swiper-item>
      </swiper>
      <no-product wx:else/>
    </view>
    <!-- 分析区域 -->
    <view class="m-t-24" id="custanalyse">
      <van-sticky scroll-top="{{ scrollTop }}" offset-top="{{ offsetTop }}">
        <van-tabs class="tab-layout " active="{{activeName}}" bind:change="onChangeTab" title-active-color="#00b9c3">
          <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
            <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
          </block>
        </van-tabs>
      </van-sticky>
      <view class="bg-white anay-layout" id="saleanalyse">
        <salesAnalysis id="sales-analysis"/>
      </view>
      <view class="bg-white anay-layout" id="stockanalyse">
        <!-- <inventoryAnalysis id="inventory-analysis"/> -->
        <!-- 用组件调用位置会错误，只能先直接不用组件的方式 -->
        <view class="cmp-layout">
          <view class="analysis-head">库存分析</view>
          <view class="title-layout">
              <view class="title-text">整体库存情况</view>
              <view class="title-right" bindtap="onClickToDetail">
                  <view class="icon-text">查看明细</view>
                  <van-icon name="arrow" color="#707070" size="16"></van-icon>
              </view>
          </view>
          <view class="canvas-box" >
              <ec-canvas wx:if="{{dataList.length > 0}}" id="inventory-chart" canvas-id="mychart-bar" ec="{{ ec }}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>
    <!-- 底部空余站位区 -->
    <view class="placeholder-layout"></view>
  </scroll-view >
</view>