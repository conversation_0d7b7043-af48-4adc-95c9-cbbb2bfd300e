<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<list-head isSup="{{true}}" isShowType="{{false}}" id="listHead" />
<view class="root-layout">
    <!-- 头部导航 -->
    <van-sticky offset-top="{{offsetTop}}">
        <view class="filter-layout" id="filter-layout">
            <view class="filter-layout-search flex-box align-center" catchtap="onClickSearchListener">
                <van-icon name="/asset/imgs/purchase/search.png" size="20" />
                <view class="search-text">搜索</view>
            </view>
        </view>
    </van-sticky>
    <view class="content flex-box">
        <scroll-view style="height: {{windowHeight}}rpx" class="scroll-left" scroll-y="{{true}}" show-scrollbar="{{false}}">
            <van-sidebar active-key="{{ activeKey }}" bind:change="onChangeLeftSidebar">
                <van-sidebar-item wx:for="{{classList}}" wx:key="index" title="{{item.name}}" />
            </van-sidebar>
        </scroll-view>
        <view class="content-right">
            <view class="smallClass-filter" id="smallClassFilter" wx:if="{{activeKey != 0 && smallClassIdList.length > 0}}">
                <view class="grid-block">
                    <van-grid column-num="3" border="{{false}}">
                    <van-grid-item use-slot custom-class="grid-item-block" content-class="grid-content" wx:for="{{wxsUtil.formatGoodList(isExand, smallClassIdList, 3)}}" wx:key="index">
                        <view class="grid-item {{curSmallClassId == item.id ? 'active-filter-type' : ''}}" data-id="{{item.id}}" bindtap="onClickFilterClass">{{item.name}}</view>
                    </van-grid-item>
                    </van-grid>
                </view>
                <view style="padding-bottom: 24rpx" >
                    <van-icon size="16" color="#707070" bindtap="onClickExpand" name="arrow-down" />
                </view>
            </view>
            <view class="good-filter flex-box align-center" id="goodFilter" wx:if="{{activeKey != 0}}">
                <!-- <view class="filter-item flex-box align-center" bindtap="onClickOrderByPrice">
                    <view class="{{orderByPriceActive ? 'acitveFilter' : ''}}">价格</view>
                    <image class="filter-img {{orderByPrice == 2 ? 'filter-up' : 'filter-down' }}" wx:if="{{orderByPrice != 0}}" src="/asset/imgs/purchase/d-caret-active.png" mode="aspectFit"></image>
                    <image class="filter-img" src="/asset/imgs/purchase/d-caret.png" wx:else mode="aspectFit"></image>
                </view> -->
                <!-- <view class="filter-item flex-box align-center" bindtap="onClickOrderBySales">
                    <view class="{{orderBySalesActive ? 'acitveFilter' : ''}}">销量</view>
                    <image class="filter-img {{orderBySales == 2 ? 'filter-up' : 'filter-down' }}" wx:if="{{orderBySales != 0}}" src="/asset/imgs/purchase/d-caret-active.png" mode="aspectFit"></image>
                    <image class="filter-img" src="/asset/imgs/purchase/d-caret.png" wx:else mode="aspectFit"></image>
                </view> -->
            </view>
           <scroll-view style="height: {{windowHeight}}rpx" scroll-top='{{topNum}}' class="scroll-right" 	show-scrollbar="{{false}}" scroll-y="{{true}}" bindscrolltolower="onTolower">
            <block wx:if="{{goodsList.length > 0}}">
                <view class="goodsList flex-box align-center" wx:for="{{goodsList}}" data-item="{{item}}" bindtap="onClickGood" wx:key="index">
                    <view class="good-img-layout">
                        <image class="good-img" src="{{item.itemUrl || item.picUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                        <image class="good-noQty" wx:if="{{item.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
                    </view>
                    <view class="good-info">
                        <view class="good-name">{{item.itemName}}</view>
                        <!-- <view class="good-specs">规格: {{item.specs}}</view> -->
                        <view class="good-price">
                            <view class="price-info">
                                <text class="price-symbol" wx:if="{{item.applyPrice || item.standardPrice}}">¥</text>
                                <text class="price-text" >{{item.applyPrice || item.standardPrice ? wxsUtil.moneyFormatInt((item.applyPrice || item.standardPrice), 'int') : '暂无报价'}}</text>
                                <text class="price-rem" wx:if="{{item.applyPrice||item.standardPrice}}">{{wxsUtil.moneyFormatInt((item.applyPrice || item.standardPrice))}}</text>
                                <!-- <text class="onhand-type type-1" wx:if="{{item.qtyOnhand == '无货'}}">{{item.qtyOnhand}}</text>
                                <text class="onhand-type type-2" wx:else>库存:{{item.qtyOnhand}}</text> -->
                            </view>
                            <van-icon name="/asset/imgs/purchase/good-shopCar.png" data-item="{{item}}" catchtap="onClickToShopCar" size="20"></van-icon>
                        </view>
                    </view>
                </view>
            </block>
            <!-- 缺省 -->
            <block class="m-t-25p" wx:else>
                <no-product noneTxt="暂无商品" />
            </block>
            <!-- 占位符 -->
            <view style="height: 80rpx"></view>
            </scroll-view> 
        </view>
    </view>
    <add-carts show="{{showAddCartsPop}}" bind:onchange="onchange" bind:onClose="onCloseAddCarts" item="{{addItemInfo}}" isSup="{{true}}"></add-carts>
</view>