// pages/ccs/inventory/invSearchHis/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showHis: true,
    showScanImg: false,
    listViewH: 500,
    active: '0',
    tabList: [],
    showList: [],
    serchValue: '',
    showPop: false,
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    navTop: 24,
    navHeight: 44,
    url: '',
    standType: 4, // 5入库 6出库 4调拨
    itemInfo: {},
    busCode: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let url = ''
    let standType = 4
    let busCode = ''
    let type = options.type
    if (type == 'invList') {
      url = '/api/psi/currentInv/myx/item/warehouseInv'
      busCode = 'inventory_query'
    } else if (type == "invOutBill") {
      url = '/api/psi/currentInv/myx/warehouse/itemInv'
      busCode = 'other_outbound'
      standType = 6
    } else if (type == "invTransfer") {
      url = '/api/psi/currentInv/myx/warehouse/itemInv'
      busCode = 'inventory_transfer'
      standType = 4
    } else if (type == 'invCount') {
      url = '/api/psi/currentInv/myx/item/warehouseInv'
      busCode = 'inventory_check'
    } else if (type == 'otherInBill') {
      url = '/api/mmd/common/item/page'
      busCode = 'other_warehousing'
      standType = 5
    }
    this.setData({
      type: options.type,
      showScanImg: options.scan && options.scan == 2,
      url,
      standType,
      busCode
    })
    if (this.data.showScanImg) {
      this.openScan()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  onSearchScan(){
    this.openScan()
  },
  openScan() {
    wx.scanCode({
      success: async (res) => {
        if (res.errMsg === 'scanCode:ok') {
          const result = await App.getHttp()._post('/api/psi/barCodeFlow/scanShimgeBarcode', {barCode:res.result}, true)
         if (result.code==200&&result.content && result.content.length > 0) {
            this.onSearchClick({detail:result.content[0].itemCode})
          } else {
            wx.showModal({
              title: '无效条码',
              content: '条码不存在,请检查!',
              showCancel: false
            })
          }
        } else {
          wx.showModal({
            title: '扫码失败',
            content: res.errMsg,
            showCancel: false
          })
        }
      },
      fail: (err) => {
        wx.showModal({
          title: '扫码失败',
          content: err.errMsg,
          showCancel: false
        })
      },
    })
  },
  onSearchCancel() {
    this.setData({ searchValue: '' })
  },
  onSearchClick(e) {
    this.setData({
      searchValue: e.detail,
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.selectComponent('#searchHistory').addSearchHist(e.detail)
    this.loadMore()
  },
  onClickHisKeyWord(e) {
    this.setData({
      searchValue: e.detail,
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.selectComponent('#search').setData({
      searchValue: e.detail
    })
    this.loadMore()
  },
  onSearchChange(e) {
    this.setData({ searchValue: e.detail })
  },
  onRefresh() {
    this.setData({
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.loadMore()
  },
  loadMore() {
    if (this.data.pages && this.data.pages < this.data.pageIndex + 1) {
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    let param = {
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        // warehouseId: this.data.warehouseInfo.id,
        keyword: this.data.searchValue
      }
    }
    App.getHttp()._post(this.data.url, param, true).then(res => {
      // console.log('itemList', res)
      this.setData({
        showList: this.data.showList.concat(res.recordList ? res.recordList : []),
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
      console.log('showList', this.data.showList)
    })
  },
  itemBtnClick(e) {
    let type = this.data.type
    if (type == 'invList' || type == 'invCount') {
      let index = e.currentTarget.dataset.value // 获取传入的参数
      wx.navigateTo({
        url: `/pages/ccs/inventory/productInventory/index?itemId=${this.data.showList[index].itemId}`,
        events: {
          // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
          fresh: (data) => {
            this.onRefresh()
          }
        }
      })
    } else {
      let index = e.currentTarget.dataset.value // 获取传入的参数
      console.log('e', e, e.currentTarget.dataset.value)
      let itemInfo = this.data.showList[index]
      this.setData({
        showPop: true,
        itemInfo: { ...itemInfo, qty: 0 }
      })
    }
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  submitBtnClick(e) {
    // 前端不存直接调接口
    console.log('e', e)
    const supInfo = wx.getStorageSync('supInfo')
    let itemInfo = e.detail
    console.log('itemInfo', itemInfo)
    if (!itemInfo.qty) {
      wx.showToast({
        title: '请输入数量',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    let param = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      "itemId": itemInfo.itemId || itemInfo.id,
      "itemCode": itemInfo.itemCode || itemInfo.code,
      "warehouseId": itemInfo.warehouseId,
      "qty": itemInfo.qty,
      "standType": this.data.standType // 5入库 6出库 4调拨
    }
    App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', param, true).then(res => {
      wx.showToast({
        title: '加入调整台成功',
        duration: 3000,
        icon: 'none'
      })
      this.setData({ showPop: false })
    })
  },
  transferBtnClick() {
    // wx.setStorageSync('warehouseListOne', this.data.warehouseList)
    wx.navigateTo({
      url: `/pages/ccs/inventory/inOutTransferTotal/index?active=0`,
    })
  },
  onPopClose() {
    this.setData({ showPop: false })
  },
  goBack() {
    wx.navigateBack({
      delta: 1
    })
  },
})