<!--pages/ccs/inventory/invSearchHis/index.wxml-->
<view class="root-layout" id="root-layout">
    <van-sticky class="top-layout" id="top-layout">
        <nav-bar title="搜索"/>
        <search showScanImg="{{showScanImg}}" showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{false}}" bind:onSearchTxtClick="onSearchClick" bind:onClickScan="onSearchScan" bind:onConfirm="onSearchClick" id="search"/>
    </van-sticky>
    <search-history wx:if="{{!showList.length}}" showHis="{{!showList.length}}" busCode="{{busCode}}" id="searchHistory" bind:onClickHisKeyWord="onClickHisKeyWord"/>
    <view class="scroll-container" >
        <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
            <view class="item-layout" wx:if="{{showList.length>0}}">
                <inventory-item wx:for="{{showList}}" wx:for-item="item" wx:key="index" type="{{type}}" class="list-item" data-value="{{index}}" item="{{item}}" bind:itemBtnClick="itemBtnClick"/>
            </view>
            <view class="m-t-64" wx:else>
                <no-product noneTxt="暂无数据" />
            </view>
        </listView>
    </view>
    <transfer-pop showPop="{{showPop}}" itemInfo="{{itemInfo}}" bind:submitBtnClick="submitBtnClick" bind:onPopClose="onPopClose"/>
</view>
