// pages/ccs/newCenter/newsList.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH: 500,
    active: '0',
    tabList: [],
    showList: [],
    searchValue: '',
    showPop: false,
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    // 暂存的list
    warehouseList: [],
    navTop: 24,
    navHeight: 44,
    standCount: undefined
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getCount()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onSearchCancel() {
    this.setData({ searchValue: '' })
    console.log('searchValue', this.data.searchValue)
  },
  onSearchChange(e) {
    this.setData({ searchValue: e.detail })
    console.log('searchValue', this.data.searchValue)
  },
  onSearchClick(e) {
    this.setData({
      searchValue: e.detail,
      // pageIndex: 1,
      // pages: 0,
      // showList: [],
    })
    // 跳转搜索页
    wx.navigateTo({
      url: `/pages/ccs/inventory/invSearchHis/index?type=invTransfer`,
    })
    // this.loadMore()
  },
  chooseTab(e) {
    console.log('warehouseInfo', e.detail)
    let warehouseInfo = e.detail
    this.setData({
      pageIndex: 1,
      pages: 0,
      showList: [],
      warehouseInfo,
    })
    this.loadMore()
  },
  onRefresh() {
    console.log('6666')
    this.setData({
      pageIndex: 1,
      pages: 0,
      showList: []
    })
    this.loadMore()
  },
  loadMore() {
    if (this.data.pages && this.data.pages < this.data.pageIndex + 1) {
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    let param = {
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        warehouseId: this.data.warehouseInfo.id,
        keyword: this.data.searchValue
      }
    }
    App.getHttp()._post('/api/psi/currentInv/myx/warehouse/itemInv', param, true).then(res => {
      console.log('itemList', res)
      this.setData({
        showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
    })
  },
  itemBtnClick(e) {
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let itemInfo = this.data.showList[index]
    console.log('itemInfo', itemInfo)
    this.setData({
      showPop: true,
      itemInfo: { ...itemInfo, qty: 0 }
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  submitBtnClick(e) {
    // let itemInfo = e.detail
    // let warehouseList = this.data.warehouseList
    // // 先判断有无同样仓库
    // let index = warehouseList.findIndex(item=>{
    //   return item.warehouseId == itemInfo.warehouseId
    // })
    // if(index > -1){
    //   // 判断仓库下有无同样商品
    //   warehouseList[index].itemList = warehouseList[index].itemList || [] 
    //   let itemList = warehouseList[index].itemList
    //   let index2 = itemList.findIndex(item=>{
    //     return item.itemId === itemInfo.itemId
    //   })
    //   if(index2 > -1){
    //     itemList[index2] = itemInfo
    //   }else{
    //     itemList.push(itemInfo)
    //   }
    // }else{
    //   warehouseList.push({
    //     warehouseId: itemInfo.warehouseId,
    //     warehouseName: itemInfo.warehouseName,
    //     itemList: [{...itemInfo}]
    //   })
    // }
    // 前端不存直接调接口
    const supInfo = wx.getStorageSync('supInfo')
    let itemInfo = e.detail
    console.log('itemInfo', itemInfo)
    if (!itemInfo.qty) {
      wx.showToast({
        title: '请输入数量',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    let param = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      "itemId": itemInfo.itemId,
      "itemCode": itemInfo.itemCode,
      "warehouseId": itemInfo.warehouseId,
      "qty": itemInfo.qty,
      "standType": 4 // 5入库 6出库 4调拨
    }
    App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', param, true).then(res => {
      wx.showToast({
        title: '加入调整台成功',
        duration: 3000,
        icon: 'none'
      })
      this.setData({ showPop: false })
      // 更新调整台数量
      this.getCount()
    })
  },
  transferBtnClick() {
    // wx.setStorageSync('warehouseListOne', this.data.warehouseList)
    wx.navigateTo({
      url: `/pages/ccs/inventory/transferTotal/index`,
    })
  },
  onPopClose() {
    this.setData({ showPop: false })
  },
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },
  getCount() {
    const supInfo = wx.getStorageSync('supInfo')
    let param = {
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "standType": 4
    }
    App.getHttp()._post('/api/psi/invCheckstand/getCount', param, true).then(res => {
      console.log('count',res)
      this.setData({
        standCount: res.content
      })
    })
  }


})
