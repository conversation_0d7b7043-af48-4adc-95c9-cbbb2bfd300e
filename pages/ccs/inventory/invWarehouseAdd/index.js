// pages/ccs/inventory/invWarehouseAdd/index.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        columns:[],
        showPop: false,
        name: '',
        code: '',
        warehouseProperty: '',
        state: 2,
        isDefault:2,
        warehousePropertyName: '',
        id: ''
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        console.log(wx.getStorageSync('dictMap'), options)
        this.setData({
            id: options.id,
            columns: wx.getStorageSync('dictMap').warehouseType.map(item=>{
                return {text: item.name, ...item}
            })
        })
        if(this.data.id){
            wx.setNavigationBarTitle({title: '编辑仓库'})
            this.getData()
        }else{
            wx.setNavigationBarTitle({title: '新增仓库'})
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    getData(){
        let param={
            "pageIndex": 1,
            "pageSize": 1,
            "param": {
                id: this.data.id,
            }
        }
        App.getHttp()._post(`/api/psi/baseWarehouse/page`, param, true).then(res => {
            let result = res.recordList[0]
            this.setData({
                name: result.name,
                code: result.code,
                warehouseProperty: result.warehouseProperty,
                state: result.state,
                isDefault: result.isDefault,
                warehousePropertyName: result.warehousePropertyName,
            })
        })
    },
    handleTypeClick(){
        this.setData({
            showPop: true,
        })
    },
    onSureBtnClick(){
        let param = {
            name: this.data.name,
            code: this.data.code,
            warehouseProperty: this.data.warehouseProperty,
            state: this.data.state,
            isDefault: this.data.isDefault,
            warehousePropertyName: this.data.warehousePropertyName,
            id: this.data.id
        }
        App.getHttp()._post(`/api/psi/baseWarehouse/myx/${this.data.id ? 'update' :'create'}`, param, true).then(res => {
            console.log('itemList', res)
            wx.showToast({
                title: this.data.id ? '保存成功' : '新增成功',
                duration: 3000,
                icon: 'none'
            })
            this.setData({
                id: res.content
            })
            //this.getData();
            wx.navigateBack({
                delta: 1
            })
        })
    },
    cancel(){
        this.setData({showPop: false})
    },
    confirm(event){
        const { picker, value, index } = event.detail;
        console.log(`当前值：${value}, 当前索引：${index}`);
        let warehousePropertyName = this.data.columns[index].name
        let warehouseProperty = this.data.columns[index].value
        this.setData({
            warehousePropertyName,
            warehouseProperty,
            showPop: false
        })
    },
    onInputChange(e){
        this.setData({
            name: e.detail
        })
    },
    onSwitchDefaultChange(e){
      this.setData({
        isDefault: e.detail
    })
    },
    onSwitchChange(e){
        this.setData({
            state: e.detail
        })
    }
})