<!--pages/ccs/inventory/invWarehouseAdd/index.wxml-->
<view>
  <van-cell-group>
    <van-field label="仓库名称" model:value="{{ name }}" placeholder="请输入" border="{{ false }}" bind:change="onInputChange" input-align="right" />
    <van-field label="仓库编码" disabled model:value="{{ code }}" placeholder="" border="{{ false }}" input-align="right" class="warehouseCode" />
    <van-field label="仓库类型" model:value="{{ warehousePropertyName }}" placeholder="请选择" border="{{ false }}" readonly is-link bindtap="handleTypeClick" input-align="right" />
    <van-field label="是否默认仓" readonly>
      <van-switch slot="right-icon" checked="{{ isDefault  }}" active-color="#00b9c3" active-value="{{2}}" inactive-value="{{1}}" bind:change="onSwitchDefaultChange" size="48rpx" />
    </van-field>
    <van-field label="是否有效" readonly>
      <van-switch slot="right-icon" checked="{{ state  }}" active-color="#00b9c3" active-value="{{2}}" inactive-value="{{1}}" bind:change="onSwitchChange" size="48rpx" />
    </van-field>
  </van-cell-group>
  <van-popup show="{{ showPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="cancel">
    <van-picker title="选择仓库类型" show-toolbar columns="{{ columns }}" bind:change="onChange" bind:confirm="confirm" bind:cancel="cancel" />
  </van-popup>
  <view class="btn-box">
    <van-button type="info" block round class="btn-submit" bindtap="onSureBtnClick">{{id ? '保存': '提交'}}</van-button>
  </view>
</view>