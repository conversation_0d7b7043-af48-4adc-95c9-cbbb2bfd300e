/* pages/ccs/inventory/invWarehouseAdd/index.wxss */
page{
    height: 100%;
    background: #F2F2F2;
}
.van-cell-group{
    margin-top: 24rpx;
}
.van-field__label {
    height: 48rpx !important;
    line-height: 48rpx !important;
    font-size: 32rpx !important;
    color: #242424 !important;
}
.van-field__body--text{
    color: #242424 !important;
}
.van-field__label--disabled{
    color: #242424 !important;
}
.van-field__control--disabled{
    color: #BDBDBD !important;
}
.van-cell__right-icon-wrap{
    margin-left: 16rpx !important;
}
.van-icon-arrow{
    margin-top: -12rpx;
    width: 40rpx;
    height: 40rpx;
}
.van-cell::before {
    content: '';
    position: absolute;
    top: 0;
    left: 32rpx;
    width: 100%;
    height: 100%;
    border-bottom: 2rpx solid #E6E6E6;
    box-sizing: border-box;
}
.btn-box{
    position: fixed;
    width: 100%;
    height: 112rpx;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    background: #fff;
}
.van-button{
    margin: 16rpx 24rpx 0;
    width: 702rpx !important;
    height: 80rpx !important;
    line-height: 80rpx !important;
    background: #00b9c3 !important;
    border-radius: 8rpx !important;
}