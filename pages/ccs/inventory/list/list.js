// pages/ccs/newCenter/newsList.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        listViewH:500,
        active: 0,
        tabList: [],
        showList:[],
        searchValue: '',
        pageIndex: 1,
        pageSize: 20,
        pages: 0,
        warehouseInfo:{}, // 当前tab信息
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
      this.initPage()
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      // this.initPage()
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    onSearchCancel(){
      this.setData({searchValue: ''})
    },
    onSearchScan(){
      // 跳转搜索页
      wx.navigateTo({
        url: `/pages/ccs/inventory/invSearchHis/index?type=invList&scan=2`,
      })
    },
    onSearchClick(e){
      this.setData({
        searchValue: e.detail,
        // pageIndex: 1,
        // pages: 0,
        // showList: [],
      })
      // 跳转搜索页
      wx.navigateTo({
        url: `/pages/ccs/inventory/invSearchHis/index?type=invList`,
      })
      // this.loadMore()
    },
    chooseTab(e) {
      console.log('warehouseInfo', e.detail)
      let warehouseInfo = e.detail
      this.setData({
        warehouseInfo,
        pageIndex: 1,
        pages: 0,
        showList: [],
      })
      this.loadMore()
    },
    onRefresh(){
      this.setData({
        showList: [],
        pageIndex: 1,
        pages: 0,
      })
      this.loadMore()
    },
    loadMore(){
      if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
        wx.showToast({
          title: '没有更多数据了...',
          duration: 3000,
          icon: 'none'
        })
        return;
      }
      console.log('warehouseInfo',this.data.warehouseInfo)
      let param ={
        "pageIndex": this.data.pageIndex,
        "pageSize": this.data.pageSize,
        "param": {
          warehouseId: this.data.warehouseInfo.id,
          keyword: this.data.searchValue
        }
      }
      console.log(param)
      App.getHttp()._post('/api/psi/currentInv/myx/warehouse/itemInv', param, true).then(res=>{
        console.log('itemList',res)
        this.setData({
          showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
          pageIndex: this.data.pageIndex + 1,
          pages: res.pages
        })
      })
    },
    itemBtnClick(e){
      let index = e.currentTarget.dataset.value // 获取传入的参数
      wx.navigateTo({
        url: `/pages/ccs/inventory/count/count?itemId=${this.data.showList[index].itemId}`,
      })
    },
    // getTabList(){
    //   console.log('jll')
    //   const param = {
    //     "pageIndex": 1,
    //     "pageSize": 999999,
    //     "param": {
    //       isUsable: 2,
    //     }
    //   }
    //   // 查询仓库
    //   App.getHttp()._post('/api/psi/baseWarehouse/page', param).then(res => {
    //     this.setData({
    //       active: 0,
    //       pageIndex: 1,
    //       tabList: res
    //     })
    //     // 查询仓库下商品
    //     this.loadMore()
        
    //   })
    // },
    initPage() {
      const query = wx.createSelectorQuery()
      query.select('#root-layout').boundingClientRect()
      query.select('#top-layout').boundingClientRect()
      query.exec((res) => {
        console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
        this.setData({
          listViewH: res[0].height - res[1].height,
        })
      })
    },
    

})
