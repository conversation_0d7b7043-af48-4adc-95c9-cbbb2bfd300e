<!--pages/ccs/inventory/list/list.wxml-->
<view class="page-layout root-layout" id="root-layout">
    <van-sticky offset-top="0" class="top-layout" id="top-layout">
        <search showScanImg showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{true}}" bind:onSearchTxtClick="onSearchClick" bind:onClickScan="onSearchScan" bind:onConfirm="onSearchClick" bind:onClickSearch="onSearchClick" />
        <warehouse-tab bind:chooseTab="chooseTab" />
    </van-sticky>
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
        <view class="item-layout" wx:if="{{showList.length>0}}">
            <inventory-item wx:for="{{showList}}" wx:key="index" class="list-item" class="list-item" data-value="{{index}}" item="{{item}}" bind:itemBtnClick="itemBtnClick" type="invList"/>
            <!-- <view wx:for="{{showList}}" wx:key="index" class="list-item" data-value="{{index}}">
                <view class="item-left-box">
                    <view class="list-item-left">
                        <van-image width="160rpx" height="160rpx" src="{{item.itemUrl}}" />
                    </view>
                    <view class="list-item-right">
                        <view class="title two-line-ellipsis">{{item.itemName}}</view>
                        <view class="item-btn-box">
                            <view class="item-num">
                                {{item.qtyAvi}}
                                <view class="item-num-text">可用库存</view>
                            </view>
                            <van-button plain round type="info" size="mini" bindtap="itemBtnClick" data-value="{{index}}">盘点</van-button>
                        </view>
                    </view>
                </view>
                <view class="item-stock" wx:for="{{item.warehouses}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" data-value="{{index2}}">
                        <view class="stock-name">{{item2.warehouseName}} </view>
                        <view class="stock-num">可用库存：{{item2.qtyAvi}}</view>
                    </view>
            </view> -->
        </view>
        <view class="m-t-25p" wx:else>
            <no-product noneTxt="暂无数据" />
        </view>
    </listView>
</view>