<!--pages/ccs/inventory/count/count.wxml-->
<view class="root-layout" id="root-layout">
    <van-sticky offset-top="0" class="top-layout" id="top-layout">
        <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{true}}" bind:onSearchTxtClick="onSearchClick" bind:onConfirm="onSearchClick" bind:onClickSearch="onSearchClick" showScanImg bind:onClickScan="onSearchScan" />
    </van-sticky>
    <view class="scroll-container">
        <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
            <view class="item-layout" wx:if="{{showList.length>0}}">
                <inventory-item wx:for="{{showList}}" wx:key="index" class="list-item" class="list-item" data-value="{{index}}" item="{{item}}" bind:itemBtnClick="itemBtnClick" type="invCount" />
            </view>
            <view class="m-t-25p" wx:else>
                <no-product noneTxt="暂无数据" />
            </view>
        </listView>
    </view>
</view>