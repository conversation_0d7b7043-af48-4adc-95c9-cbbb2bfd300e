<!--pages/ccs/inventory/transferTotal/index.wxml-->
<view>
  <!-- <view wx:for="{{stockList}}" wx:key="index1" wx:for-item="item1" wx:for-index="index1" class="stock-list-item" bindtap="itemClick" data-value="{{index1}}">
        <van-checkbox value="{{ item1.isChecked }}" checked-color="#00b9c3" data-value="{{index1}}" bind:change="onItemCheck">
            {{item1.warehouseName}}
        </van-checkbox>
        <van-checkbox-group value="{{ item1.result }}" data-value="{{index1}}" bind:change="onSecondItemCheck">
            <van-swipe-cell right-width="{{ 65 }}" left-width="{{ 0 }}" wx:for="{{item1.itemList}}" wx:key="index2" wx:for-index="index2" wx:for-item="item2" data-value1="{{index1}}" data-value2="{{index2}}">
                <vew class="swip-cell-box">
                    <van-checkbox name="{{item2.itemId}}">
                    </van-checkbox>
                    <view>
                        <view class="list-item">
                            <view class="list-item-left">
                                <van-image width="70" height="70" src="{{item2.itemUrl}}" />
                            </view>
                            <view class="list-item-right">
                                <view class="title">{{item2.itemName}}</view>
                                <view class="item-num">物料编码：{{item2.itemCode}}</view>
                            </view>
                        </view>
                        <view>
                            <van-cell title="库存：{{item2.qtyAvi}}">
                                <van-stepper slot="right-icon" value="{{ item2.billQty }}" bind:change="onStepperChange"  data-index1="{{index1}}" data-index2="{{index2}}"/>
                            </van-cell>
                        </view>
                    </view>
                </vew>
                <view slot="right" class="van-swipe-cell__right" bindtap="onDeleteClick" data-value1="{{index1}}" data-value2="{{index2}}">删除</view>
            </van-swipe-cell>
        </van-checkbox-group>
    </view> -->
  <view class="filter-layout flex justify-content-between font-main">
    <view class="flex" catchtap="onClickFilter">
      <view class="left"></view>
    </view>
    <view class="right" catchtap="onClickSet">{{showOpenSet?'取消':'编辑'}}</view>
  </view>
  <van-cell title="调入仓" is-link bind:click="showPopup" value="{{ toWarehouseName }}" custom-class="cell-class" />
  <warehouse-check-box stockList="{{stockList}}" bind:listChange="listChange" bind:onDeleteClick="onDeleteClick" showOpenSet="{{showOpenSet}}" />
  <view class="pop-box">
    <!-- <van-cell title="调入仓" is-link bind:click="showPopup" /> -->
    <van-popup show="{{ showPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="cancel">
      <van-picker title="" show-toolbar columns="{{ columns }}" bind:change="onPickerChange" bind:confirm="confirm" bind:cancel="cancel" />
    </van-popup>
  </view>
  <view class="all-check-box">
    <!-- <van-checkbox value="{{ allChecked }}" checked-color="#00b9c3" bind:change="onAllCheck">
            全选
        </van-checkbox> -->
    <view class="btn-tips">提示：按照仓库进行出库</view>
    <van-button wx:if="{{!showOpenSet}}" type="info" round custom-class="all-check-btn" size="small" bindtap="submitBtnClick">调拨</van-button>
    <van-button wx:else type="info" round custom-class="all-check-btn delete-btn" size="small" bindtap="deleteBtnClick">删除</van-button>
  </view>
</view>