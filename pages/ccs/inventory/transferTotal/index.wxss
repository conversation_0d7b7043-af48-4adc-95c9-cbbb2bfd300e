/* pages/ccs/inventory/transferTotal/index.wxss */
page {
    height: 100%;
    background-color: #F2F2F2;
}

.pop-box {
    height: 150rpx;
}

.all-check-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    height: 128rpx;
    display: flex;
    justify-content: space-between;
}

.btn-tips {
    color: #707070;
    font-size: 28rpx;
    line-height: 128rpx;
    margin-left: 32rpx;
}

.all-check-btn {
    padding: 0 32rpx !important;
    height: 64rpx !important;
    border-radius: 8rpx !important;
    background: #00b9c3 !important;
    font-size: 28rpx !important;
    margin-right: 32rpx !important;
    margin-top: 32rpx !important;
}
.delete-btn{
    background: #FF4A4D !important;
    border: none !important;
}

/* .van-swipe-cell__right {
    display: inline-block;
    background-color: #FF4A4D;
    width: 160rpx;
    color: #fff;
} */

.cell-class {
    height: 96rpx !important;
    line-height: 48rpx !important;
    padding: 24rpx 32rpx !important;
    color: #242424 !important;
    border-top: 2rpx solid #E6E6E6 !important;
}

.van-picker__cancel,
.van-picker__confirm {
    margin: 32rpx;
    font-size: 32rpx !important;
    color: #00b9c3 !important;
    line-height: 44rpx;
}

.van-picker-column__item {
    color: #1D2129 !important;
    font-size: 32rpx !important;
}

.filter-layout {
    padding: 24rpx 24rpx;
}

.filter-layout .right {
    color: #00b9c3;
    line-height: 32rpx;
}

.filter-layout .left {
    line-height: 32rpx;
}

.filter-layout .filter-icon {
    width: 32rpx;
    height: 32rpx;
}