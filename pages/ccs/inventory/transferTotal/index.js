// pages/ccs/inventory/transferTotal/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showList: [{}, {}],
    stockList: [
      //     {
      //     isChecked: false,
      //     result: [],
      //     items:[{isChecked: false, name: 'a'},{isChecked: false, name: 'b'}]
      // }
    ],
    columns: [],
    showPop: false,
    allChecked: false,
    toWarehouseId: '',
    toWarehouseName: '',
    showOpenSet: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getTabList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 查调整台列表
    this.getAllList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  getAllList() {
    const supInfo = wx.getStorageSync('supInfo')
    let param = {
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "standType": 4
    }
    App.getHttp()._post('/api/psi/invCheckstand/getInvList', param).then(res => {
      this.setData({
        stockList: res ? res.map(item => {
          item.items.map(item2 => {
            item2.billQty = item2.qty
            item2.standId = item2.id
            return item2;
          })
          return item
        }) : []
      })
    })
  },
  getTabList() {
    const param = {
      "pageIndex": 1,
      "pageSize": 999999,
      "param": {
        isUsable: 2,
        state: 2,
      }
    }
    // 查询仓库
    App.getHttp()._post('/api/psi/baseWarehouse/page', param).then(res => {
      let defaultToWarehouseId = ''
      let defaultToWarehouseName = ''
      const columns = res.map(item => {
        if (item.isDefault == 2) {
          defaultToWarehouseId = item.id
          defaultToWarehouseName = item.name
        }
        return { text: item.name, ...item }
      })
      this.setData({
        columns: columns,
        toWarehouseId: defaultToWarehouseId,
        toWarehouseName: defaultToWarehouseName,
      })
    })
  },
  // onItemCheck(e) {
  //     console.log(e)
  //     let isChecked = e.detail;
  //     let firstIndex = e.target.dataset.value;
  //     let stockList = this.data.stockList
  //     stockList[firstIndex].isChecked = isChecked
  //     stockList[firstIndex].result = isChecked ? stockList[firstIndex].items.map(item => {
  //         return item.itemId
  //     }) : []
  //     console.log(stockList[firstIndex].result)
  //     this.setData({
  //         stockList: stockList
  //     })
  // },
  // onSecondItemCheck(e) {
  //     let result = e.detail;
  //     let firstIndex = e.target.dataset.value;
  //     let stockList = this.data.stockList
  //     stockList[firstIndex].result = result
  //     if (result.length !== stockList[firstIndex].items.length) {
  //         stockList[firstIndex].isChecked = false
  //     } else if (result.length === stockList[firstIndex].items.length) {
  //         stockList[firstIndex].isChecked = true
  //     }
  //     console.log('stockList', stockList)
  //     this.setData({
  //         stockList: stockList
  //     })
  // },
  // toggle(index1, index2,) {
  //     const checkbox = this.selectComponent(`.checkboxes-${index1}-${index2}`);
  //     console.log('checkbox', checkbox)
  //     checkbox.toggle();
  // },
  showPopup() {
    this.setData({
      showPop: true
    })
  },
  // cancel() {
  //     this.setData({
  //         showPop: false
  //     })
  // },
  // onAllCheck(e) {
  //     this.setData({
  //         allChecked: e.detail
  //     })
  //     let stockList = this.data.stockList
  //     stockList.forEach((item, index) => {
  //         e.target.dataset.value = index;
  //         this.onItemCheck(e)
  //     })
  // },
  // onStepperChange(e) {
  //     console.log(e);
  //     let stockList = this.data.stockList
  //     let index1 = e.currentTarget.dataset['index1'];
  //     let index2 = e.currentTarget.dataset['index2'];
  //     stockList[index1].items[index2].billQty = e.detail
  //     console.log('stockList', stockList)
  //     this.setData({ stockList })
  // },
  onDeleteClick(e) {
    let index1 = e.detail.index1;
    let index2 = e.detail.index2;
    let stockList = this.data.stockList
    let item = stockList[index1].items[index2]
    const param = {
      ids: [item.id]
    }
    // 请求接口删除
    App.getHttp()._post('/api/psi/invCheckstand/delete', param).then(res => {
      stockList[index1].items.splice(index2, 1)
      if (!stockList[index1].items.length) {
        stockList.splice(index1, 1)
      }
      this.setData({ stockList })
      wx.showToast({
        title: '删除成功',
        duration: 3000,
        icon: 'none'
      })
    })

  },
  confirm(event) {
    this.setData({ showPop: false })
    const { picker, value, index } = event.detail;
    this.setData({
      toWarehouseId: this.data.columns[index].id,
      toWarehouseName: this.data.columns[index].text,
    })
  },
  cancel() {
    this.setData({ showPop: false })
  },
  onPickerChange(event) {

  },
  submitBtnClick(event) {
    // 找出点击的第一项
    let obj = this.data.stockList.find(item => {
      return item.result && item.result.length
    })
    if (!obj) {
      wx.showToast({
        title: '至少选择一项...',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    if (!this.data.toWarehouseId) {
      wx.showToast({
        title: '请选择仓库...',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    if (this.data.toWarehouseId == obj.warehouseId) {
      wx.showToast({
        title: '调入仓和调出仓不能一样',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    // 过滤出已选商品
    let items = obj.items.filter(item => {
      return obj.result.includes(item.itemId)
    })
    let param = {
      "warehouseId": obj.warehouseId,
      "toWarehouseId": this.data.toWarehouseId,
      "items": items
    }
    App.getHttp()._post('/api/psi/allocateHead/myx/submitByMyx', param, true).then(res => {
      // this.getAllList()
      wx.navigateTo({
        url: '/pages/ccs/inventory/success/index?query=' + JSON.stringify({
          title: '调拨成功',
          leftTxt: '返回调整台',
          rightTxt: '返回库存调拨'
        }),
      })
    })
  },
  listChange(e) {
    this.setData({
      stockList: e.detail
    })
  },
  onClickSet() {
    this.setData({ showOpenSet: !this.data.showOpenSet })
  },
  deleteBtnClick() {
    let ids = [];
    this.data.stockList.forEach(item => {
      item.items.forEach(item2 => {
        if (item.result.includes(item2.itemId)) {
          ids.push(item2.id)
        }
      })
    })
    if (!ids.length) {
      wx.showToast({
        title: '至少选择一项...',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    const param = {
      ids: ids
    }
    // 请求接口删除
    App.getHttp()._post('/api/psi/invCheckstand/delete', param).then(res => {
      wx.showToast({
        title: '删除成功',
        duration: 3000,
        icon: 'none'
      })
      this.getAllList();
    })
  }
})