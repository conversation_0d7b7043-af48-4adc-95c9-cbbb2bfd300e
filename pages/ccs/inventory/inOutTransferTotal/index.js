// pages/ccs/inventory/inOutTransferTotal/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showList: [{}, {}],
    stockList: [
      //     {
      //     isChecked: false,
      //     result: [],
      //     items:[{isChecked: false, name: 'a'},{isChecked: false, name: 'b'}]
      // }
    ],
    columns: [],
    showPop: false,
    allChecked: false,
    toWarehouseId: '',
    toWarehouseName: '',
    tabList: [{ name: '其他出库' }, { name: '其他入库' }],
    active: '',
    itemList: [],
    result: [],
    allChecked: false,
    goodsNum: 0,
    showOpenSet: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      active: Number(options.active)
    })
    this.getTabList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 查调整台列表
    this.getAllList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onTabChange(event) {
    if (this.data.active !== event.detail.name) {
      this.setData({
        active: event.detail.name
      })
      // 查调整台列表
      this.getAllList()
    }
  },
  getAllList() {
    if (this.data.active == 1) {
      const supInfo = wx.getStorageSync('supInfo')
      let param = {
        "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
        "standType": 5, // // 5入库 6出库 4调拨
      }
      App.getHttp()._post('/api/psi/invCheckstand/getInList', param).then(res => {
        this.setData({
          itemList: res ? res.map(item => {
            item.billQty = item.qty
            item.standId = item.id
            return item
          }) : [],
          goodsNum: res ? res.length : 0
        })
      })
    } else if (this.data.active === 0) {
      const supInfo = wx.getStorageSync('supInfo')
      let param = {
        "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
        "standType": 6
      }
      App.getHttp()._post('/api/psi/invCheckstand/getInvList', param).then(res => {
        res = res || []
        let goodsNum = 0
        this.setData({
          stockList: res ? res.map(item => {
            goodsNum += item.items.length
            item.items.map(item2 => {
              item2.billQty = item2.qty
              item2.standId = item2.id
              return item2;
            })
            return item
          }) : []
        })
        this.setData({ goodsNum })
      })
    }
  },
  getTabList() {
    const param = {
      "pageIndex": 1,
      "pageSize": 999999,
      "param": {
        isUsable: 2,
        state: 2,
      }
    }
    // 查询仓库
    App.getHttp()._post('/api/psi/baseWarehouse/page', param).then(res => {
      let defaultToWarehouseId = ''
      let defaultToWarehouseName = ''
      const columns = res.map(item => {
        if (item.isDefault == 2) {
          defaultToWarehouseId = item.id
          defaultToWarehouseName = item.name
        }
        return { text: item.name, ...item }
      })
      this.setData({
        columns: columns,
        toWarehouseId: defaultToWarehouseId,
        toWarehouseName: defaultToWarehouseName,
      })
    })
  },
  // onItemCheck(e) {
  //     console.log(e)
  //     let isChecked = e.detail;
  //     let firstIndex = e.target.dataset.value;
  //     let stockList = this.data.stockList
  //     stockList[firstIndex].isChecked = isChecked
  //     stockList[firstIndex].result = isChecked ? stockList[firstIndex].items.map(item => {
  //         return item.itemId
  //     }) : []
  //     console.log(stockList[firstIndex].result)
  //     this.setData({
  //         stockList: stockList
  //     })
  // },
  onItemCheck(e) {
    let result = e.detail;
    this.setData({
      result: result
    })
    this.setData({
      allChecked: this.data.result.length == this.data.itemList.length
    })
  },
  // toggle(index1, index,) {
  //     const checkbox = this.selectComponent(`.checkboxes-${index1}-${index}`);
  //     console.log('checkbox', checkbox)
  //     checkbox.toggle();
  // },
  showPopup() {
    this.setData({
      showPop: true
    })
  },
  cancel() {
    this.setData({
      showPop: false
    })
  },
  onAllCheck(e) {
    let isAllCheck = e.detail
    this.setData({
      allChecked: e.detail
    })
    this.setData({
      result: isAllCheck ? this.data.itemList.map((item, index) => {
        return item.id;
      }) : []
    })
  },
  onStepperChange(e) {
    let itemList = this.data.itemList
    let index = e.currentTarget.dataset['index'];
    itemList[index].billQty = e.detail
    this.setData({ itemList })
  },
  onDeleteClick(e) {
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: () => {
        if (this.data.active == 1) {
          let index = e.currentTarget.dataset.value;
          let itemList = this.data.itemList
          let item = itemList[index]
          const param = {
            ids: [item.id]
          }
          // 请求接口删除
          App.getHttp()._post('/api/psi/invCheckstand/delete', param).then(res => {
            itemList.splice(index, 1)
            this.setData({ itemList })
            wx.showToast({
              title: '删除成功',
              duration: 3000,
              icon: 'none'
            })
          })
        } else {
          let index1 = e.detail.index1;
          let index2 = e.detail.index2;
          let stockList = this.data.stockList
          let item = stockList[index1].items[index2]
          const param = {
            ids: [item.id]
          }
          // 请求接口删除
          App.getHttp()._post('/api/psi/invCheckstand/delete', param).then(res => {
            stockList[index1].items.splice(index2, 1)
            if (!stockList[index1].items.length) {
              stockList.splice(index1, 1)
            }
            this.setData({ stockList })
            wx.showToast({
              title: '删除成功',
              duration: 3000,
              icon: 'none'
            })
          })
        }
        this.setData({
          goodsNum: this.data.goodsNum - 1
        })
      }
    })
  },
  confirm(event) {
    this.setData({ showPop: false })
    const { picker, value, index } = event.detail;
    this.setData({
      toWarehouseId: this.data.columns[index].id,
      toWarehouseName: this.data.columns[index].text,
    })
  },
  submitBtnClick() {
    if (this.data.active == 1) {
      if (!this.data.result.length) {
        wx.showToast({
          title: '请选择至少一个商品',
          duration: 3000,
          icon: 'none'
        })
        return
      }
      if (!this.data.toWarehouseId) {
        wx.showToast({
          title: '请选择仓库...',
          duration: 3000,
          icon: 'none'
        })
        return
      }

      let param = {
        "warehouseId": this.data.toWarehouseId,
        "items": this.data.itemList.filter(item => {
          return this.data.result.includes(item.id)
        })
      }
      App.getHttp()._post('/api/psi/otherInBill/myx/submitByMyx', param, true).then(res => {
        // this.getAllList()
        wx.navigateTo({
          url: '/pages/ccs/inventory/success/index?query=' + JSON.stringify({
            title: '入库成功',
            leftTxt: '返回调整台',
            rightTxt: '返回其他入库'
          }),
        })
      })
    } else {
      // 其他出库
      // 找出点击的第一项
      let obj = this.data.stockList.find(item => {
        return item.result && item.result.length
      })
      if (!obj) {
        wx.showToast({
          title: '至少选择一项...',
          duration: 3000,
          icon: 'none'
        })
        return
      }
      // 过滤出已选商品
      let items = obj.items.filter(item => {
        return obj.result.includes(item.itemId)
      })
      let param = {
        "warehouseId": obj.warehouseId,
        // "toWarehouseId": this.data.toWarehouseId,
        "items": items
      }
      App.getHttp()._post('/api/psi/otherOutBill/myx/submitByMyx', param, true).then(res => {
        // this.getAllList()
        wx.navigateTo({
          url: '/pages/ccs/inventory/success/index?query=' + JSON.stringify({
            title: '出库成功',
            leftTxt: '返回调整台',
            rightTxt: '返回其他出库'
          }),
        })
      })
    }

  },
  listChange(e) {
    this.setData({
      stockList: e.detail
    })
  },
  onClickSet() {
    this.setData({ showOpenSet: !this.data.showOpenSet })
  },
  deleteBtnClick() {
    let ids = [];
    this.data.stockList.forEach(item => {
      item.items.forEach(item2 => {
        if (item.result.includes(item2.itemId)) {
          ids.push(item2.id)
        }
      })
    })
    if (!ids.length) {
      wx.showToast({
        title: '至少选择一项...',
        duration: 3000,
        icon: 'none'
      })
      return
    }
    const param = {
      ids: ids
    }
    // 请求接口删除
    App.getHttp()._post('/api/psi/invCheckstand/delete', param).then(res => {
      wx.showToast({
        title: '删除成功',
        duration: 3000,
        icon: 'none'
      })
      this.getAllList();
    })
  },
  deleteBtnClick1() {
    let itemList = this.data.itemList
    const param = {
      ids: this.data.result
    }
    // 请求接口删除
    App.getHttp()._post('/api/psi/invCheckstand/delete', param).then(res => {
      wx.showToast({
        title: '删除成功',
        duration: 3000,
        icon: 'none'
      })
      this.getAllList()
    })
  }
})