<!--pages/ccs/inventory/inOutTransferTotal/index.wxml-->
<view>
  <van-tabs active="{{ active }}" bind:change="onTabChange">
    <van-tab wx:for="{{tabList}}" wx:key="index" title="{{item.name}}" name="{{index}}" data-value="{{index}}">
    </van-tab>
  </van-tabs>
  <!-- <view class="goods-num-box">
        <view>共{{goodsNum}}件商品</view>
        <view>编辑</view>
    </view> -->
  <view class="filter-layout flex justify-content-between font-main">
    <view class="flex" catchtap="onClickFilter">
      <view class="left"></view>
    </view>
    <view class="right" catchtap="onClickSet">{{showOpenSet?'取消':'编辑'}}</view>
  </view>
  <van-cell wx:if="{{active == 1}}" title="调入仓" is-link bind:click="showPopup" value="{{ toWarehouseName }}" custom-class="cell-class" />
  <view class="checkbox-group-box" wx:if="{{active==1}}">
    <van-checkbox-group value="{{ result }}" bind:change="onItemCheck" wx:if="{{active==1}}" size="36rpx">
      <view right-width="{{ 65 }}" left-width="{{ 0 }}" wx:for="{{itemList}}" wx:key="index" wx:for-index="index" wx:for-item="item" data-value="{{index}}">
        <vew class="swip-cell-box  active1-swip-cell">
          <van-checkbox name="{{item.id}}" icon-size="36rpx" custom-class="checkbox-class" checked-color="#00b9c3"/>
          <view class="list-item-box">
            <van-swipe-cell right-width="{{ 65 }}" left-width="{{ 0 }}">
              <view class="list-item">
                <view class="list-item-left">
                  <van-image width="160rpx" height="160rpx" src="{{item.itemUrl}}" fit="contain" custom-class="img-custom-class" />
                </view>
                <view class="list-item-right">
                  <view class="title two-line-ellipsis">{{item.itemName}}</view>
                  <view class="specs single-line-ellipsis">{{item.specs}}</view>
                  <view class="item-num">商品编码：{{item.itemCode}}</view>
                  <view class="num-box">
                    <view class="item-num"></view>
                    <van-stepper slot="right-icon" min="0" value="{{ item.billQty }}" bind:change="onStepperChange" data-index="{{index}}" button-size="56rpx" />
                  </view>
                </view>
              </view>
              <view slot="right" class="item-delete" bindtap="onDeleteClick" data-value="{{index}}">删除</view>
            </van-swipe-cell>
          </view>
        </vew>
      </view>
    </van-checkbox-group>
  </view>
  <warehouse-check-box stockList="{{stockList}}" bind:listChange="listChange" bind:onDeleteClick="onDeleteClick" wx:if="{{active==0}}" showOpenSet="{{showOpenSet}}" />
  <view class="pop-box">
    <van-popup show="{{ showPop }}" position="bottom" custom-style="height: 60%;" custom-class="pop-calss" bind:click-overlay="cancel">
      <van-picker title="" show-toolbar columns="{{ columns }}" bind:change="onPickerChange" bind:confirm="confirm" bind:cancel="cancel" />
    </van-popup>
  </view>
  <view class="all-check-box">
    <view class="checkbox-all">
      <van-checkbox value="{{ allChecked }}" checked-color="#00b9c3" bind:change="onAllCheck" wx:if="{{active == 1}}" icon-size="36rpx" label-class="label-all">
        全选
      </van-checkbox>
      <view wx:else class="tips">提示：按照仓库进行出库</view>
    </view>
    <!-- 出库不需要选择仓库 -->
    <van-button wx:if="{{!showOpenSet && active === 0}}" type="info" round custom-class="all-check-btn" size="small" bindtap="submitBtnClick">出库</van-button>
    <!-- 入库需要选择仓库 -->
    <van-button wx:if="{{!showOpenSet && active === 1}}" type="info" round custom-class="all-check-btn" size="small" bindtap="submitBtnClick">入库</van-button>
    <van-button wx:if="{{showOpenSet && active === 0}}" type="info" round custom-class="all-check-btn delete-btn" size="small" bindtap="deleteBtnClick">删除</van-button>
    <van-button wx:if="{{showOpenSet && active === 1}}" type="info" round custom-class="all-check-btn delete-btn" size="small" bindtap="deleteBtnClick1">删除</van-button>
  </view>
</view>