/* pages/ccs/inventory/inOutTransferTotal/index.wxss */
page{
    background-color: #F2F2F2;
}
.all-check-box{
    position: fixed;
    display: flex;
    justify-content: space-between;
    left: 0;
    right: 0;
    bottom: 0;
    height: 128rpx;
    background:#fff;
}
.all-check-btn{
    margin-top: 32rpx;
    margin-right: 24rpx;
    height: 64rpx !important;
    padding: 0 32rpx !important;
    border-radius: 8rpx !important;
    font-size: 28rpx !important;
}
.tips{
    font-size: 28rpx;
    margin-left: 32rpx;
    line-height: 34rpx;
    color: #707070;
}

.cell-class{
    height: 96rpx !important;
    line-height: 48rpx !important;
    padding: 24rpx 32rpx !important;
    margin-top: 24rpx!important;
    color: #242424 !important;
}
.checkbox-group-box{
    padding: 24rpx 0 24rpx 32rpx;
    background: #fff;
}
.active1-swip-cell{
    overflow: hidden;
    height: 240rpx;
    display: flex;
}
.active1-swip-cell .list-item{
    display: flex;
    height: 240rpx;
    flex: 1;
}
.active1-swip-cell .img-custom-class{
    margin-top: 24rpx;
}
.checkbox-class{
    margin-top: 86rpx;
    width: 68rpx;
}
.active1-swip-cell .list-item-right{
    margin-left: 32rpx;
    margin-right: 24rpx;
    flex: 1;
}
.active1-swip-cell .title{
    margin-top: 4rpx;
    max-height: 72rpx;
    line-height: 36rpx;
    font-size: 28rpx;
    color: #242424;
}
.active1-swip-cell .specs{
  line-height: 36rpx;
  font-size: 26rpx;
  color: #8A8A8A;
}
.active1-swip-cell .item-num{
    font-size: 24rpx;
    color: #707070;
    line-height: 56rpx;
}
.active1-swip-cell .num-box{
    display: flex;
    justify-content: space-between;
}
.list-item-box{
    flex: 1;
}
.checkbox-all{
    padding: 48rpx 0 0 24rpx;
}
.label-all{
    color: #242424;
    font-size: 28rpx;
    line-height: 34rpx;
}
.van-picker__cancel, .van-picker__confirm {
    margin: 32rpx;
    font-size: 32rpx !important;
    color: #00b9c3!important;
    line-height: 44rpx;
}
.van-picker-column__item {
    color: #1D2129 !important;
    font-size: 32rpx !important;
}
/* .van-swipe-cell__right{
    background: #FF4A4D;
} */
.item-delete{
    margin-top: 24rpx;
    text-align: center;
    height: 192rpx !important;
    line-height: 192rpx;
    width: 160rpx;
    color: #fff;
    font-size: 28rpx !important;
    background: #FF4A4D;
}
.delete-btn{
    background: #FF4A4D !important;
    border: none !important;
}

.filter-layout {
    padding: 24rpx 24rpx 0;
}

.filter-layout .right {
    color: #00b9c3;
    line-height: 32rpx;
}

.filter-layout .left {
    line-height: 32rpx;
}

.filter-layout .filter-icon {
    width: 32rpx;
    height: 32rpx;
}
.item-num{
    color: #707070;
    font-size: 24rpx;
}
