// pages/ccs/inventory/otherInBill/index.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        listViewH:500,
        active: '0',
        tabList: [],
        showList:[],
        searchValue: '',
        showPop: false,
        pageIndex: 1,
        pageSize: 20,
        pages: 0,
        // 暂存的list
        warehouseList: [],
        navTop: 24,
        navHeight: 44,
        standCount: undefined
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.loadMore()
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
      this.initPage()
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      this.getCount()
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    onSearchCancel(){
      this.setData({searchValue: ''})
    },
    onSearchScan(){
      wx.navigateTo({
        url: `/pages/ccs/inventory/invSearchHis/index?type=otherInBill&scan=2`,
      })
    },
    onSearchClick(e){
      this.setData({
        searchValue: e.detail,
        // pageIndex: 1,
        // pages: 0,
        // showList: [],
      })
      // 跳转搜索页
      wx.navigateTo({
        url: `/pages/ccs/inventory/invSearchHis/index?type=otherInBill`,
      })
      // this.loadMore()
    },
    onSearchChange(e){
      this.setData({searchValue: e.detail})
      console.log('searchValue', this.data.searchValue)
    },
    chooseTab(e) {
      console.log('warehouseInfo', e.detail)
      let warehouseInfo = e.detail
      this.setData({
        pageIndex: 1,
        pages: 0,
        showList: [],
        warehouseInfo,
      })
      this.loadMore(warehouseInfo.id)
    },
    onRefresh(){
      console.log('6666')
      this.setData({
        pageIndex: 1,
        pages: 0,
        showList: []
      })
      this.loadMore();
    },
    loadMore(){
      if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
        wx.showToast({
          title: '没有更多数据了...',
          duration: 3000,
          icon: 'none'
        })
        return;
      }
      let param ={
        "pageIndex": this.data.pageIndex,
        "pageSize": this.data.pageSize,
        "param": {
          keyword: this.data.searchValue
        }
      }
      App.getHttp()._post('/api/mmd/common/item/page', param, true).then(res=>{
        console.log('itemList',res)
        this.setData({
          showList: this.data.showList.concat(res.recordList),
          pageIndex: this.data.pageIndex + 1,
          pages: res.pages
        })
      })
    },
    itemBtnClick(e){
      let index = e.currentTarget.dataset.value // 获取传入的参数
      let itemInfo = this.data.showList[index]
      this.setData({
        showPop: true,
        itemInfo: {...itemInfo, qty: 0}
      })
    },
    initPage() {
      // const rect = wx.getMenuButtonBoundingClientRect()
      // this.setData({
      //   clientRectTop: rect.top,
      //   clientRectBottom: (rect.bottom + 10), //10补偿余量
      //   clientRectWidth: (rect.width + 12), //12补偿间隙
      //   clientRectHeight: (rect.height)
      // })
      const query = wx.createSelectorQuery()
      query.select('.page-layout').boundingClientRect()
      query.select('.top-layout').boundingClientRect()
      query.exec((res) => {
        console.log('listViewH', res[0].height - res[1].height)
        this.setData({
          listViewH: res[0].height - res[1].height,
        })
      })
    },
    submitBtnClick(e){
      // 前端不存直接调接口
      const supInfo = wx.getStorageSync('supInfo')
      let itemInfo = e.detail
      console.log('itemInfo', itemInfo)
      if(!itemInfo.qty){
        wx.showToast({
          title: '请输入数量',
          duration: 3000,
          icon: 'none'
        })
        return
      }
      let param = {
        invoiceSetsOfBooksId: supInfo.setsOfBooksId,
        "itemId": itemInfo.id,
        "itemCode": itemInfo.code,
        "qty": itemInfo.qty,
        "standType": 5 // 5入库 6出库 4调拨
      }
      App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', param, true).then(res=>{
        wx.showToast({
          title: '加入调整台成功',
          duration: 3000,
          icon: 'none'
        })
        this.setData({showPop: false})
        // 更新调整台数量
        this.getCount()
      })
    },
    transferBtnClick(){
      // wx.setStorageSync('warehouseListOne', this.data.warehouseList)
      wx.navigateTo({
        url: `/pages/ccs/inventory/inOutTransferTotal/index?active=1`,
      })
    },
    onPopClose(){
      this.setData({showPop: false})
    },
    goBack(){
      wx.navigateBack({
        delta: 1
      })
    },
    getCount() {
      const supInfo = wx.getStorageSync('supInfo')
      let param = {
        "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
        "standType": 5
      }
      App.getHttp()._post('/api/psi/invCheckstand/getCount', param, true).then(res => {
        console.log('count',res)
        this.setData({
          standCount: res.content
        })
      })
    }
})