<!--pages/ccs/inventory/invOutBill/index.wxml-->
<view class="root-layout" id="root-layout">
    <van-sticky class="top-layout" id="top-layout">
        <nav-bar title="其他出库" imgTxt="调整台" imgUrl="{{'/asset/imgs/adjustment.png'}}" info="{{standCount}}" bind:navImgClick="transferBtnClick"/>
        <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{true}}" bind:onSearchTxtClick="onSearchClick" bind:onConfirm="onSearchClick" bind:onClickSearch="onSearchClick" showScanImg bind:onClickScan="onSearchScan"/>
        <warehouse-tab bind:chooseTab="chooseTab" customNav="{{true}}"/>
    </van-sticky>
    <view class="scroll-container">
        <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
            <view class="item-layout" wx:if="{{showList.length>0}}">
                <!-- <view wx:for="{{showList}}" wx:key="index" class="list-item" data-value="{{index}}">
                    <view class="list-item-left">
                        <van-image width="70" height="70" src="{{item.itemUrl}}" />
                    </view>
                    <view class="list-item-right">
                        <view class="title">{{item.itemName}}</view>
                        <view class="item-num">可用库存：{{item.qtyAvi}}</view>
                        <view class="item-btn">
                            <van-button plain round type="info" size="mini" bindtap="itemBtnClick">加入调整台</van-button>
                        </view>
                    </view>
                </view> -->
                <inventory-item wx:for="{{showList}}" wx:for-item="item" wx:key="index" class="list-item" data-value="{{index}}" item="{{item}}" bind:itemBtnClick="itemBtnClick"/>
            </view>
            <view class="m-t-25p" wx:else>
                <no-product noneTxt="暂无数据" />
            </view>
        </listView>
    </view>
    <!-- <van-popup show="{{ showPop }}" bind:close="onClose" closeable position="bottom">
        <view class="pop-title">加入调整台</view>
        <view class="list-item-left">
            <van-image width="70" height="70" src="https://img.yzcdn.cn/vant/cat.jpeg" />
        </view>
        <view class="list-item-right">
            <view class="title">新一级能效美的 酷金/1.5匹变频空调家用</view>
            <view class="item-num">物料编码：10086</view>
            <view class="item-btn">
                <van-button plain round type="info" size="mini">加入调整台</van-button>
            </view>
        </view>
        <van-cell title="入库单价" value="{{'￥999'}}" />
        <van-cell title="数量">
            <van-stepper slot="right-icon" value="{{ 1 }}" bind:change="onChange" />
        </van-cell>
        <van-field label="备注" value="{{ value }}" placeholder="请输入" border="{{ false }}" bind:change="onChange" />
        <van-button type="primary" block round bindtap="submitBtnClick">提交</van-button>
    </van-popup> -->
    <transfer-pop showPop="{{showPop}}" itemInfo="{{itemInfo}}" bind:submitBtnClick="submitBtnClick" bind:onPopClose="onPopClose"/>
</view>