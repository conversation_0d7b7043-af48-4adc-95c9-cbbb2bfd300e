/* pages/ccs/inventory/productInventory/index.wxss */
page{
    height: 100%;
    background-color: #F1F2F2;
}
.list-item-box{
    overflow: hidden;
    background: #fff;
}
.list-item{
    margin: 24rpx 24rpx 24rpx;
    height: 248rpx;
}
.item-left-box{
    display: flex;
}
.list-item-left{
    margin: 24rpx 32rpx 0 24rpx;
    overflow: hidden;
}
.list-item-right{
    flex: 1;
}
.title{
    margin-top: 10rpx;
    font-weight: 500;
    font-size: 30rpx;
    color: #242424;
    line-height: 36rpx;
    height: 72rpx;
}
.specs{
  margin-top: 4rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: #8A8A8A;
  line-height: 36rpx;
}
.two-line-ellipsis {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* qutoprefixer: off */
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap:break-word;
    word-break: break-all;
    white-space: normal;
  }
.item-stock{
    display: flex;
    justify-content: space-between;
}
.stock-name{
    font-size: 28rpx;
    color:#666;
}
.stock-num{
    font-size: 28rpx;
    color: #999;
}

.item-num{
    font-size: 24rpx;
    color: #666;
}
.top{
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: red;
}
.item-btn-box{
    margin-top: 48rpx;
    display: flex;
    justify-content: space-between;
}
.item-num{
    font-size: 32rpx;
    color: #242424;
    height: 64rpx;
    line-height: 64rpx;
    font-weight: 500;
}
.item-num-text{
    display: inline-block;
    margin-left: 8rpx;
    font-size: 28rpx;
    color: #707070;
    font-weight: 400;
}
.stepper-list-box{
    margin-top: 24rpx;
    background: #fff;
}
.stepper-box{
    position: relative;
    display: flex;
    justify-content: space-between;
    line-height: 48rpx;
    background: #fff;
}
.item-qtyAvi{
    font-size: 28rpx;
    line-height: 48rpx;
    color: #242424;
}
.item-qtyAvi-txt{
    color: #707070;
}
.stepper-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 32rpx;
    width: 100%;
    height: 100%;
    border-bottom: 2rpx solid #E6E6E6;
    box-sizing: border-box;
}
.stepper-item-left{
    margin-left: 32rpx;
    font-size: 32rpx;
    color: #242424;
}
.stepper-item-right{
    margin-right: 24rpx;
    margin-top: 18rpx;
}
.btn-submit{
    position: fixed;
    width: 96%;
    bottom: 10rpx;
    left: 0;
    right: 0;
    margin: auto;
}