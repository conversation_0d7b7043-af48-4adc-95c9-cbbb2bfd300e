// pages/ccs/inventory/productInventory/index.js
const App = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    itemId: '',
    itemInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('options', options)
    this.setData({
      itemId: options.itemId
    })
    this.getData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  getData() {
    let param = {
      "pageIndex": 1,
      "pageSize": 20,
      "param": {
        itemId: this.data.itemId,
      }
    }
    App.getHttp()._post('/api/psi/currentInv/myx/item/warehouseInv', param, true).then(res => {
      console.log('itemList', res)
      let itemInfo = res.recordList[0]
      itemInfo.warehouses = itemInfo.warehouses.map(item => {
        return { ...item, checkQty: item.qtyAvi }
      })
      this.setData({
        itemInfo: itemInfo,
      })
    })
  },
  onStepperChange(e) {
    console.log(e);
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let itemInfo = this.data.itemInfo
    itemInfo.warehouses[index].checkQty = e.detail
    this.setData({
      itemInfo: itemInfo,
    })
  },
  submitBtnClick() {
    console.log(this.data.itemInfo)
    let itemInfo = this.data.itemInfo
    let params = {
      "itemId": itemInfo.itemId,
      "itemCode": itemInfo.itemCode,
      "itemName": itemInfo.itemName,
      "lines": itemInfo.warehouses.map(item => {
        return { warehouseId: item.warehouseId, checkQty: item.checkQty }
      })
    }
    App.getHttp()._post('/api/psi/whCheck/submitByMyx', params, true).then(res => {
      //关闭page前, 给组件回调值
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('fresh');
      wx.showToast({
        title: '提交成功',
        duration: 3000,
        icon: 'none'
      })
      wx.navigateBack({
        delta: 1
      })
    })
  }
})