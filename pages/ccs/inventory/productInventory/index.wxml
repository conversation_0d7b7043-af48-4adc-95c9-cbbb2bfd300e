<!--pages/ccs/inventory/productInventory/index.wxml-->
<view>
    <view class="list-item-box">
        <view class="list-item">
            <view class="item-left-box">
                <view class="list-item-left">
                    <van-image width="160rpx" height="160rpx" fit="contain" src="{{itemInfo.itemUrl}}" />
                </view>
                <view class="list-item-right">
                    <view class="title two-line-ellipsis">{{itemInfo.itemName}}</view>
                    <view class="specs single-line-ellipsis">{{itemInfo.specs}}</view>
                    <view class="item-btn-box">
                        <view class="item-num">
                            {{itemInfo.totalQty}}
                            <view class="item-num-text">可用库存</view>
                        </view>
                        <view></view>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="stepper-list-box">
        <view wx:for="{{itemInfo.warehouses}}" wx:key="index" class="stepper-box" bindtap="itemClick" data-value="{{index}}">
            <view class="stepper-item-left">
                <view>{{item.warehouseName}}</view>
                <view class="item-qtyAvi"><text class="item-qtyAvi-txt">可用库存：</text>{{item.qtyAvi}}</view>
            </view>
            <view class="stepper-item-right">
                <van-stepper value="{{ item.checkQty }}" bind:change="onStepperChange" min="0" data-value="{{index}}" button-size="56rpx"/>
            </view>
        </view>
    </view>
    <van-button type="info" block round class="btn-submit" bindtap="submitBtnClick">提交</van-button>
</view>