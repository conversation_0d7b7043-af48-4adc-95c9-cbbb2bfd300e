/* pages/ccs/inventory/invWarehouse/index.wxss */
page {
    height: 100%;
}
.root-layout{
    height: 100vh;
}
.list-box{
    height: 100%;
    background: #F2F2F2;
}
.list-item{
    margin: 24rpx 24rpx 0;
    padding: 0 30rpx 0 24rpx;
    height: 148rpx;
    display: flex;
    background: #fff;
    justify-content: space-between;
}
.item-text-box{
    padding-top: 24rpx;
}
.item-text{
    display: inline-block;
    height: 40rpx;
    line-height: 40rpx;
    color: #242424;
    font-size: 30rpx;
    font-weight: 500;
}
.item-tag{
    display: inline-block;
    padding: 0 12rpx;
    margin-left: 16rpx;
    height: 36rpx;
    line-height: 36rpx;
    font-size: 24rpx;
    color: #FAAE16;
    background: #FFFBE6;
    border-radius: 8rpx;
    border: 2rpx solid #FAAE16;
}
.invalid{
    color: #73777C;
    background: #F6F6F6;
    border: 2rpx solid #DCDDDE;
}
.default{
  color: #ffffff;
  background: #5dc3ca;
  border: 2rpx solid #00b9c3;
}
.item-code{
    margin-top: 16rpx;
    height: 44rpx;
    line-height: 44rpx;
    font-size: 28rpx;
    color: #707070;
}
.icon-box{
    margin-top: 56rpx;
}
.btn-box{
    position: fixed;
    width: 100%;
    height: 112rpx;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    background: #fff;
}
.van-button{
    margin: 16rpx 24rpx 0;
    width: 702rpx !important;
    height: 80rpx !important;
    line-height: 80rpx !important;
    background: #00b9c3 !important;
    border-radius: 8rpx !important;
}
