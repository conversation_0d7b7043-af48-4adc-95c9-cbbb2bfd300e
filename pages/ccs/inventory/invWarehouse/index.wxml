<!--pages/ccs/inventory/invWarehouse/index.wxml-->
<view class="list-box root-layout" id="root-layout">
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
    <view class="item-layout" wx:if="{{showList.length>0}}">
      <view wx:for="{{showList}}" wx:key="index" class="list-item" data-value="{{index}}">
        <view class="item-text-box">
          <view class="item-text-tag">
            <view class="item-text">{{item.name}}</view>
            <view class="item-tag">{{item.warehousePropertyName}}</view>
            <view wx:if="{{item.state == 1}}" class="item-tag invalid">已失效</view>
            <view wx:if="{{item.isDefault == 2}}" class="item-tag default">默认仓</view>
          </view>
          <view class="item-code">仓库编码：{{item.code}}</view>
        </view>
        <view class="icon-box">
          <van-icon name="/asset/svgs/editor.svg" bindtap="onEditClick" data-id="{{item.id}}" size="40rpx" />
        </view>
      </view>
    </view>
  </listView>
  <view class="btn-box">
    <van-button type="primary" block round class="btn-submit" bindtap="createBtnClick">新增仓库</van-button>
  </view>
</view>