// pages/ccs/inventory/invWarehouse/index.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        showList: [],
        listViewH: 300,
        pageIndex: 1,
        pageSize: 20,
        pages: 0,
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.initPage()
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        this.onRefresh()
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    onRefresh() {
        this.setData({
            showList: [],
            pageIndex: 1,
            pages: 0,
        })
        this.loadMore()
    },
    loadMore() {
        if (this.data.pages && this.data.pages < this.data.pageIndex + 1) {
            wx.showToast({
                title: '没有更多数据了...',
                duration: 3000,
                icon: 'none'
            })
            return;
        }
        let param = {
            "pageIndex": this.data.pageIndex,
            "pageSize": this.data.pageSize,
            "param": {

            }
        }
        App.getHttp()._post('/api/psi/baseWarehouse/page', param, true).then(res => {
            console.log('itemList', res)
            this.setData({
                showList: this.data.showList.concat(res.recordList),
                pageIndex: this.data.pageIndex + 1,
                pages: res.pages
            })
        })

    },
    createBtnClick() {
        wx.navigateTo({
            url: '/pages/ccs/inventory/invWarehouseAdd/index',
        })
    },
    onEditClick(e) {
        console.log(e)
        let id = e.currentTarget.dataset.id
        wx.navigateTo({
            url: `/pages/ccs/inventory/invWarehouseAdd/index?id=${id}`,
        })
    },
    initPage() {
        const query = wx.createSelectorQuery()
        query.select('.list-box').boundingClientRect()
        query.select('.btn-submit').boundingClientRect()
        query.exec((res) => {
            console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
            this.setData({
                listViewH: res[0].height - res[1].height,
            })
        })
    },
})