<!--pages/ccs/report/redInformation/index.wxml-->
<view class="page-layout">
  <van-cell-group custom-class="custom-group-layout">
    <!-- 销售组织 -->
    <van-field value="{{ saleOrgName }}" label="销售组织" placeholder="请选择销售组织" is-link readonly required bindtap="onAcquirerClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" right-icon-class="right-icon-class" />
    <van-popup show="{{ showSaleOrgName }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="acquirerCancel">
      <van-picker title="选择销售组织" show-toolbar columns="{{ showSaleOrgColumns }}" bind:change="onPickerChange" bind:confirm="acquirerConfirm" bind:cancel="acquirerCancel" />
    </van-popup>
    <!-- 客户 -->
    <van-field value="{{ custName }}" label="客户" placeholder="请选择客户" is-link readonly required bindtap="onCustNameClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" />
    <van-popup show="{{ showCustName }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="custNameCancel">
      <van-picker title="选择客户" show-toolbar columns="{{ custNameColumns }}" bind:change="onPickerChange" bind:confirm="custNameConfirm" bind:cancel="custNameCancel" />
    </van-popup>
  </van-cell-group>
  <view class="table-note">
    备注:若余额为负数指我司欠客户钱;本余额为实时数据，但少数情况下存在延时统计
  </view>
  <view class="scroll-layout">
    <view class="table-title">
      附表
    </view>
    <view class="table-header align-items-center flex">
      <view class="flexbox m-h-26">文件</view>
      <view class="table-count">下载</view>
    </view>
    <scroll-view id="scorllview" scroll-y bindscrolltolower="loadMore" style='height: {{scrollH}}' enable-flex>
      <block wx:if="{{redInformationData.length}}">
        <view class="row-layout flex align-items-center font-sub" wx:for="{{redInformationData}}" wx:key="itemId">
          <view class="flexbox single-line-ellipsis m-h-26"><text>{{item.fileName}}</text></view>
          <view data-detail="{{item}}" data-index="{{index}}" class="table-count" bindtap="handlerDownload">
            <text wx:if="{{item.downloader}}">下载中</text>
            <van-icon wx:else name="down" />
          </view>
        </view>
      </block>
      <noneView wx:else></noneView>
    </scroll-view>
  </view>
</view>