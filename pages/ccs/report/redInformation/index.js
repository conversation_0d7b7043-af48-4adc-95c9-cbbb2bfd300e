// pages/ccs/report/redInformation/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    orgCode: '',
    saleOrgName: '',
    custId: '',
    custName: '',
    custCode: '',
    showSaleOrgName: false,
    showCustName: false,
    showSaleOrgColumns: [],
    custNameColumns: [],
    redInformationData: {},

  }, 

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let saleOrgOptions = wx.getStorageSync('dictMap').saleOrgCode.map(item => {
      return {
        text: item.name,
        value: item.value
      }
    })
    this.setData({
      showSaleOrgColumns: saleOrgOptions
    })
    this.getDefaultCust()
    this.getCusrData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  
  // 销售组织弹窗
  onAcquirerClick(){
    this.setData({
      showSaleOrgName: true
    })
  },
  
  // 销售组织关闭
  acquirerCancel(){
    this.setData({
      showSaleOrgName: false
    })
  },
  
  // 销售组织选择回调
  acquirerConfirm(e){
    let item = e.detail.value;
    this.setData({
      orgCode: item.value,
      saleOrgName: item.text,
      showSaleOrgName: false
    }, () => {
      this.getRedInformationData()
    })
  },
  getDefaultCust() {
    // 获取主客户数据到当前默认查询条件
    // 销售组织的默认带出
    if (!this.data.saleOrgCode) {
      App.getHttp()._post('/api/mmd/common/bsSaleOrgCode', {
        pageIndex: 1,
        pageSize: 5,
        param: {},
      }).then(resSaleOrg => {
        App.getHttp()._post('/api/mmd/common/bsSup/page', {
          pageIndex: 1,
          pageSize: 55,
          param: {saleOrgCode: resSaleOrg[0].saleOrgCode,},
        }).then(resCust => {
          let val = {}
          if (resCust && resCust.length > 0) {
            // 默认当前客户, 优先取默认设置,没有再拿第一条
            const findDefaultIndex = resCust.findIndex((findSup) => !findSup.mainAccountId);
            val = resCust[findDefaultIndex > -1 ? findDefaultIndex : 0];
          }
          this.setData({
            orgCode: resSaleOrg[0].saleOrgCode,
            saleOrgName: resSaleOrg[0].saleOrgCodeName,
            custName: val.scustName,
            custCode: val.scustCode,
          }, () => {
            this.getRedInformationData()
          })
        })
      })
    }
  },
  // 获取客户数据
  getCusrData() {
    let params = {
      pageIndex: 1,
      pageSize: 2000,
      param: {isUsable: 2}
    }
    App.getHttp()._post('/api/mmd/common/bsSupRelate/querySupCust', params).then(res => {
      let custNameColumns = res.map(item => {
        return {
          text: item.name,
          value: item.code,
          id: item.id,
        }
      })
      this.setData({custNameColumns})
    })
  },
  // 客户弹窗
  onCustNameClick(){
    this.setData({
      showCustName: true
    })
  },
  // 客户选择弹窗关闭
  acquirerCancel(){
    this.setData({
      showCustName: false
    })
  },
  // 客户选择回调
  custNameConfirm(e){
    let item = e.detail.value;
    this.setData({
      custCode: item.value,
      custName: item.text,
      custId: item.id,
      showCustName: false
    }, () => {
      this.getRedInformationData()
    })
  },
  getRedInformationData() {
    let data = this.data
    if (data.orgCode && data.custCode) {
      let params = {
        orgCode: data.orgCode,
        custCode: data.custCode,
      }
      // let params = {
      //   orgCode: '101',
      //   custCode: '0111-00210',
      // }
      App.getHttp()._post('/api/interface/erp/redInformation/query', params).then(res => {
        this.setData({redInformationData: res})
      })
    }
  },
  handlerDownload(event) {
    let downloadUrl = event.currentTarget.dataset.detail.path
    let index = event.currentTarget.dataset.index
    this.data.redInformationData[index].downloader = true
    this.setData({
      redInformationData: this.data.redInformationData
    })
    let that = this
    let downloadTask = wx.downloadFile({
      url: downloadUrl,
      timeout: 300000,
      success(res) {
        that.data.redInformationData[index].downloader = false
        that.setData({
          redInformationData: that.data.redInformationData
        })
      },
      fail(res) {
        that.data.redInformationData[index].downloader = false
        that.setData({
          redInformationData: that.data.redInformationData
        })
      }
    })
    // downloadTask.onProgressUpdate((res) => {
    //   if (res.progress == 100) {
    //     this.setData({
    //       installUpdate: `下载完成...`
    //     })
    //   } else {
    //     this.setData({
    //       installUpdate: `下载中（${res.progress}%）`
    //     })
    //   }
    // })
  }
})