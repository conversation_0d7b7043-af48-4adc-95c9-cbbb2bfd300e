<!--pages/ccs/report/statement/index.wxml-->
<view class="page-layout">
  <van-cell-group custom-class="custom-group-layout">
    <!-- 销售组织 -->
    <van-field value="{{ saleOrgName }}" label="销售组织" placeholder="请选择销售组织" is-link readonly required bindtap="onAcquirerClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" right-icon-class="right-icon-class" />
    <van-popup show="{{ showSaleOrgName }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="acquirerCancel">
      <van-picker title="选择销售组织" show-toolbar columns="{{ showSaleOrgColumns }}" bind:change="onPickerChange" bind:confirm="acquirerConfirm" bind:cancel="acquirerCancel" />
    </van-popup>
    <!-- 客户 -->
    <van-field value="{{ custName }}" label="客户" placeholder="请选择客户" is-link readonly required bindtap="onCustNameClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" />
    <van-popup show="{{ showCustName }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="custNameCancel">
      <van-picker title="选择客户" show-toolbar columns="{{ custNameColumns }}" bind:change="onPickerChange" bind:confirm="custNameConfirm" bind:cancel="custNameCancel" />
    </van-popup>
    <!-- 月度 -->
    <van-field value="{{ period }}" label="月度" placeholder="请选择月度" is-link readonly required bindtap="periodClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" />
    <van-popup show="{{ periodShowPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="periodCancel">
      <van-datetime-picker
        title="选择年月"
        type="year-month"
        value="{{ periodTime }}"
        bind:input="onInput"
        bind:confirm="periodConfirm"
      />
    </van-popup>
  </van-cell-group>
  
  <view class="table-message">
    月度对账待财务结账完毕后约次月15日前显示数据
  </view>
  <view class="table-note">
    备注:若应收客户余额为负数指我司欠客户余额
  </view>
  <view class="scroll-layout">
    <view class="table-title">
      附表
    </view>
    <view class="table-header align-items-center flex">
      <view class="flexbox m-h-26">项目</view>
      <view class="table-count">金额</view>
    </view>
    <scroll-view id="scorllview" scroll-y bindscrolltolower="loadMore" style='height: {{scrollH}}' enable-flex>
      <block wx:if="{{statementData.statement1List.length}}">
        <view class="row-layout flex align-items-center font-sub" wx:for="{{statementData.statement1List}}" wx:key="itemId">
          <view class="flexbox single-line-ellipsis m-h-26" bindtap="showItemCellDetail"
            data-detail="{{item}}"><text>{{item.itemName}}</text></view>
          <view class="table-count">{{item.itemValue}}</view>
        </view>
      </block>
      <noneView wx:else></noneView>
    </scroll-view>
  </view>
  <view class="scroll-layout">
    <view class="table-title">
      附表1:客户优惠发放情况表
    </view>
    <view class="table-header align-items-center flex">
      <view class="flexbox m-h-26">项目</view>
      <view class="table-count">促销商品(45元/台下浮)</view>
      <view class="table-count">销售折扣(直接折扣下浮)</view>
    </view>
    <scroll-view id="scorllview" scroll-y bindscrolltolower="loadMore" style='height: {{scrollH}}' enable-flex>
      <block wx:if="{{statementData.statement2List.length}}">
        <view class="row-layout flex align-items-center font-sub" wx:for="{{statementData.statement2List}}" wx:key="itemId">
          <view class="flexbox single-line-ellipsis m-h-26" bindtap="showItemCellDetail"
            data-detail="{{item}}"><text>{{item.itemName}}</text></view>
          <view class="table-count">{{item.itemValue}}</view>
          <view class="table-count">{{item.itemValue3}}</view>
        </view>
      </block>
      <noneView wx:else></noneView>
    </scroll-view>
  </view>
  <view class="scroll-layout">
    <view class="table-title">
      附表2:客户水泵维修情况表
    </view>
    <view class="table-header align-items-center flex">
      <view class="flexbox m-h-26">项目</view>
      <view class="table-count">促销</view>
    </view>
    <scroll-view id="scorllview" scroll-y bindscrolltolower="loadMore" style='height: {{scrollH}}' enable-flex>
      <block wx:if="{{statementData.statement4List.length}}">
        <view class="row-layout flex align-items-center font-sub" wx:for="{{statementData.statement4List}}" wx:key="itemId">
          <view class="flexbox single-line-ellipsis m-h-26" bindtap="showItemCellDetail"
            data-detail="{{item}}"><text>{{item.itemName}}</text></view>
          <view class="table-count">{{item.itemValue}}</view>
        </view>
      </block>
      <noneView wx:else></noneView>
    </scroll-view>
  </view>
  <view class="scroll-layout">
    <view class="table-title">
      附表3:广告费
    </view>
    <view class="table-header align-items-center flex">
      <view class="flexbox m-h-26">项目</view>
      <view class="table-count">促销</view>
    </view>
    <scroll-view id="scorllview" scroll-y bindscrolltolower="loadMore" style='height: {{scrollH}}' enable-flex>
      <block wx:if="{{statementData.statement5List.length}}">
        <view class="row-layout flex align-items-center font-sub" wx:for="{{statementData.statement5List}}" wx:key="itemId">
          <view class="flexbox single-line-ellipsis m-h-26" bindtap="showItemCellDetail"
            data-detail="{{item}}"><text>{{item.itemName}}</text></view>
          <view class="table-count">{{item.itemValue}}</view>
        </view>
      </block>
      <noneView wx:else></noneView>
    </scroll-view>
  </view>
</view>