<!--pages/ccs/report/repair/index.wxml-->
<view class="page-layout">
  <van-cell-group custom-class="custom-group-layout">
    <!-- 销售组织 -->
    <van-field value="{{ saleOrgName }}" label="销售组织" placeholder="请选择销售组织" is-link readonly required bindtap="onAcquirerClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" right-icon-class="right-icon-class" />
    <van-popup show="{{ showSaleOrgName }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="acquirerCancel">
      <van-picker title="选择销售组织" show-toolbar columns="{{ showSaleOrgColumns }}" bind:change="onPickerChange" bind:confirm="acquirerConfirm" bind:cancel="acquirerCancel" />
    </van-popup>
    <!-- 客户 -->
    <van-field value="{{ custName }}" label="客户" placeholder="请选择客户" is-link readonly required bindtap="onCustNameClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" />
    <van-popup show="{{ showCustName }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="custNameCancel">
      <van-picker title="选择客户" show-toolbar columns="{{ custNameColumns }}" bind:change="onPickerChange" bind:confirm="custNameConfirm" bind:cancel="custNameCancel" />
    </van-popup>
  </van-cell-group>
  <view class="show-oweQty">
    <van-switch size="32rpx" checked="{{ oweQtyCheck }}" bind:change="oweQtyChange"></van-switch>显示欠货
  </view>
  <view class="table-note">
    备注:本数据为实时数据，因业务实际制单时间存在出入，会有部分延时
  </view>
  <view class="repair-layout">
    <block wx:if="{{receivableData.length}}">
      <view class="repair-list" wx:for="{{receivableData}}" wx:key="itemCode">
        <view class="goods-item">
          <view class="goods-item-count">料号: {{item.itemCode}}</view>
          <view class="goods-item-count">品明: {{item.itemName}}</view>
          <view class="goods-item-count">规格: {{item.itemSpc}}</view>
        </view>
        <view class="item-repair-box flex align-items-center font-sub">
          <view class="item-repair-count" data-detail="{{item}}"><text>退货</text><view>{{item.refundQty}}</view></view>
          <view class="item-repair-count"><text>减账</text><view>{{item.debtQty}}</view></view>
          <view class="item-repair-count"><text>报废</text><view>{{item.scrapQty}}</view></view>
          <view class="item-repair-count"><text>还货</text><view>{{item.repayQty}}</view></view>
          <view wx:if="{{oweQtyCheck}}" class="item-repair-count"><text>欠货</text><view>{{item.oweQty}}</view></view>
        </view>
      </view>
    </block>
    <noneView wx:else></noneView>
  </view>
</view>