/* pages/ccs/echarts/expenses/index.wxss */
page{
  background-color: #F2F2F2;
}
/* .echarts-box {
  background-color: #F2F2F2;
} */
.list-item{
  margin: 24rpx;
  background: #fff;
  border-radius: 8rpx;
}
.saleOrgName{
  display: inline-block;
  margin-top: 32rpx;
  margin-left: 24rpx;
  line-height: 40rpx;
  color: #242424;
  font-size: 28rpx;
}
.tag{
  padding: 8rpx 12rpx;
  display: inline-block;
  line-height: 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-top: 24rpx;
  margin-left: 24rpx;
  background: #FFFBE6;
  color: #FAAE16;
  border: 2rpx solid #FAAE16;
}
.canvas-box{
  height: 350rpx;
  padding-bottom: 32rpx;
  width: 100%;
}