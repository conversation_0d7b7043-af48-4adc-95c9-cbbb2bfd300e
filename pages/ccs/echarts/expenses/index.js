// pages/ccs/echarts/expenses/index.js
import * as echarts from '../ec-canvas/echarts.min'
const App = getApp()
// function initChart(canvas, width, height, dpr) {
//   const chart = echarts.init(canvas, null, {
//     width: 500,
//     height: 300,
//     devicePixelRatio: dpr
//   });
//   canvas.setChart(chart);

//   var option = {
//     tooltip: {
//       trigger: 'item'
//     },
//     legend: {
//       top: '5%',
//       left: 'center'
//     },
//     series: [
//       {
//         name: 'Access From',
//         type: 'pie',
//         radius: ['40%', '70%'],
//         avoidLabelOverlap: false,
//         label: {
//           show: false,
//           position: 'center'
//         },
//         emphasis: {
//           label: {
//             show: true,
//             fontSize: 40,
//             fontWeight: 'bold'
//           }
//         },
//         labelLine: {
//           show: false
//         },
//         data: [
//           { value: 1048, name: 'Search Engine' },
//           { value: 735, name: 'Direct' },
//           { value: 580, name: 'Email' },
//           { value: 484, name: 'Union Ads' },
//           { value: 300, name: 'Video Ads' }
//         ]
//       }
//     ]
//   };
//   chart.setOption(option);
//   return chart;
// }

Page({

  /**
   * 页面的初始数据
   */
  data: {
    showList: [],
    pageIndex: 1,
    pageSize: 50,
    pages: 0,
    ec: {
      lazyLoad: true,
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage()
    this.loadMore();
    // this.setData({
    //   ec: {
    //     onInit: initChart
    //   }
    // });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  loadMore(){
    if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    let param ={
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        agencyCode: wx.getStorageSync('custInfo').custCode,
        agencyId: wx.getStorageSync('custInfo').custId,
      }
    }
    console.log(param)
    // /api/mms/news/myx/page
    App.getHttp()._post('/api/vcs/costPool/myx/page', param, true).then(res=>{
      console.log('itemList',res)
      this.setData({
        showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
      wx.nextTick(()=>{
        this.data.showList.map((item,index)=>{
          this.init_echarts(item, '#id' + index,  this.optionFunc)
        })
        // wx.nextTick(()=>{
        //   this.initPage();
        // })
      })
    })
  },
  init_echarts: function (chartData, id, optionFunc) {
    //去获取echarts  这里的id就是echarts的id
    console.log(id,this.selectComponent(id))
    this.selectComponent(id).init((canvas, width, height, dpr) => {
      // 初始化图表
      let variable = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr, // 像素
      });
      variable.setOption(optionFunc(chartData));
      return variable//一定要return 否则展示会有问题
    });
  },
  optionFunc(chartData){
    var option = {
      tooltip: {
        trigger: 'item'
      },
      title: {
        text: (chartData.totalAmount > 1000 ? (chartData.totalAmount/10000).toFixed(3) + '万元' : chartData.totalAmount  + '元') + '\n我的费用',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#242424',
          fontSize: '11px',
          //lineHeight: '9px',
          fontWeight: '500'
        },
      },
      legend: {
        icon: "circle",
        bottom: '0%',
        padding: [0, 0, 0, 0],
        itemWidth: 6,
        left: 'center',
        textStyle: {
          // 图例文字的样式
          color: "#707070",
          fontSize: 12,
        },
      },
      color: ['#A0DD80','#5D95F7','#8677CA'],
      series: [
        {
          // name: 'Access From',
          type: 'pie',
          radius: ['35%', '55%'],
          avoidLabelOverlap: true,
          label: {
            show: true,
            formatter(param) {
              console.log(param)
              return param.data.value > 1000 ? (param.data.value/10000).toFixed(3) + '万元' : param.data.value  + '元';
            },
            fontSize: 10,
            fontWeight: 'bold',
            lineHeight: 10,
            // distanceToLabelLine: 5
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 10,
              fontWeight: 'bold',
              lineHeight: 10,
            }
          },
          labelLine: {
            show: true,
            // length: 6,
            // length2: 10
          },
          data: [
            { value: chartData.lockAmount, name: '占用额度',itemStyle: { color: '#A0DD80' } },
            { value: chartData.availableAmount, name: '可用额度',itemStyle: { color: '#5D95F7' } },
            { value: chartData.usedAmount, name: '已用额度',itemStyle: { color: '#8677CA' } },
          ]
        }
      ]
    };
    return option;
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('.echarts-box').boundingClientRect()
    query.exec((res) => {
      console.log('query',res[0])
      this.setData({
        listViewH: res[0].height,
      })
    })
  },

})