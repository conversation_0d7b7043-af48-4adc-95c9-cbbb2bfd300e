// pages/ccs/benchCenter/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeName: '',
    scrollIntoView: "",
    openSet: false,
    operateBase: '',
    operateChildren: '',
    userList: [],
    scrollTop: 0,
    offsetTop: 0,
    moduleList:[]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let userBench = [...wx.getStorageSync('userBench') || App.globalData.defaultUserBench]
    this.setData({
      userList: userBench
    })
    this.getModuleMenu()
  },
  getModuleMenu() {
    // 工作台要排除的几个菜单, 让用户去tabbarpage使用
    const excludeMenu = ['/pages/ccs/sup/purchase/index','/pages/ccs/down/purchaseNew/index','/pages/ccs/retail/index']
    // custMenus 整理成 authMenus
    const custMenus = wx.getStorageSync('custMenus')
    const authMenus = custMenus.filter(root => root.isEnd == 1 && root.parentId && root.parentId != -1).map(rootMenu => {
      return {
        id: rootMenu.url || rootMenu.id, name: rootMenu.name, modules: custMenus.filter(moduleRoot => moduleRoot.parentId == rootMenu.id&&!excludeMenu.includes(moduleRoot.perms)).map(moduleMenu => {
          return {
            icon: moduleMenu.icon,
            text: moduleMenu.name,
            to: moduleMenu.perms
          }
        })
      }
    })
    this.setData({
      moduleList:authMenus
    })
  },
  onScroll(event) {
    wx.createSelectorQuery()
      .select('#scroller')
      .boundingClientRect((res) => {
        this.setData({
          scrollTop: event.detail.scrollTop,
          offsetTop: res.top,
        });
      })
      .exec();
  },
  onClickSet() {
    if (this.data.openSet) {
      wx.setStorageSync('userBench', this.data.userList)
    }
    const openSet = !this.data.openSet
    this.setData({
      openSet: openSet,
      operateBase: openSet ? 'del' : '',
      operateChildren: openSet ? 'add' : '',
    })
  },
  onClickDel(e) {
    const info = e.detail
    this.data.userList.splice(info.index, 1)
    this.setData({
      userList: this.data.userList
    })
    wx.showToast({
      title: '已删除!',
      icon: "none"
    })
  },
  onClickAdd(e) {
    const info = e.detail
    if (this.data.userList.length == 7) {
      wx.showToast({
        title: '已达到常用应用限制(7个)',
        icon: "none"
      })
    } else if (this.data.userList.findIndex(res => res.text == info.item.text) > -1) {
      wx.showToast({
        title: '勿重复添加,常用应用中已存在!',
        icon: "none"
      })
    } else {
      this.data.userList.push(info.item)
      this.setData({
        userList: this.data.userList
      })
      wx.showToast({
        title: '添加成功!',
        icon: "success"
      })
    }
  },
  onTabSwitch(e) {
    this.setData({
      scrollIntoView: e.detail.name
    })
  }
})