<!--pages/ccs/benchCenter/index.wxml-->
<scroll-view class="root-layout" scroll-into-view="{{scrollIntoView}}" scroll-y bind:scroll="onScroll" id="scroller">
  <view class="bg-white">
    <view class="user-layout flex justify-content-between">
      <view class="font-sub-hint">常用应用 (7个)</view>
      <view class="font-sub-hint set" bindtap="onClickSet">{{openSet?'完成':'设置'}}</view>
    </view>
    <benchGrid columnNum="4" iconSize="96rpx" layoutList="{{userList}}" operate="{{operateBase}}" bindoperate="onClickDel" />
  </view>
  <view class=" m-t-24">
    <van-sticky scroll-top="{{ scrollTop }}" offset-top="{{ offsetTop }}">
      <van-tabs class="tab-layout" active="{{activeName}}" bind:change="onTabSwitch" title-active-color="#00b9c3" ellipsis="{{false}}">
        <block wx:for="{{moduleList}}" wx:for-item="tabItem" wx:key="name">
          <van-tab title="{{tabItem.name}}" name="{{tabItem.id}}"></van-tab>
        </block>
      </van-tabs>
    </van-sticky>
  </view>
  <view class="bg-white bench-layout" wx:for="{{moduleList}}" wx:key="id" id="{{item.id}}">
    <benchGrid operate="{{operateChildren}}" columnNum="4" iconSize="96rpx" layoutList="{{item.modules}}" title="{{item.name}}" bindoperate="onClickAdd" />
  </view>
</scroll-view>