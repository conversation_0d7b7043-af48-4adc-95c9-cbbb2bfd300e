/* pages/ccs/benchCenter/index.wxss */
.root-layout {
  background-color: #F2F2F2;
  width: 100vw;
  height: 100vh;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
::-webkit-scrollbar{
  width: 0;
  height: 0;
  color: transparent;
}
.bg-white{
  width: 100%;
  background-color: white;
}
.user-layout{
  padding: 24rpx 32rpx 0 32rpx;
  background-color: white;
}
.user-layout .set{
  color:#00b9c3
}

.bench-layout:nth-of-type(n+4){
  margin-top: 24rpx;
 
}

.tab-layout {
  width: 100%;
}