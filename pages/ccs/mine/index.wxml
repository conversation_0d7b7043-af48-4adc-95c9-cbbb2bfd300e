<!-- pages/ccs/mine/index.wxml -->
<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<!-- 顶部区域 -->
<view class="top-layout" id="top-layout">
  <view class="top-nav" style="margin: {{navMarginTop}}rpx {{navMarginRight}}rpx 12rpx 24rpx">
    <view class="nav-left">
      <view class="top-nav-text">我的</view>
    </view>
    <!-- <van-icon name="/asset/svgs/message-red.svg" size="28px" info="{{unReadMessageCount}}" bindtap="linkToMessage"/> -->
  </view>
</view>
<view class="root-layout" style="margin-top:{{clientRectBottom}}rpx;">
  <!-- 个人信息区域 -->
  <view class="user-layout flex align-items-center" bindtap="onClickUser">
    <image class="avatar" src="{{avatar}}" mode="aspectFit" />
    <view class="nickname single-line-ellipsis flexbox">{{nickname}}</view>
    <!-- 改动 -->
    <van-icon name="arrow" size="16px"  color="#fff" />
  </view>
  <!-- 我的信用和我的费用 -->
  <view class="task-layout horizontal-layout task-layout-expand" wx:if="{{creditExpensesShowFlag}}">
    <block wx:for="{{accountTypeList}}" wx:key="index">
      <block wx:if="{{item.isShow}}">
        <view class="horizontal-item" data-url="{{item.to}}" bindtap="onClickAccount">
          <image class="horizontal-img" src="{{item.icon}}"></image>
          <view class="horizontal-text">{{item.text}}</view>
        </view>
      </block>
    </block>
  </view>
  <!-- 更多功能 -->
  <view class="task-layout task-layout-expand" wx:if="{{moreTypeList.length !== 0}}">
    <view class="title title-expand">更多功能</view>
    <gridLayout columnNum="4" iconSize="48px" layoutList="{{moreTypeList}}" />
  </view>
</view>