// pages/ccs/mine/index.js
const App = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    purchaseQty: 0,
    purchaseAmount: 0,
    noticeCount: 0,
    trackCount: 0,
    clientRectBottom: 100,
    unAudit: 0,
    undelivered: 0,
    unsigned:0,
    nickname: '',
    avatar: '',
    unReadFlag:false,
    moreTypeList: [
      {
        icon: '/asset/svgs/more-address.svg',
        text: '收货地址',
        to: '/pages/ccs/moreFeatures/addr/index'
      },
      {
        icon: '/asset/svgs/more-track.svg',
        text: '足迹',
        to: '/pages/ccs/track/index'
      },
      {
        icon: '/asset/svgs/more-focus.svg',
        text: '我的收藏',
        to: '/pages/ccs/focus/index'
      },
      {
        icon: '/asset/svgs/more-message.svg',
        text: '问题反馈',
        to: '/pages/ccs/feedback/add/index'
      },
      {
        icon: '/asset/svgs/more-setting.svg',
        text: '设置',
        to: '/pages/ccs/sets/index'
      }
    ],
    accountTypeList: [
      {
        icon: '/asset/svgs/mine-credit.svg',
        text: '我的信用',
        to: '/pages/ccs/echarts/credit/index'
      },
      {
        icon: '/asset/svgs/mine-cost.svg',
        text: '我的费用',
        to: '/pages/ccs/echarts/expenses/index'
      }
    ],
    creditExpensesShowFlag: false,
    navMarginTop: 24,
    navMarginRight: 100,
    unReadMessageCount: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const supInfo = wx.getStorageSync('supInfo')
    this.setData({
      nickname: `${supInfo.setsOfBooksId}-${supInfo.setsOfBooksName}`,
      avatar: supInfo && supInfo.avatarUrl ? supInfo.avatarUrl: "/asset/svgs/default-avatar.svg"
    })
    // 判断我的信用我的费用是否显示
    const custMenus = wx.getStorageSync('custMenus')
    let creditShowFlag = false
    let expensesShowFlag = false
    if(custMenus.findIndex(menu=>menu.perms&&menu.perms=='/pages/ccs/echarts/credit/index')>-1){
      creditShowFlag = true
    }
    if(custMenus.findIndex(menu=>menu.perms&&menu.perms=='/pages/ccs/echarts/expenses/index')>-1){
      expensesShowFlag = true
    }
    // 判断更多功能是否显示
    this.data.moreTypeList.forEach(item=>{
      if(custMenus.findIndex(menu=>menu.perms&&menu.perms==item.to)>-1){
        item.isShow = true
      }else{
        item.isShow = false
      }
    })
    this.setData({
      'accountTypeList[0].isShow': creditShowFlag,
      'accountTypeList[1].isShow': expensesShowFlag,
      creditExpensesShowFlag : creditShowFlag || expensesShowFlag,
      moreTypeList: this.data.moreTypeList.filter(item=>{
        return item.isShow
      })
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    // 获取菜单按钮（右上角胶囊按钮）的布局位置信息。
    const rect = wx.getMenuButtonBoundingClientRect()
    console.log('胶囊', rect);
    this.setData({
      clientRectBottom: (rect.bottom + 24) * App.globalData.pxToRpxRatio,
      navMarginTop: (rect.top) * App.globalData.pxToRpxRatio,
      navMarginRight: (rect.width + 23) * App.globalData.pxToRpxRatio
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 全局自定义custom-tab-bar需要
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      // this.getTabBar().setData({
      //   selected: 3
      // })
      this.getTabBar().initSelected(getApp().tabbarIndex);
    }
    //一起不做
    // this.getUnreadCount()
  },
  getUnreadMsgCount(){
    const param = {
      custCode:wx.getStorageSync('mastCode'),
      readStatus:1
    }
    App.getHttp()._post('myx/ccs-mobile-web/msg/notice/getUnreadMessageNum', param).then(res => {
      this.setData({
        unReadFlag:res&&res.count>0,
        unReadMessageCount: res && res.count==0?'':res.count
      })
    })
  },
  linkToMessage(){
    wx.navigateTo({
      url: '/pages/ccs/message/msgList/msgList',
    })
  },
 
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  // 点击我的信用和我的费用
  onClickAccount(e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({ url })
  },
  // 点击我的信息整行跳转
  onClickUser() {
    wx.navigateTo({
      url: '/pages/sob/index'
    })
  },
  // 获取未读数量
  getUnreadCount(){
    const userInfo = wx.getStorageSync('userInfo')
    const supInfo = wx.getStorageSync('supInfo')
      let param ={
          isRead: 1,
          receiveUserId: userInfo.userId,
          receiveUserSetsId: supInfo.setsOfBooksId,
      }
      App.getHttp()._post('/api/message/notifyMessage/gerCurrentUserUnreadCount', param, true).then(res=>{
        this.setData({
          unReadMessageCount: res.content&&res.content==0? '':res.content
        })
      })
  },
})