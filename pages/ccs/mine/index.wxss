/* pages/ccs/mine/index.wxss */
/* 页面背景色 */
page {
  background-color: #f1f2f2;
  font-family: PingFang SC-Regular, PingFang SC;
}
.bg-color {
  background: rgba(0, 0, 0, 0.04);
}
.bg-img {
  width: 100%;
  height: 450rpx;
}

.root-layout {
  position: absolute;
  top: 0;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
}

.avatar {
  width: 90rpx;
  height: 90rpx;
  /* border: 6rpx solid rgba(255, 255, 255, 0.56); */
  border-radius: 50%;
}
.user-layout{
  padding: 0 24rpx;
}
.nickname {
  margin: 0 32rpx;
  font-family: PingFangSC-Medium;
  font-size: 28rpx;
  color: #ffffff;
  /* line-height: 43rpx; */
  font-weight: 400;
  -webkit-line-clamp: 2;
}

.setting {
  width: 48rpx;
  height: 48rpx;
  margin-left: 24rpx;
}

.focus-layout {
  margin-top: 22rpx;
  height: 80rpx;
  font-family: SanFranciscoText-Semibold;
  color: #ffffff;
  font-size: 28rpx;
  line-height: 52rpx;
}

.focus-layout .number {
  font-size: 36rpx;
  margin-left: 16rpx;
  line-height: 52rpx;
}
.mt-32 {
  margin-top: 32rpx;
}
.mt-24 {
  margin-top: 24rpx;
}
.mt-48 {
  margin-top: 48rpx;
}
.task-layout {
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  box-shadow: 0 0 12rpx 0 rgba(45, 45, 75, 0.04);
  border-radius: 12px;
}
.task-layout .title {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 48rpx;
  font-weight: 500;
}
.task-layout .yymmdd {
  width: 210rpx;
  height: 48rpx;
  background-color: rgb(245, 245, 245);
  border-radius: 48rpx;
}
.task-layout .cell {
  width: 70rpx;
  height: 48rpx;
  border-radius: 48rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  line-height: 48rpx;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}
.task-layout .active {
  width: 70rpx;
  height: 48rpx;
  border-radius: 48rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  line-height: 48rpx;
  text-align: center;
  color: #ffffff;
  background-color: #f97d4e;
}
.task-layout .label {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.75);
  line-height: 40rpx;
  font-weight: 400;
}
.task-layout .itemprice {
  font-family: SanFranciscoText-Semibold;
  font-size: 36rpx;
  color: #f97d4e;
  line-height: 52rpx;
  font-weight: 600;
}
.task-layout .uom {
  margin-right: 8rpx;
  font-family: SanFranciscoText-Semibold;
  font-size: 28rpx;
  color: #f97d4e;
  line-height: 52rpx;
  font-weight: 600;
}
.msg-layout {
  position: relative;
  width: 48rpx;
  height: 48rpx;
  margin-left: 24rpx;
}
.msg-img {
  width: 48rpx;
  height: 48rpx;
}
.msg-inner-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #f97d4e;
  position: absolute;
  right: 0;
  top: 0;
}
.top-layout {
  background-color: #fff;
  width: 100%;
  position: absolute;
  top: 0;
}
.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.nav-left {
  display: flex;
  align-items: center;
}
.top-nav-text {
  margin-left: 16rpx;
  line-height: 56rpx;
  font-size: 36rpx;
  font-weight: 400;
}
.horizontal-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 70rpx;
  margin-top: 40rpx;
}
.horizontal-item {
  display: flex;
  align-items: center;
}
.horizontal-img {
  width: 96rpx;
  height: 96rpx;
}
.horizontal-text {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #242424;
}
.task-layout-expand {
  border-radius: 5px;
}
.task-layout .title-expand {
  font-size: 28rpx;
  font-weight: 400;
  color: #707070;
  margin-bottom: 24rpx;
}
/* 我的页面宫格文字单独样式 */
.van-grid-item__content{
  padding: 16rpx 16rpx !important;
}
.van-grid-item__icon+.van-grid-item__text {
  margin-top: 36rpx!important;
}
