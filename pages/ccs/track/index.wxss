/* pages_sub/pages/ccs/sup/track/index.wxss */
.root-layout{
  background: rgba(0,0,0,0.04);
}
.root-layout .none{
  padding-top: 160rpx;
}
.root-layout  .filter-layout{
  padding: 32rpx 24rpx;
}
.root-layout  .filter-layout .right{
  color: #00b9c3;
  line-height: 32rpx;
}
.root-layout  .filter-layout .left{
  line-height: 32rpx;
}
.root-layout  .filter-layout .filter-icon{
  width: 32rpx;
  height: 32rpx;
}
.root-layout .card-layout{
  padding: 0 24rpx 10rpx 24rpx;
}
.root-layout .swiper-layout{
  margin-top: 24rpx;
}
.root-layout .swiper-box{
  background-color: white;
  border-radius: 8rpx;
}
.root-layout .goods-card{
  position:relative;
}
.root-layout .goods-card:after {
  position: absolute;
  bottom: 1rpx;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: rgba(0,0,0,0.1);
 }
 .root-layout .goods-card:last-child:after{  
  height:0;
}

.root-layout .swipe-right-del {
  position: relative;
  display: flex;
  width:320rpx;
  height: 100%;
  background-color: #fff;
  color: #fff;
  text-align: center;
  line-height: 100%;
}
.root-layout .swipe-right-del .delete-button {
  flex: 1;
  line-height: 224rpx;
  height: 100%;
  background: #FF4A4D;
  border-radius: 0;
  font-size: 28rpx;
  color: #fff;
}
.collect-button{
  flex: 1;
  line-height: 224rpx;
  height: 100%;
  background: #FAAE16;
  border-radius: 0;
  font-size: 28rpx;
  color: #fff;
}

.root-layout .swipe-right-del .delete-button::after {
  border: none;
}
.root-layout .foot-layout{
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  padding-left: 24rpx;
  height: 100rpx;
  background-color: #FFFFFF;
}
.root-layout .foot-layout .all{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.45);
  font-weight: 400;
}
.root-layout .radio-img{
  width: 48rpx;
  height: 48rpx;
}
.root-layout .foot-layout .total{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.85);
  font-weight: 400;
  text-align: right;
  margin: 0 34rpx;
}
.root-layout .foot-layout .end{
  width: 240rpx;
  background: rgba(0,0,0,0.20);
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 100rpx;
}
.foot-layout .end-active{
  width: 240rpx;
  background: #00b9c3;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 100rpx;
}
