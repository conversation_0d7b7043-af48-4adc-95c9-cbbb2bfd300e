<!-- pages_sub/pages/ccs/sup/track/index.wxml -->
<view class=" page root-layout" id="root-layout">
  <view class="filter-layout flex justify-content-between font-main" id="top-layout">
    <view class="flex" catchtap="onClickFilter">
      <block wx:if="{{isSupMode}}">
        <view class="left">筛选</view>
        <image class="filter-icon" src="/asset/imgs/filter.png" />
      </block>
      <view class="flex {{isSupMode ? 'm-l-24' : ''}}" wx:if="{{typeActions.length>1}}" catchtap="onClickFilter" data-is-type="{{true}}">
        <view class="left">类型</view>
        <image class="filter-icon" src="/asset/imgs/type.png" />
      </view>
    </view>
    <view class="right" catchtap="onClickSet">{{showOpenSet?'取消':'管理'}}</view>
  </view>
  <scroll-view scroll-y bindscrolltolower="onReachBottom" style="width: 100%; height: {{calculateViewHeightPx}}px;">
    <!-- 关注列表 -->
    <block wx:if="{{focusList.length > 0}}">
      <view class="card-layout" wx:for="{{focusList}}" wx:key="gIndex" wx:for-item="groupItem" wx:for-index="gIndex">
        <view class="font-main">{{groupItem.date}}</view>
        <view class="swiper-layout " wx:for="{{groupItem.list}}" wx:for-index="tIndex" wx:key="tIndex">
          <van-swipe-cell right-width="{{ 184 }}" wx:key="tIndex">
            <view class="flex_center swiper-box">
              <image wx:if="{{showOpenSet}}" class="radio-img m-l-24" src="{{item.check?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickItemChecked" data-checked-layer="2" data-g-index="{{gIndex}}" data-t-index="{{tIndex}}" />
              <goods-card class="goods-card flexbox" wx:key="tIndex" item="{{item}}" bindtap="onClickSkuDetail" data-item="{{item}}" data-type="2" showCart isHorizontal bind:addToCarts="onAddToCarts" showRetailPrice="{{!isSupMode}}" showQtyOnhand="{{!isSupMode}}"/>
            </view>
            <view slot="right" class="swipe-right-del">
              <view class="collect-button"  bindtap="onClickColItem" data-collect-id="{{item.itemId}}" data-g-index="{{gIndex}}" data-t-index="{{tIndex}}">
                收藏
              </view>
              <view class="delete-button" bindtap="onClickDelItem" data-collect-id="{{item.id}}">
                删除
              </view>
            </view>
          </van-swipe-cell>
        </view>
      </view>
    </block>
    <!-- 缺省 -->
    <view class="none" wx:else>
      <no-product noneTxt="暂无商品" />
    </view>
  </scroll-view>
  <view wx:if="{{showOpenSet}}" class="foot-layout flex align-items-center" id="foot-layout">
    <view class="all flex align-items-center">
      <image class="radio-img" src="{{isCheckAll?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickAllChecked"  />全选
    </view>
    <view class="flexbox total">
    </view>
    <view class="end-active" catchtap="onClickDelAll">删除</view>
  </view>
  <add-carts show="{{showAddCartsPop}}" bind:onClose="onCloseAddCarts" item="{{addItemInfo}}" isSup="{{true}}"></add-carts>
  <van-action-sheet show="{{ showFilterAction }}" actions="{{ actions }}" cancel-text="取消" bind:close="onCloseFilter" bind:select="onSelectFilter" bind:cancel="onCloseFilter" />
  <van-action-sheet show="{{ showTypeAction }}" actions="{{ typeActions }}" cancel-text="取消" bind:close="onCloseFilter" bind:select="onSelectFilter" bind:cancel="onCloseFilter" data-is-type="{{true}}"/>
</view>