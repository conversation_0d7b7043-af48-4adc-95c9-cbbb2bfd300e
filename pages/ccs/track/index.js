// pages_sub/pages/ccs/sup/track/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageIndex: 1,
    pageSize: 10,
    focusList: [],
    showOpenSet:false,
    showAddCartsPop: false,
    showFilterAction:false,
    isUsable:'',
    isCheckAll:false,
    actions: [
      {name: '生效中',value:2}, {name: '已失效',value:1}
    ],
    showTypeAction:false,
    typeActions:[],
    isSupMode: true,
    custType: '',
    calculateViewHeightPx: '500'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const custInfo =  wx.getStorageSync('custInfo')
    this.setData({
      custType: custInfo.custType,
      isSupMode:custInfo.custType==5,//经销商优先是向上模块
      typeActions:custInfo.bsRelates&&custInfo.supRelates?[ {name: '厂商采购足迹',value:true}, {name: '经销采购足迹',value:false}]:[]//厂商协同和购销关系都存在,才有类型选择
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        calculateViewHeightPx: res[0].height - res[1].height,
      })
    })
  },

  //获取商品列表
  getNoticeGoods() {
    let params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        invoiceCustCode: wx.getStorageSync('custInfo').custCode,
        isUsable:this.data.isUsable,
        // vendorCode: wx.getStorageSync('vendorInfo').bvendorCode,
        // vendorSetsOfBooksId: wx.getStorageSync('vendorInfo').ssetsOfBooksId,
        // invoiceCustCode: wx.getStorageSync('vendorInfo').scustCode,
        // invoiceSetsOfBooksId: wx.getStorageSync('vendorInfo').bsetsOfBooksId,
      }
    }
    let url = this.data.isSupMode ?'/api/vcs/mmsController/myx/up/getFootprint' : '/api/psi/mmsController/myx/down/getFootprint'
    App.getHttp()._post(url, params).then(res => {
      this.setData({
        focusList: this.data.focusList.concat(res||[]),
        pageIndex: this.data.pageIndex * 1 + 1
      })
      if (res.length > 0) {
        this.pageIndex = this.data.pageIndex * 1 + 1
      } else {
        wx.showToast({
          title: '没有更多数据了...',
          duration: 3000,
          icon: 'none'
        })
      }
    })
  },
  onClickSkuDetail(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/${this.data.isSupMode ? 'sup': 'down'}/skuDetail/index?itemId=${item.itemId}&vendorCode=${item.vendorCode}&invoiceSetsOfBooksId=${item.invoiceSetsOfBooksId}&invoiceCustCode=${item.invoiceCustCode}&vendorName=${item.vendorName}&vendorSetsOfBooksId=${item.vendorSetsOfBooksId}&supId=${item.supId}`,
    })
  },
  onAddToCarts(e) {
    let addItemInfo = e.currentTarget.dataset.item
    addItemInfo.qty = 1
    this.setData({
      addItemInfo: addItemInfo,
      showAddCartsPop: true
    })
  },
  onCloseAddCarts(e) {
    if (e.detail) {
      this.addShops(e.detail)
    }
    this.setData({
      showAddCartsPop: false
    })
  },
  addShops(reault) {
    if (reault.qty == 0) {
      wx.showToast({
        title: '购买数量不能为0',
        icon: 'error'
      })
      return
    }
    const params = {
      channelCode: reault.channelCode, //渠道
      productCode: reault.productCode, //产品组
      saleOrgCode: reault.saleOrgCode, //销售组织
      orderType: 0, // 订单类型
      qtyModel: 2, // 1扣减购物车 2增加购物车 3直接填充购买数量
      invoiceCustId: reault.invoiceCustId, // 客户ID
      invoiceCustCode: reault.invoiceCustCode, // 客户ID
      invoiceSetsOfBooksId: reault.invoiceSetsOfBooksId, // 客户ID
      vendorSetsOfBooksId: reault.vendorSetsOfBooksId, // 供应方账套ID
      vendorCode: reault.vendorCode, // 供应商ID
      vendorId: reault.vendorId, // 供应商ID
      // 向下模型
      itemList: [{
        itemId: reault.itemId, // 商品ID
        itemCode: reault.itemCode, // 商品编码
        itemName: reault.itemName, // 商品名称
        purchaseQty: reault.qty // 购买数量
      }],
       // 向下模型
      items:[{
        itemId: reault.itemId, // 商品ID
        itemCode: reault.itemCode, // 商品编码
        itemName: reault.itemName, // 商品名称
        itemCount: reault.qty // 购买数量
      }]
    }
    App.getHttp()._post(this.data.isSupMode?'/api/vcs/mobile-web/myx/supOrder/addToCart':'/api/psi/shoppingCart/addToCarts', params).then(res => {
      wx.showToast({
        title: '加入成功',
        icon: 'success'
      })
    })
  },
  onClickSet(){
    this.setData({
      showOpenSet:!this.data.showOpenSet
    })
  },
  onClickFilter(e){
    const dataset = e.currentTarget.dataset
    if(dataset&&dataset.isType){
      this.setData({
        showTypeAction:true
      })
    }else{
      this.setData({
        showFilterAction:true
      })
    }
  },
  onCloseFilter(){
    this.setData({
      showFilterAction:false,
      showTypeAction:false
    })
  },
  onSelectFilter(e){
    const dataset = e.currentTarget.dataset
    if(dataset&&dataset.isType){
      this.setData({
        isSupMode: e.detail.value
      })
    }else{
      this.data.isUsable = e.detail.value
    }
    this.data.pageIndex = 1
    this.setData({
      focusList: []
    })
    this.getNoticeGoods()
  },
  onClickItemChecked(e){
    const indexInfo = e.currentTarget.dataset
    const currentCheck = this.data.focusList[indexInfo.gIndex].list[indexInfo.tIndex].check
    this.data.focusList[indexInfo.gIndex].list[indexInfo.tIndex].check = !currentCheck
    this.setData({
      focusList:this.data.focusList
    })
  },
  onClickAllChecked(){
    this.data.focusList.forEach(gele=>{
      gele.list.forEach(tele=>{
        tele.check = !tele.check
      })
    })
    this.setData({
      isCheckAll:!this.data.isCheckAll,
      focusList:this.data.focusList
    })
  },
  onClickDelItem(e) {
    wx.showActionSheet({
      alertText:'确定要删除吗?',
      itemList: ['是'],
      itemColor:'#FF4A4D',
      success: (res)=> {
        const collectId = e.currentTarget.dataset.collectId
        App.getHttp()._post('/api/mms/itemFootprintRelation/myx/delete', { ids: [collectId] }).then(res => {
          this.data.pageIndex=1
          this.data.focusList=[]
          this.getNoticeGoods()
        })
      }
    })
  },
  onClickColItem(e) {
    const collectId = e.currentTarget.dataset.collectId
    const gIndex  = e.currentTarget.dataset.gIndex
    const tIndex  = e.currentTarget.dataset.tIndex
    let item = this.data.focusList[gIndex].list[tIndex]
    App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect', { 
      itemId: collectId,
      vendorName: item.vendorName,
      vendorCode: item.vendorCode,
      vendorSetsOfBooksId: item.vendorSetsOfBooksId,
      invoiceCustCode: item.invoiceCustCode,
      invoiceSetsOfBooksId: item.invoiceSetsOfBooksId,
      sourceModule:this.data.isSupMode?'up':'down'
    }).then(res => {
      wx.showToast({
        title: '收藏成功',
        icon:'success'
      })
    })
  },
  onClickDelAll(){
    let ids=[]
    this.data.focusList.forEach(gele=>{
      gele.list.forEach(tele=>{
        if(tele.check)ids.push(tele.id)
      })
    })
    if(ids.length===0){
      wx.showToast({
        title: '无数据操作',
        icon:'error'
      })
      return
    }
    App.getHttp()._post('/api/mms/itemFootprintRelation/myx/delete', { ids }).then(res => {
      this.data.pageIndex=1
      this.data.focusList=[]
      this.getNoticeGoods()
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.data.pageIndex=1
    this.data.focusList=[]
    this.getNoticeGoods()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    this.data.pageIndex = this.data.pageIndex+1
    this.getNoticeGoods()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})