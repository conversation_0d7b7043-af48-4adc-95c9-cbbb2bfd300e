<!--pages/ccs/newsCenter/newsDetail.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout">
    <view class="content-box">
        <view class="msg-title">
            {{noticeMsg.newsTitle}}
        </view>
        <view class="date-box">
            <view class="msg-date">
                {{noticeMsg.publishTime}}
            </view>
            <view class="news-tag tag-sys" wx:if="{{noticeMsg.topCategory === '1'}}">{{wxsUtil.getDictName(mapValue,noticeMsg.topCategory)}}</view>
            <view class="news-tag tag-news" wx:if="{{noticeMsg.topCategory === '2'}}">{{wxsUtil.getDictName(mapValue,noticeMsg.topCategory)}}</view>
            <view class="news-tag tag-rank" wx:if="{{noticeMsg.topCategory === '3'}}">{{wxsUtil.getDictName(mapValue,noticeMsg.topCategory)}}</view>
            <view class="news-tag tag-policy" wx:if="{{noticeMsg.topCategory === '4'}}">{{wxsUtil.getDictName(mapValue,noticeMsg.topCategory)}}</view>
            <view class="news-tag tag-sales" wx:if="{{noticeMsg.topCategory === '5'}}">{{wxsUtil.getDictName(mapValue,noticeMsg.topCategory)}}</view>
        </view>
        <view class="line"></view>
        <view class="msg-wrap" wx:if="{{noticeMsg.newsBody}}">
            <rich-text nodes="{{noticeMsg.newsBody}}" style="word-break: break-all;" class="rich-text"></rich-text>
        </view>
        <view wx:if="{{noticeMsg.fileRelationUrl}}">
            <image src="{{noticeMsg.fileRelationUrl}}"  class="msg-img" mode="aspectFit" lazy-load />
        </view>
    </view>
    <view wx:if="{{attchList.length>0}}" class="attch-layout">
        <view class="attch-title">附件（{{attchList.length}}）</view>
        <view class="attch-box" wx:for="{{attchList}}" wx:key="attachmentId" wx:for-item="item"   data-file-name="{{item.fileName}}" data-url="{{item.fileUrl}}" bindtap="onClickPreview">
            <view class="icon-box">
                <van-icon name="{{wxsUtil.fileIconName(item.fileType)}}" size="64rpx" />
                <!-- <van-icon wx:if="{{['xls','xlsx'].includes(item.fileType)}}" name="/asset/imgs/excel.png" size="64rpx" />
                <van-icon wx:if="{{['txt'].includes(item.fileType)}}" name="/asset/imgs/txt.png" size="64rpx" />
                <van-icon wx:if="{{['png','jpg','bmp', 'gif'].includes(item.fileType)}}" name="/asset/imgs/pic.png" size="64rpx" />
                <van-icon wx:if="{{['zip','rer','arj'].includes(item.fileType)}}" name="/asset/imgs/zip.png" size="64rpx" />
                <van-icon wx:if="{{['ppt','pptx'].includes(item.fileType)}}" name="/asset/imgs/ppt.png" size="64rpx" />
                <van-icon wx:if="{{['doc','docx'].includes(item.fileType)}}" name="/asset/imgs/word.png" size="64rpx" />
                <van-icon wx:if="{{['pdf'].includes(item.fileType)}}" name="/asset/imgs/pdf.png" size="64rpx" /> -->
            </view>
            <view class="fileName-box">
                <view class="fileName-text single-line-ellipsis">{{item.fileName}}</view>
                <view class="fileName-size">{{item.fileSize}}k</view>
            </view>
            <!-- <van-icon name="/asset/imgs/download.png" size="32rpx"  data-file-name="{{item.fileName}}" data-url="{{item.fileUrl}}" catchtap="onClickDownLoad"/> -->
        </view>
    </view>
</view>