// pages/ccs/newsCenter/newsDetail.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        noticeMsg: {},
        attchList: []
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.setData({
            id: options.id,
            msgId: options.msgId,
            mapValue: wx.getStorageSync('dictMap').newsTopCategory
        })
        this.getNewsById()
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    getNewsById() {
        // 标记已读
        // App.getHttp()._post(`/api/message/notifyMessage/markRead/${this.data.msgId}`,{id: this.data.msgId})
        // 查询详情
        App.getHttp()._get(`/api/mms/news/myx/${this.data.id}`).then(res => {
            res.newsBody = this.formatRichText(res.newsBody)
            this.setData({
                noticeMsg: res
            })
            // 根据id查附件
            if (res.fileRelationIds) {
                App.getHttp()._get(`/api/base/fileAttach/listByIds?ids=${res.fileRelationIds}`).then(res => {

                    this.setData({
                        attchList: res
                    })
                })
            }
        })
    },
    onClickPreview(e) {
        const dataset = e.currentTarget.dataset
        const fileType = dataset.fileName ? dataset.fileName.split('.')[1] : 'doc'
        wx.showLoading({
            title: '努力加载中...',
        })
        wx.downloadFile({
            url: dataset.url,
            header: {
                'Authorization': wx.getStorageSync('authorization'),
            },
            success: function (res) {
                const filePath = res.tempFilePath
                if (['jpg', 'png', 'jpeg'].includes(fileType)) {
                    wx.previewImage({
                        current: filePath,
                        urls: [filePath]
                    })
                } else {
                    wx.openDocument({
                        fileType: fileType,
                        filePath: filePath,
                        fail: function () {
                            wx.showToast({
                                title: '打开失败',
                                icon: "fail"
                            })
                        },
                    })
                }
            },
            fail: function () {
                wx.showToast({
                    title: '打开失败',
                    icon: "fail"
                })
            },
            complete: function () {
                wx.hideLoading()
            }
        })
    },
    onClickDownLoad(e) {
        const dataset = e.currentTarget.dataset
        const fileType = dataset.fileName ? dataset.fileName.split('.')[1] : 'doc'
        wx.showLoading({
            title: '努力加载中...',
        })
        console.log(wx.env.USER_DATA_PATH + '/' + dataset.fileName);
        wx.downloadFile({
            url: dataset.url,
            header: {
                'Authorization': wx.getStorageSync('authorization'),
            },
            success: function (res) {
                wx.getFileSystemManager().saveFile({
                    tempFilePath: res.tempFilePath,
                    filePath: wx.env.USER_DATA_PATH + '/' + dataset.fileName, // 本地自定义的文件名
                    success: function (res) {
                        wx.showToast({
                            icon: "none",
                            title: "下载文件成功",
                        });
                        const savedFilePath = res.savedFilePath;
                        console.log('savedFilePath', savedFilePath, res)
                        // 打开文件
                        if (['jpg', 'png', 'jpeg'].includes(fileType)) {
                            wx.previewImage({
                                current: savedFilePath,
                                urls: [savedFilePath]
                            })
                        } else {
                            wx.openDocument({
                                fileType: fileType,
                                filePath: savedFilePath
                            })
                        }
                    },
                    fail: function () {
                        wx.showToast({
                            title: '下载失败',
                            icon: "fail"
                        })
                    },
                    complete: function () {
                        wx.hideLoading()
                    }
                })
            }
        })
    },
    /**
     * 处理富文本里的图片宽度自适应
     * 1.去掉img标签里的style、width、height属性
     * 2.img标签添加style属性：max-width:100%;height:auto
     * 3.修改所有style里的width属性为max-width:100%
     * 4.去掉<br/>标签
     * 修改字体样式
     * @param html
     * @returns {void|string|*}
     */
    formatRichText(html) {
        let newContent = html.replace(/<img[^>]*>/gi, function (match, capture) {
            match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
            match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
            match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
            return match;
        });
        newContent = newContent.replace(/style="[^"]+"/gi, function (match, capture) {
            match = match
                .replace(/<p>/gi, '<p class="p_class">')
                .replace(/width:[^;]+;/gi, 'max-width:100%;')
                .replace(/width:[^;]+;/gi, 'max-width:100%;');
            return match;
        });
        newContent = newContent.replace(/<br[^>]*\/>/gi, "");
        newContent = newContent.replace(/<a>/gi, '<a class="p_class "');
        newContent = newContent.replace(/<li>/gi, '<li class="p_class "');
        newContent = newContent.replace(/\<p/gi, '<p class="p_class "');
        newContent = newContent.replace(/\<span/gi, '<span class="p_class "');
        newContent = newContent.replace(/\<img/gi, '<img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"');
        return newContent;
    }

})