/* pages/ccs/newCenter/newsList.wxss */
.root-layout{
    height: 100vh;
}
::-webkit-scrollbar{
  width: 0;
  height: 0;
  color: transparent;
}
.list-item{
    display: flex;
    margin: 10rpx;
    padding: 10rpx;
    border-top: 1rpx solid #ccc;
}
.list-item-left{
    align-items: center;
    overflow: hidden;
}
.list-item-right{
    position: relative;
    padding-left: 10rpx;
    flex: 1;
}
.title{
    font-weight: bold;
    font-size: 40rpx;
}
.date{
    font-size: 32rpx;
    color: #666;
}
.top{
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: red;
}


.tab-class {
  width: 100px;
}