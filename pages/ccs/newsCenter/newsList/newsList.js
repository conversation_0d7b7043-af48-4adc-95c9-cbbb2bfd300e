// pages/ccs/newCenter/newsList.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        listViewH:500,
        active: 0,
        tabList: [
          {name: '全部', value: 'all'},
          {name: '销售政策', value: '1'},
          {name: '系统公告', value: '2'},
          {name: '新闻资讯', value: '3'},
          {name: '促销信息', value: '4'}
        ],
        showList:[],
        searchValue: '',
        pageIndex: 1,
        pageSize: 20,
        pages: 0,
        warehouseInfo:{}, // 当前tab信息
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      let tabList = [{name: '全部', value: 'all'}]
      tabList.push(...wx.getStorageSync('dictMap').newsTopCategory)
      this.setData({
        tabList: tabList,
        topCategory: tabList[0] && tabList[0].value,
      })
      // this.loadMore();
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
      this.initPage();
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      this.onRefresh()
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    onSearchCancel(){
      this.setData({searchValue: ''})
    },
    onSearchClick(e){
      console.log(11)
      this.setData({
        searchValue: e.detail,
        pageIndex: 1,
        pages: 0,
        showList: [],
      })
      this.loadMore()
    },
    onSearchChange(e){
      this.setData({searchValue: e.detail})
      console.log('searchValue', this.data.searchValue)
    },
    chooseTab(e) {
      console.log('warehouseInfo', e.detail)
      let name = e.detail.name
      this.setData({
        topCategory: e.detail.name,
        active: name,
        pageIndex: 1,
        pages: 0,
        showList: [],
      })
      this.loadMore()
    },
    onRefresh(){
      this.setData({
        showList: [],
        pageIndex: 1,
        pages: 0,
      })
      this.loadMore()
    },
    loadMore(){
      if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
        wx.showToast({
          title: '没有更多数据了...',
          duration: 3000,
          icon: 'none'
        })
        return;
      }
      const userInfo = wx.getStorageSync('userInfo')
      console.log('userInfo', userInfo)
      let param ={
        "pageIndex": this.data.pageIndex,
        "pageSize": this.data.pageSize,
        "param": {
          // receiveUserSetsId: userInfo.setsOfBooksId,
          newsTitle: this.data.searchValue,
          topCategory: this.data.topCategory === 'all' ? undefined : this.data.topCategory,
        }
      }
      console.log(param)
      // /api/mms/news/myx/page
      App.getHttp()._post('/api/mms/news/myx/page', param, true).then(res=>{
        console.log('itemList',res, this.data.showList)
        this.setData({
          showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
          pageIndex: this.data.pageIndex + 1,
          pages: res.pages
        })
      })
    },
    itemBtnClick(e){
      let index = e.currentTarget.dataset.value // 获取传入的参数
      wx.navigateTo({
        url: `/pages/ccs/inventory/count/count?itemId=${this.data.showList[index].itemId}`,
      })
    },
    // getTabList(){
    //   console.log('jll')
    //   const param = {
    //     "pageIndex": 1,
    //     "pageSize": 999999,
    //     "param": {
    //       isUsable: 2,
    //     }
    //   }
    //   // 查询仓库
    //   App.getHttp()._post('/api/psi/baseWarehouse/page', param).then(res => {
    //     this.setData({
    //       active: 0,
    //       pageIndex: 1,
    //       tabList: res
    //     })
    //     // 查询仓库下商品
    //     this.loadMore()
        
    //   })
    // },
    initPage() {
      const query = wx.createSelectorQuery()
      query.select('#root-layout').boundingClientRect()
      query.select('#top-layout').boundingClientRect()
      query.exec((res) => {
        console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
        this.setData({
          listViewH: res[0].height - res[1].height,
        })
      })
    },
    getNewsList(){
      const loginId =  wx.getStorageSync('loginId')
      console.log(loginId)
      const param = {
        pageIndex: 1,
        pageSize: 9,
        param:{
          userLoginId: loginId
        }
      }
      App.getHttp()._post('/api/mms/news/myx/page', param).then(res => {
        this.setData({
          newsList: res,
          swiperList: this.handleSwiperList(res)
        })
      })
    },

})
