<!--pages/ccs/newCenter/newsList.wxml-->
<view class="page-layout root-layout" id="root-layout">
    <van-sticky offset-top="0" class="top-layout" id="top-layout">
        <!-- <van-search value="{{ searchValue }}" placeholder="请输入搜索关键词" bind:search="onSearch" bind:clear="onSearchCancel"  bind:change="onSearchChange"/> -->
        <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{false}}" bind:onSearchTxtClick="onSearchClick" bind:onConfirm="onSearchClick"/>
        <van-tabs tab-class="tab-class-index" active="{{ active }}" bind:change="chooseTab">
            <van-tab wx:for="{{tabList}}" wx:key="index" title="{{item.name}}" name="{{item.value}}">
            </van-tab>
        </van-tabs>
        <!-- <warehouse-tab bind:chooseTab="chooseTab" /> -->
    </van-sticky>
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
        <view class="item-layout" wx:if="{{showList.length>0}}">
            <new-carts wx:for="{{showList}}" wx:for-item="item" wx:key="index" data-value="{{index}}" itemInfo="{{item}}"></new-carts>
        </view>
        <view class="m-t-25p" wx:else>
            <no-product noneTxt="暂无数据" />
        </view>
    </listView>
</view>