// pages/ccs/moreFeatures/addr/index.js
const App=getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH:475,
    pageIndex: 1,
    pageSize: 10,
    showAddAddress: false,
    listData: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 用户ID
    this.setData({
      sourceId:options.sourceId,
      action:options.action,
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
   this.data.pageIndex =1
   this.getListData()
  },
  onReady:function(){
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    if(this.data.showAddAddress){
      query.select('#footer-layout').boundingClientRect()
      query.exec((res)=>{
        this.setData({
          listViewH:res[0].height-res[1].height
        })
      })
    }else{
      query.select('#list-layout').boundingClientRect()
      query.exec((res)=>{
        this.setData({
          listViewH:res[0].height-res[1].top-App.globalData.deviceBottomOccPx
        })
      })
    }
  },
  // 获取地址列表
  async getListData() {
    const param = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: { isUsable: 2 ,sourceId: this.data.sourceId}
    }
    App.getHttp()._post('/api/mmd/common/addr/page',param,true).then(res=>{
      if(res&&res.recordList&&res.recordList.length>0){
        this.setData({
          listData:res.recordList
        })
      }
    })
  },
  onListPullRefresh(){
    this.data.pageIndex = 1
  },
  onLoadmore(){
    this.data.pageIndex= this.data.pageIndex+1
    this.getListData()
  },
  onClickAdd(){
    wx.navigateTo({
      url: '/pages/ccs/moreFeatures/addrEdit/index',
    })
  },
  doSelect: function(e){
    let item = e.currentTarget.dataset.value // 获取传入的地址信息
    // 来自订单等页面,点击选中收货地址返回
    if (this.data.action === 'order') {
      // this.updateAddress(item)
      wx.setStorageSync('address', item)
     wx.navigateBack()
    }
  },
  toEditAddress: function(e){
    let item = e.currentTarget.dataset.value
    wx.navigateTo({
      url: '/pages/ccs/moreFeatures/addrEdit/index?id=' + item.id,
    })
  },
  
  onClickDel: function(e) {
    let item = e.currentTarget.dataset.value // 获取传入的参数
    let list = this.data.listData
    wx.showActionSheet({
      alertText:'确定要删除吗?',
      itemList: ['是'],
      itemColor:'#FF4A4D',
      success: (res)=> {
        // 先调用删除接口，成功后，进行下面的刷新操作
        App.getHttp()._post('/api/mmd/baseCustAddr/myx/delete',{
          ids:[item.id],
        }).then(res=>{
          for (let i = 0; i < list.length; i++) {
            if (list[i].id === item.id) {
              list.splice(i, 1);
              this.setData({
                listData:list
              })
              break
            }
          }
        })
      }
    })
  },
})