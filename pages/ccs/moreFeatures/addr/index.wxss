/* pages/ccs/moreFeatures/addr/index.wxss */
.root-layout{
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.04);
}
.list-layout{
  padding: 0 24rpx 24rpx;
}
.item-layout {
  background: #fff;
  border-radius: 8rpx;
}
.item-layout:nth-of-type(n+1) {
  margin-top: 24rpx;
}
.address-item {
  padding: 32rpx 24rpx;
}
.address-item .left {
  padding-right: 24rpx;

}
.address-item .left image {
  width: 48rpx;
  height: 48rpx;
}
.address-item .right {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
}
.address-item .right .right-left .top {
  display: flex;
  font-size: 28rpx;
  line-height: 44rpx;
  color:rgba(0,0,0,0.85);
}
.address-item .right .right-left .top .name {
  font-weight: 500;
}
.address-item .right .right-left .top .phone {
  margin: 0 16rpx 0 16rpx;
}
.address-item .right .right-left .top .tag {
  padding: 0 12rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: #FAAE16;
  text-align: center;
  background-color: #FFFBE6;
  border: 1rpx solid #FAAE16;
  border-radius: 8rpx;
}
.address-item .right .right-left .bottom {
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: rgba(0, 0, 0, 0.75);
}
.address-item .right .right-right {
  padding-left: 24rpx;
}
.address-item .right .right-right image {
  width: 48rpx;
  height: 48rpx;
}



.swipe-right-del {
  position: relative;
  width:140rpx;
  height: 100%;
  background-color: #FF4A4D;
  color: #fff;
  text-align: center;
  line-height: 100%;
}
.swipe-right-del .delete-button {
  position: absolute;
  padding: 0;
  margin: 0;
  width: 100%;
  top: 50%;
  transform: translate(0, -50%);
  color: #fff;
  border: 0;
  outline-style: none;
  outline: 0;
  background-color: #FF4A4D;
}

.swipe-right-del .delete-button::after {
  border: none;
}

.warining-layout {
  height: 80rpx;
  background: #FFFBE6;
  opacity: 0.7;
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 72rpx;
  padding-left: 36rpx;
}
.warining-icon{
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}