<view class="page root-layout" id="page-layout">
  <!-- <view wx:if="{{!showAddAddress}}" class="warining-layout flex align-items-center" id="warining-layout">
    <image class="warining-icon" src="/asset/imgs/purchase/warning.png"></image>
    <text class=" font-s24-lh44">提示：厂商采购收货地址请联系供货商系统维护！～</text>
  </view> -->
    
  <listView viewHeightPx="{{listViewH}}" id="list-layout" bind:pullRefresh="onListPullRefresh" bind:loadmore="onLoadmore" >
    <view class="list-layout">
      <view wx:for="{{listData}}" data-item="{{item}}" wx:key="addrId" class="item-layout">
          <van-swipe-cell right-width="{{ 82 }}" >
          <view class="address-item" bindtap="doSelect" data-value="{{item}}">
            <!-- <view wx:if="{{item.isDefault === 2}}" class="left">
              <van-checkbox checked-color="#00b9c3" class="image" value="{{item.isDefault}}" icon-size="36rpx">
              </van-checkbox>
            </view> -->
            <view class="right flex">
              <view class="right-left flexbox">
                <view class="top">
                  <view class="name">
                    {{ item.name }}
                  </view>
                  <view class="phone">
                    {{ item.phone }}
                  </view>
                  <view wx:if="{{item.isDefault === 2}}" class="tag">
                    默认
                  </view>
                </view>
                <view class="bottom">
                  {{ item.contactAddr || ''}}
                </view>
              </view>
              <view wx:if="{{showAddAddress}}" class="right-right" catchtap="toEditAddress" data-value="{{item}}">
                <image src="/asset/svgs/editor.svg" mode='scaleToFill' />
              </view>
            </view>
          </view>
          <view slot="right" class="swipe-right-del" bindtap="onClickDel" data-value="{{item}}">
            <button class="delete-button">
              删除
            </button>
          </view>
        </van-swipe-cell>
      </view>
      <view class="m-t-25p" wx:if="{{listData.length==0}}">
            <no-product  />
        </view>
    </view>
  </listView>
  
  <footer-button wx:if="{{showAddAddress}}" btnWord="新增收货地址" bind:mainClick="onClickAdd" id="footer-layout"></footer-button>
</view>