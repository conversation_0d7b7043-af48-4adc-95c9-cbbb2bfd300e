// pages/ccs/moreFeatures/addrEdit/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showPicker: false,
    isDefault: true,
    id: null,
    areAStringBuffer: '',
    areA: [],
    phone: '',
    contact: '',
    detail: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id){
      this.setData({
        id: options.id
      })
      this.initAddr()
    }else{
      wx.setNavigationBarTitle({
        title: '新增收货地址',
      })
    }
  },
  initAddr(){
    const param = {
      pageIndex: 1,
      pageSize: 1,
      param: {id:this.data.id, sourceId: wx.getStorageSync('custInfo').custId}
    }
    App.getHttp()._post('/api/mmd/baseCustAddr/myx/page',param).then(res=>{
      if(res&&res.length>0){
        let tempBuffer = []
        let tempAreA = []
        tempBuffer.push(res[0].provinceName)
        tempBuffer.push(res[0].cityName)
        tempBuffer.push(res[0].districtName)
        tempBuffer.push(res[0].townName)
        tempAreA.push({id:res[0].provinceId,name:res[0].provinceName})
        tempAreA.push({id:res[0].cityId,name:res[0].cityName})
        tempAreA.push({id:res[0].districtId,name:res[0].districtName})
        tempAreA.push({id:res[0].townId,name:res[0].townName})
        this.setData({
          isDefault: res[0].isDefault==2,
          phone: res[0].phone,
          contact: res[0].name,
          detail: res[0].addr,
          areA:tempAreA,
          areAStringBuffer:tempBuffer.map((v) => {
            return v
          }).join(' ')
        })
      }
    })
  },
  openCitySelect: function () {
    // 弹出地址选择
    this.setData({
      showPicker: true
    })
  },
  onClose: function () {
    // 关闭地址选择框
    this.setData({
      showPicker: false
    })
  },

  citySelectChange(e) {
    let res = e.detail
    this.setData({
      showPicker: false
    })
    if (res) {
      this.setData({
        areA: res,
        areAStringBuffer: res.map((v) => {
          return v.name
        }).join(' ')
      })
    }
  },

  onDefaultChange: function ({detail}) {
    this.setData({
      isDefault: detail
    });
  },
  doSubmit: function () {
    let toastTitle = ''
    if (!this.data.contact) {
      toastTitle = '请填写收货姓名'
    } else if (!this.data.phone||this.data.phone.length!=11) {
      toastTitle = '请输入正确的手机号码'
    } else if (this.data.areA.length < 3) {
      toastTitle = '请选择所在地区,至少到区(县)'
    } else if (!this.data.detail) {
      toastTitle = '请选择输入详情地址'
    }
    if (toastTitle) {
      wx.showToast({
        title: toastTitle,
        duration: 3000,
        icon: 'none'
      })
      return
    }
    let regionArr = this.data.areA.map(region=>region.id)
    App.getHttp()._post(`/api/mmd/baseCustAddr/myx/${this.data.id?'update':'create'}`, {
      isDefault: this.data.isDefault ? 2 : 1,
      name: this.data.contact,
      phone: this.data.phone,
      provinceId: this.data.areA[0].id,
      districtId: this.data.areA[2].id,
      townId: this.data.areA[3].id,
      addr: this.data.detail,
      id: this.data.id,
      cityId: this.data.areA[1].id,
      regionIds: regionArr.join(','),
      sourceId: this.data.sourceId||wx.getStorageSync('custInfo').custId,
      sourceCode: this.data.sourceCode||wx.getStorageSync('custInfo').custCode,
      sourceName: this.data.sourceName||wx.getStorageSync('custInfo').custName,
      sourceType: this.data.sourceType||wx.getStorageSync('custInfo').custType,
      isUsable: 2,
    }).then(res => {
      wx.showToast({
        title: '保存成功',
        icon:'success',
        duration:'2000',
        complete:function(){
          wx.navigateBack({
            delta: 1,
          })
        }
      })
    })
  }
})