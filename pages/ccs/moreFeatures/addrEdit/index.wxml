<!--pages/ccs/moreFeatures/addrEdit/index.wxml-->

<view class="page addr-edit-manage">
  <view :border="false">
    <van-field model:value="{{contact}}" border="{{false}}" label="收货人" placeholder="请输入" required maxlength="11"
      input-align="right" />
    <van-field model:value="{{phone}}" border="{{false}}" label="手机号码" placeholder="请输入" required maxlength="11"
      type="digit" clearable input-align="right" />
    <van-field model:value="{{areAStringBuffer}}" border="{{false}}" label="所在地区" readonly is-link placeholder="请选择"
      required clickable input-align="right" bindtap="openCitySelect" />
    <van-field model:value="{{detail}}" border="{{false}}" rows="2" autosize label="详细地址" type="textarea" clearable
      placeholder="街道、楼牌号等" required maxlength="50" input-align="right" />
  </view>
  <view class="flex align-items-center default-layout">
    <view class="flexbox">
      <view class="default-layout_theme">
        设置默认地址
      </view>
      <view class="default-layout_des">
        提醒：每次下单会默认推荐该地址
      </view>
    </view>
    <van-switch checked="{{isDefault}}" bind:change="onDefaultChange" size="24px" active-color="#52BB26" />
  </view>
  <van-popup show="{{showPicker}}" bind:close="onClose" class="van-popup-city-select" round position="bottom"
    :lock-scroll="true">
    <city-select bind:addressChange="citySelectChange" bind:closeCitySelect="onClose" />
  </van-popup>
  <footer-button btnWord="保存" bind:mainClick="doSubmit" />
</view>