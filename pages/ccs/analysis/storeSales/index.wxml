<view class="root-layout bg-04">
    <scroll-view style="height: {{windowHeight}}rpx" scroll-y  scroll-with-animation="true">
        <view class="scroll-item" wx:for="{{storeList}}" wx:key="index">
            <view class="scroll-item-head" data-item="{{item}}" bindtap="onClickToAnalysis">
                <view class="scroll-item-head-title">{{item.custName}}</view>
                <van-icon size="16" color="#242424" name="arrow"></van-icon>
            </view>
            <view class="scroll-content">
                <view class="analysis-item">
                    <view class="analysis-item-num">{{item.sumYesterdayMoney ? item.sumYesterdayMoney + '万元' : 0}}</view>
                    <view class="analysis-item-text">昨日销售额</view>
                </view>
                <view class="analysis-item">
                    <view class="analysis-item-num">{{item.sumYesterdayQty || 0}}</view>
                    <view class="analysis-item-text">昨日销量</view>
                </view>
                <view class="analysis-item">
                    <view class="analysis-item-num">{{item.sumYearMoney ? item.sumYearMoney + '万元' : 0}}</view>
                    <view class="analysis-item-text">年累计销售额</view>
                </view>
            </view>
        </view>
        <view class="m-t-25p" wx:if="{{storeList.length == 0}}">
          <noneView ></noneView>
        </view>
    </scroll-view>
</view>