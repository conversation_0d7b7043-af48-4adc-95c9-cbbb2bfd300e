// pages/ccs/analysis/storeSales/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    windowHeight: 1200,
    storeList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const that = this
    wx.getSystemInfo({
      success(res) {
        that.setData({
          windowHeight: res.windowHeight * App.globalData.pxToRpxRatio
        })
      }
    })
    this.getStoreList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取门店数据
  getStoreList() {
    const url = '/api/psi/salesAnalysis/myx/query/cust'
    const supInfo = wx.getStorageSync('supInfo')
    App.getHttp()._post(url, {
      setsOfBooksId: supInfo.setsOfBooksId,
      custType: 7
    }).then(res => {
      this.setData({
        storeList: res
      })
    })
  },
  // 跳转销售分析
  onClickToAnalysis(e) {
    const item = e.currentTarget.dataset.item
    wx.setStorageSync('storeInfo', { custId: item.custId, custName: item.custName })
    wx.navigateTo({
      url: '/pages/ccs/analysis/salesAnalysis/index'
    })
  }
})