<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
  <view class="inventory-head" bindtap="onClickShowFilter">
    <view class="inventory-head-text">筛选</view>
    <van-icon name="/asset/imgs/purchase/filler-filled.png" size="16"></van-icon>
  </view>
  <inventoryAnalysis marginStyle="{{true}}" hideChart="{{hideChart}}" showTitle="{{false}}" />
  <view class="block-layout time-slot">
    <view class="time-slot-type">
      <view class="time-slot-type-title">时间段(单选):</view>
      <view class="time-slot-type-item {{activeTime == 1 ? 'active-time-type' : ''}}" data-type="{{1}}" bindtap="onClickChangeTime">
        今日
      </view>
      <view class="time-slot-type-item {{activeTime == 2 ? 'active-time-type' : ''}}" data-type="{{2}}" bindtap="onClickChangeTime">
        近7天
      </view>
      <view class="time-slot-type-item {{activeTime == 3 ? 'active-time-type' : ''}}" data-type="{{3}}" bindtap="onClickChangeTime">
        近30天
      </view>
      <view class="time-slot-type-item {{activeTime == 4 ? 'active-time-type' : ''}}" data-type="{{4}}" bindtap="onClickChangeTime">
        自定义
      </view>
    </view>
    <view class="time-slot-data">
      <view class="time-slot-data-item" wx:for="{{totalDataList}}" wx:key="index">
        <view class="time-slot-data-amount">{{item.billQty}}</view>
        <view class="time-slot-data-title">{{item.billType == 1 ? '出库数量' : '入库数量'}}</view>
      </view>
    </view>
  </view>
  <view class="block-layout" id="block-layout">
    <van-sticky>
      <van-tabs active="{{ activeTab }}" bind:change="onChangeTab">
        <van-tab title="汇总" name="sum">
          <view class="dimension-type">
            <view class="dimension-type-item {{activeDimension == 1 ? 'active-dimension-type' : ''}}" data-type="{{1}}" bindtap="onChangeDimension">
              商品维度视图
            </view>
            <view class="dimension-type-item {{activeDimension == 2 ? 'active-dimension-type' : ''}}" data-type="{{2}}" bindtap="onChangeDimension">
              仓库维度视图
            </view>
          </view>
        </van-tab>
        <van-tab title="流水" name="stream"></van-tab>
      </van-tabs>
    </van-sticky>
  </view>
  <scroll-view class="scroll-layout" style="height: {{scrollHeight}}rpx" bindscrolltolower="onLoadMore" scroll-y>
    <block wx:for="{{dataList}}" wx:key="index">
      <view class="block-layout">
        <block wx:if="{{activeTab == 'sum'}}">
          <view class="sum-head">
            <view class="sum-head-text">
              {{item.itemName + item.itemCode || item.warehouseName}}
            </view>
            <view class="sum-head-specs" wx:if="{{item.specs}}">
              {{item.specs}}
            </view>
          </view>
          <view class="sum-content">
            <view class="data-blcok">
              <view class="data-cotent-amount">{{item.inbillQty}}</view>
              <view class="data-cotent-title">入库数</view>
            </view>
            <view class="data-blcok">
              <view class="data-cotent-amount">{{item.outbillQty}}</view>
              <view class="data-cotent-title">出库数</view>
            </view>
            <view class="data-blcok">
              <view class="data-cotent-amount">{{item.leftQty}}</view>
              <view class="data-cotent-title">结存数</view>
            </view>
          </view>
        </block>
        <block wx:else>
          <view class="stream-head">
            <view class="head-left">
              <view class="bill-no">{{item.billNo}}</view>
              <view class="bill-date">{{item.billdate}}</view>
            </view>
            <view class="bill-type">{{item.billtypeTxt}}</view>
          </view>
          <view class="stream-content" wx:for="{{item.itemList}}" wx:key="itemId" wx:for-item="billItem">
            <view class="stream-content-left">
              <view class="sum-head-text">{{billItem.itemName}}</view>
              <view class="sum-head-specs">{{billItem.specs}}</view>
              <view class="stream-warehouse">{{billItem.warehouseName}}</view>
            </view>
            <view class="stream-content-right">
              <view class="stream-warehouse">{{wxsUtil.formatBillType(item.billtype)}}</view>
              <view class="stream-content-amount">{{billItem.billQty}}</view>
            </view>
          </view>
        </block>
      </view>
    </block>
  </scroll-view>
  <!-- 筛选弹层 -->
  <van-popup show="{{ showFilter }}" position="right" custom-style="height: 100%;width: 612rpx" bind:click-overlay="onCloseFilterAfter">
    <view class="popup-block">
      <view class="filter-item">
        <view class="filter-item-head">类型</view>
        <view class="popup-grid">
          <van-grid column-num="3" border="{{false}}">
            <van-grid-item use-slot content-class="grid-content" wx:for="{{typeList}}" wx:key="index">
              <view class="grid-item {{item.active ? 'active-filter-type' : ''}} {{activeFilterType.join(',')}}" data-type="{{item.value}}" bindtap="onClickFilterType">
                {{item.name}}
              </view>
            </van-grid-item>
          </van-grid>
        </view>
      </view>
      <view class="filter-item">
        <view class="filter-item-head">仓库</view>
        <view class="popup-grid">
          <van-grid column-num="3" border="{{false}}">
            <van-grid-item use-slot content-class="grid-content" wx:for="{{warehouseList}}" wx:key="index">
              <view class="grid-warehouse-item {{item.active ? 'active-filter-type' : ''}}" data-id="{{item.id}}" bindtap="onClickFilterWarehouse">
                {{item.name}}
              </view>
            </van-grid-item>
          </van-grid>
        </view>
      </view>
      <view class="popup-footer">
        <view class="popup-footer-btn btn-reset" bindtap="onClickReset">重置</view>
        <view class="popup-footer-btn btn-confirm" bindtap="onClickConfirm">确定</view>
      </view>
    </view>
  </van-popup>
  <!-- 日历控件 -->
  <van-calendar type="range" show="{{ showCalendar }}" min-date="{{ minDate }}" max-date="{{ maxDate }}" color="#00b9c3" id="calendar-block" default-date="{{defaultDate}}" bind:confirm="onConfirmDate" bind:close="onCloseCalendar" />
</view>