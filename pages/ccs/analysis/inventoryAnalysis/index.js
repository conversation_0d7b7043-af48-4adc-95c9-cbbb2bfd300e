// pages/ccs/analysis/inventoryAnalysis/index.js
const App = getApp()
import dayjs from "dayjs"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeTime: 1, // 时间维度
    activeTab: 'sum', // 汇总 或者 流水
    activeDimension: 1, // 商品维度 或者 仓库维度
    dataList: [],
    showFilter: false,
    typeList: [
      {
        name: '采购',
        value: '1',
        active: false
      },
      {
        name: '销售',
        value: '2',
        active: false
      },
      {
        name: '盘点',
        value: '3',
        active: false
      },
      {
        name: '调拨',
        value: '4',
        active: false
      },
      {
        name: '其他',
        value: '5',
        active: false
      }
    ],
    warehouseList: [],
    activeFilterType: [],
    activeFilterWarehouse: [],
    pageIndex: 1,
    pageSize: 10,
    totalDataList: [],
    showCalendar: false,
    maxDate: new Date(dayjs()).getTime(),
    minDate: new Date(dayjs().subtract(1, 'year')).getTime(),
    dateList: [],
    defaultDate: [new Date().getTime(), new Date().getTime()],
    scrollHeight: 1104,
    hideChart: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getWarehouseList() // 获取仓库信息
    this.getTotalData() // 出入统计数据
    this.getData() // 汇总/流水 数据
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#block-layout').boundingClientRect()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - App.globalData.deviceBottomOccPx
          that.setData({
            scrollHeight: scrollH2 * App.globalData.pxToRpxRatio,
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取仓库
  getWarehouseList() {
    const warehouse = wx.getStorageSync('dictMap').warehouseType
    if(warehouse) {
      warehouse.forEach(item => {
        item.active = false
      })
      this.setData({
        warehouseList: warehouse
      })
    } else {
      this.setData({
        warehouseList: []
      })
    }
  },
  // 切换时间段
  onClickChangeTime(e) {
    const type = e.currentTarget.dataset.type
    if(type == 4) {
      this.setData({
        activeTime: type,
        showCalendar: true
      })
    } else {
      this.setData({
        activeTime: type,
        pageIndex: 1,
        dataList: []
      })
      this.getData()
      this.getTotalData()
    }
  },
  // 切换汇总或者流水
  onChangeTab(e) {
    const name = e.detail.name
    if(name == 'stream') {
      // 切换成流水后 把汇总的维度视图重置为第一个
      this.setData({
        activeDimension: 1
      })
    }
    this.setData({
      activeTab: name,
      pageIndex: 1,
      dataList: []
    })
    this.getData()
  },
  // 切换维度视图
  onChangeDimension(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      activeDimension: type,
      pageIndex: 1,
      dataList: []
    })
    this.getData()
  },
  // 获取汇总或者流水数据
  getData() {
    const url = this.data.activeTab == 'sum' ? '/api/psi/currentInvSearch/myx/inventory/collect' : '/api/psi/currentInvSearch/myx/inventory/flow'
    const params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        billTypeFlag: this.data.activeFilterType.join('#'),
        // warehouseId: this.data.activeFilterWarehouse,
        warehouseIdList: this.data.activeFilterWarehouse,
        timeType: this.data.activeTime,
        groupType: this.data.activeDimension
      }
    }
    if(this.data.activeTime == 4) {
      params.param.startDate = this.data.dateList.length > 0 ?  this.data.dateList[0] : ''
      params.param.endDate = this.data.dateList.length > 0 ?  this.data.dateList[1] : ''
    }
    App.getHttp()._post(url, params).then(res => {
      let dataList = this.data.pageIndex == 1 ? [] : this.data.dataList
      if(res && res.length > 0) {
        this.setData({
          dataList: dataList.concat(res)
        })
      }
    })
  },
  // 打开筛选
  onClickShowFilter() {
    this.setData({
      showFilter: true,
      hideChart: true
    })
  },
  // 关闭筛选
  onCloseFilterAfter() {
    this.setData({
      hideChart: false,
      showFilter: false
    })
  },
  // 修改筛选里面类型参数
  onClickFilterType(e) {
    const type = e.currentTarget.dataset.type
    let filterArr = this.data.activeFilterType
    let originArr = this.data.typeList
    let idx = this.data.typeList.findIndex(item => item.value == type)
    let isHas = this.data.activeFilterType.findIndex(item => item == type)
    originArr[idx].active = isHas != -1 ? false : true
    if(isHas != -1) {
      filterArr.splice(isHas, 1)
    } else {
      filterArr.push(type)
    }
    this.setData({
      activeFilterType: filterArr,
      typeList: originArr
    })
  },
  // 修改筛选里面的仓库
  onClickFilterWarehouse(e) {
    const id = e.currentTarget.dataset.id
    let originArr = this.data.warehouseList
    let idx = this.data.warehouseList.findIndex(item => item.id == id)
    let filterArr = this.data.activeFilterWarehouse
    let isHas = this.data.activeFilterWarehouse.findIndex(item => item == id)
    originArr[idx].active = isHas != -1 ? false : true
    if(isHas != -1) {
      filterArr.splice(isHas, 1)
    } else {
      filterArr.push(id)
    }
    this.setData({
      activeFilterWarehouse: filterArr,
      warehouseList: originArr
    })
  },
  // 重置
  onClickReset() {
    let typeList = this.data.typeList
    let warehouseList = this.data.warehouseList
    typeList.forEach(item => {
      item.active = false
    })
    warehouseList.forEach(item => {
      item.active = false
    })
    this.setData({
      activeFilterType: [],
      activeFilterWarehouse: [],
      typeList,
      warehouseList
    })
  },
  // 确认搜索条件
  onClickConfirm() {
    this.setData({
      showFilter: false,
      hideChart: false,
      pageIndex: 1,
      dataList: []
    })
    this.getData()
    this.getTotalData()
  },
  // 加载更多
  onLoadMore() {
    this.setData({
      pageIndex: this.data.pageIndex + 1
    })
    this.getData()
  },
  // 出入统计数据
  getTotalData() {
    const url = '/api/psi/currentInvSearch/myx/inventory/inOutStatistics'
    const params = {
      billTypeFlag: this.data.activeFilterType.join('#'),
      timeType: this.data.activeTime,
      // warehouseId: this.data.activeFilterWarehouse,
      warehouseIdList: this.data.activeFilterWarehouse
    }
    if(this.data.activeTime == 4) {
      params.startDate = this.data.dateList.length > 0 ?  this.data.dateList[0] : ''
      params.endDate = this.data.dateList.length > 0 ?  this.data.dateList[1] : ''
    }
    App.getHttp()._post(url, params).then(res => {
      if(res && res.length > 0) {
        this.setData({
          totalDataList: res
        })
      }
    })
  },
  onConfirmDate(e) {
    const dateList = e.detail
    let list = []
    let defaultList = []
    dateList.forEach(item => {
      list.push(dayjs(item).format('YYYY-MM-DD'))
      defaultList.push(new Date(item).getTime())
    })
    this.setData({
      dateList: list,
      showCalendar: false,
      dataList: [],
      pageIndex: 1
    })
    this.getTotalData()
    this.getData()
  },
  onCloseCalendar() {
    this.setData({
      showCalendar: false
    })
  },
  // 获取仓库
  getWarehouseList() {
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex:1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      this.setData({
        warehouseList: res
      })
    })
  },
})