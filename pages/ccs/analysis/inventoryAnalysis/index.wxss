/* pages/ccs/analysis/inventoryAnalysis/index.wxss */
.root-layout {
    overflow: auto;
}
.block-layout {
    margin: 24rpx 24rpx 0 24rpx;
    background-color: #fff;
}
.inventory-head {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 24rpx 32rpx;
}
.inventory-head-text {
    font-size: 26rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #3D3D3D;
    line-height: 36rpx;
}
.time-slot-type {
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: 1px solid #E6E6E6;
}
.time-slot-type-title {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 30rpx;
}
.time-slot-type-item {
    background: #F0F0F0;
    border-radius: 28rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #616161;
    line-height: 40rpx;
    padding: 8rpx 16rpx;
}
.active-time-type {
    background: #E7F2FF;
    color: #00b9c3;
}
.time-slot-data {
    padding: 32rpx 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
}
.time-slot-data-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
}
.time-slot-data-amount {
    font-size: 32rpx;
    color: #242424;
    line-height: 32rpx;
}
.time-slot-data-title {
    font-size: 24rpx;
    color: #707070;
    line-height: 24rpx;
    margin-top: 16rpx;
}
.dimension-type {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 24rpx 32rpx;
    background-color: #fff;
}
.dimension-type-item {
    background: #F0F0F0;
    border-radius: 28rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #616161;
    line-height: 40rpx;
    padding: 8rpx 84rpx;
}
.active-dimension-type {
    color: #00b9c3;
    background:  #E7F2FF;
}
.sum-head {
    padding: 32rpx 24rpx;
    border-bottom: 1px solid #E6E6E6;
}
.sum-head-text {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #242424;
    line-height: 36rpx;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
}
.sum-head-specs{
  font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #8A8A8A;
    line-height: 36rpx;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
}
.sum-content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 32rpx 0;
}
.data-blcok {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
}
.data-cotent-amount {
    font-size: 32rpx;
    color: #242424;
    line-height: 32rpx;
}
.data-cotent-title {
    font-size: 24rpx;
    color: #707070;
    line-height: 24rpx;
    margin-top: 16rpx;
}
.stream-head {
    background-color: #fff;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #E6E6E6;
}
.head-left {
    margin-right: 24rpx;
}
.bill-no {
    font-size: 28rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 36rpx;
}
.bill-date {
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 24rpx;
    margin-top: 16rpx;
}
.bill-type {
    background: #FFFBE6;
    border-radius: 8rpx;
    border: 1px solid #FAAE16;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #FAAE16;
    line-height: 24rpx;
    padding: 8rpx 12rpx;
}
.stream-content {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 24rpx 32rpx;
}
.stream-content-left {
    width: 500rpx;
}
.stream-content-right {
    text-align: right;
    display: flex;
}
.stream-warehouse {
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 24rpx;
}
.stream-content-amount {
    font-size: 24rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 24rpx;
    margin-left: 8rpx;
}
.popup-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 8rpx 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-top: 1px solid #E6E6E6;
}
.popup-footer-btn {
    padding: 16rpx 100rpx;
    font-size: 32rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 48rpx;
    border-radius: 8rpx;
}
.btn-reset {
    color: #242424;
    border: 1px solid #DBDBDB;
}
.btn-confirm {
    background: #00b9c3;
    color: #FEFFFE;
}
.filter-item-head {
    padding: 32rpx;
    font-size: 32rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #3D3D3D;
    line-height: 32rpx;
}
.popup-grid {
    padding: 0 24rpx;
}
.grid-content {
    padding: 0!important;
    margin-bottom: 32rpx;
}
.grid-item {
    background: #F0F0F0;
    border-radius: 28rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #616161;
    line-height: 40rpx;
    width: 172rpx;
    padding: 8rpx 0;
    text-align: center;
}
.grid-warehouse-item {
    background: #F0F0F0;
    border-radius: 28rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #616161;
    line-height: 40rpx;
    padding: 8rpx 24rpx;
    text-align: center;
}
.active-filter-type {
    color: #00b9c3;
    background:  #E7F2FF;
}
.scroll-layout {
    padding-bottom: 24rpx;
}