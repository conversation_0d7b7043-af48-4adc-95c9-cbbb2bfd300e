.root-layout {
  height: 100vh;
}
.block-title {
  font-size: 28rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 28rpx;
  padding: 32rpx 24rpx;
  border-bottom: 1px solid #e6e6e6;
}
.block-layout {
  background-color: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 8rpx;
}
.chooseCust {
  padding: 32rpx;
  display: flex;
  align-items: center;
}
.chooseCust .title {
    font-size: 32rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #3D3D3D;
    line-height: 32rpx;
}
.yesterday-block .analysis {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 16rpx;
}
.yesterday-block .analysis-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
}
.yesterday-block .analysis-item-text {
  font-size: 24rpx;
  color: #707070;
  line-height: 24rpx;
  margin-top: 16rpx;
}
.yesterday-block .analysis-item-num {
  font-size: 32rpx;
  color: #242424;
  line-height: 32rpx;
}
.time-slot {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  justify-content: space-around;
}
.time-slot .time-slot-title {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 30rpx;
}
.time-slot .time-item {
  background: #f0f0f0;
  border-radius: 28rpx;
  padding: 8rpx 28rpx;
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #616161;
  line-height: 40rpx;
}
.time-slot .time-active {
  background: #e7f2ff;
  color: #00b9c3;
}
.sales-overview .overview-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 24rpx;
}
.sales-overview .overview-content-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: PingFang SC-Regular, PingFang SC;
}
.sales-overview .price {
  font-size: 32rpx;
  font-weight: 400;
  color: #242424;
  line-height: 32rpx;
}
.sales-overview .price-type {
  font-size: 24rpx;
  font-weight: 400;
  color: #707070;
  line-height: 24rpx;
  margin: 16rpx 0;
}
.sales-overview .percent {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  line-height: 24rpx;
  border-top: 1px solid #e6e6e6;
  padding-top: 24rpx;
}
.sales-overview .percent-item-text {
  font-weight: 400;
  color: #bdbdbd;
  margin-right: 8rpx;
}
.sales-overview .percent-item-num {
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
}
.sales-overview .percent-item:nth-child(1) {
  margin-right: 16rpx;
}
.percent-item .round {
  color: #52c718;
}
.percent-item .same {
  color: #ff4a4d;
}
.trend-analysis .canvas-box {
    width: 100%;
    height: 500rpx;
    margin: 0 24rpx;
}
