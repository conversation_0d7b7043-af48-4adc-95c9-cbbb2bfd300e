// pages/ccs/analysis/salesAnalysis/index.js
import * as echarts from "../../echarts/ec-canvas/echarts.min";
import dayjs from "dayjs";
const App = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    storeInfo: {},
    analysisData: {},
    analysisDataYes: {},
    activeKey: "money",
    timeType: 1,
    ec: {
      lazyLoad: true,
    },
    trendAnalysisData: [],
    defaultDate: [new Date().getTime(), new Date().getTime()],
    dateList: [],
    showCalendar: false,
    maxDate: new Date(dayjs()).getTime(),
    minDate: new Date(dayjs().subtract(1, 'year')).getTime()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const store = wx.getStorageSync('storeInfo')
    if(store) {
      this.setData({
        storeInfo: store
      })
    }
    // 初始化数据
    this.setData({
      timeType: 1
    })
    // 请求昨日销售数据
    this.getSalesAnalysisYesterday()
    // 请求门店的销售分析数据
    this.getSalesAnalysis();
    // 获取趋势分析的数据
    this.getTrendAnalysis()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  getSalesAnalysisYesterday() {
    const url = '/api/psi/salesAnalysis/myx/query/total'
    const supInfo = wx.getStorageSync('supInfo')
    App.getHttp()._post(url, {
      setsOfBooksId: supInfo.setsOfBooksId,
      custId: this.data.storeInfo.custId && this.data.storeInfo.custId != 0 ? this.data.storeInfo.custId : '',
    }).then(res => {
      this.setData({
        analysisDataYes: res
      })
    })
  },
  getSalesAnalysis() {
    const url = '/api/psi/salesAnalysis/myx/query/detail'
    const supInfo = wx.getStorageSync('supInfo')
    let billStartDate = ''
    let billEndDate = dayjs().format('YYYY-MM-DD')
    if(this.data.timeType == 1) {
      billStartDate = dayjs(billEndDate).subtract(6, 'day').format('YYYY-MM-DD')
    } else if(this.data.timeType == 2) {
      billStartDate = dayjs(billEndDate).subtract(29, 'day').format('YYYY-MM-DD')
    } else {
      // 自定义时间段 交互未确定
      if(this.data.dateList.length > 0) {
        billStartDate = this.data.dateList[0]
        billEndDate = this.data.dateList[1]
      }
    }
    App.getHttp()._post(url, {
      setsOfBooksId: supInfo.setsOfBooksId,
      custId: this.data.storeInfo.custId && this.data.storeInfo.custId != 0 ? this.data.storeInfo.custId : '',
      billStartDate,
      billEndDate,
      // custType: 7 BUG2023101900214
    }).then(res => {
      this.setData({
        analysisData: res
      })
    })
  },
  onClickTime(e) {
    const type = e.currentTarget.dataset.time;
    if(type == 3) {
      this.setData({
        timeType: type,
        showCalendar: true,
        dateList: []
      });
    } else {
      this.setData({
        timeType: type,
        dateList: []
      });
      this.getSalesAnalysis()
      this.getTrendAnalysis()
    }
  },
  initChart() {
    const that = this;
    this.selectComponent("#trend-chart").init((canvas, width, height, dpr) => {
      // 初始化图表
      let variable = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr, // 像素
      });
      variable.setOption(that.optionFunc());
      return variable; //一定要return 否则展示会有问题
    });
  },
  optionFunc(chartData) {
    let option = {
      grid: {
        left: "1%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: this.data.trendAnalysisData.map(item => (item.confirmDate)),
      },
      yAxis: {
        type: "value",
        name: this.data.activeKey == 'money' ? "金额(万元)" : '数量',
      },
      series: [
        {
          data: this.data.activeKey == 'money' ? this.data.trendAnalysisData.map(item => (item.salesAmount)) : this.data.trendAnalysisData.map(item => (item.salesQty)),
          type: "line",
          smooth: true,
          showSymbol: false,
          emphasis: {
            disabled: true,
          },
          lineStyle: {
            normal: {
              color: {
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "#02D1FF", // 0% 处的颜色
                  },
                  { offset: 0.5, color: "#199FFF" },
                  {
                    offset: 1,
                    color: "#1D18FF", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          areaStyle: {
            // 折现下是否填充
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#e7f3ff", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#fff", // 100% 处的颜色
                },
              ],
              global: false,
            },
          },
        },
      ],
    };
    console.log('option', option)
    return option;
  },
  // 修改销售类型
  onChangeType(e) {
    this.setData({
      activeKey: e.detail.name
    })
    this.initChart()
  },
  // 跳转选择门店
  onClickToStore() {
    wx.navigateTo({
      url: '/pages/ccs/analysis/storeList/index'
    })
  },
  getTrendAnalysis() {
    const url = '/api/psi/salesAnalysis/myx/query/trend/analysis'
    const supInfo = wx.getStorageSync('supInfo')
    let billStartDate = ''
    let billEndDate = dayjs().format('YYYY-MM-DD')
    if(this.data.timeType == 1) {
      billStartDate = dayjs(billEndDate).subtract(6, 'day').format('YYYY-MM-DD')
    } else if(this.data.timeType == 2) {
      billStartDate = dayjs(billEndDate).subtract(29, 'day').format('YYYY-MM-DD')
    } else {
      // 自定义时间段 交互未确定
      if(this.data.dateList.length > 0) {
        billStartDate = this.data.dateList[0]
        billEndDate = this.data.dateList[1]
      }
    }
    const params = {
      setsOfBooksId: supInfo.setsOfBooksId,
      custId: this.data.storeInfo.custId && this.data.storeInfo.custId != 0 ? this.data.storeInfo.custId : '',
      billStartDate,
      billEndDate,
      // custType: 7 BUG2023101900214
    }
    App.getHttp()._post(url, params).then(res => {
      if(res && res.length > 0) {
        this.setData({
          trendAnalysisData: res
        })
      }
      this.initChart()
      // setTimeout(() => {
      //   this.initChart()
      // }, 1000)
    })
  },
  onConfirmDate(e) {
    const dateList = e.detail
    let list = []
    let defaultList = []
    dateList.forEach(item => {
      list.push(dayjs(item).format('YYYY-MM-DD'))
      defaultList.push(new Date(item).getTime())
    })
    this.setData({
      dateList: list,
      showCalendar: false
    })
    this.getSalesAnalysis()
    this.getTrendAnalysis()
  },
  onCloseCalendar() {
    this.setData({
      showCalendar: false
    })
  }
});
