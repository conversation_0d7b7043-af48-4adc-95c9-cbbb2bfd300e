<view class="root-layout bg-04">
    <view class="chooseCust" bindtap="onClickToStore">
        <view class="title">{{storeInfo.custName}}</view>
        <van-icon size="16" name="arrow-down" />
    </view>
    <view class="yesterday-block block-layout">
        <view class="block-title">昨日概况</view>
        <view class="analysis">
            <view class="analysis-item">
                <view class="analysis-item-num">{{analysisDataYes.sumYesterdayMoney ? analysisDataYes.sumYesterdayMoney + '万元' : 0}}</view>
                <view class="analysis-item-text">昨日销售额</view>
            </view>
            <view class="analysis-item">
                <view class="analysis-item-num">{{analysisDataYes.sumYesterdayQty}}</view>
                <view class="analysis-item-text">昨日销量</view>
            </view>
            <view class="analysis-item">
                <view class="analysis-item-num">{{analysisDataYes.sumYearMoney ? analysisDataYes.sumYearMoney + '万元' : 0}}</view>
                <view class="analysis-item-text">年累计销售额</view>
            </view>
        </view>
    </view>
    <view class="ananlysis-type block-layout">
        <van-tabs active="{{activeKey}}" bind:change="onChangeType">
            <van-tab title="销售金额" name="money"></van-tab>
            <van-tab title="销售数量" name="count"></van-tab>
        </van-tabs>
        <view class="time-slot">
            <view class="time-slot-title">选择时间段(单选):</view>
            <view class="time-item {{timeType == 1 ? 'time-active' : ''}}" bindtap="onClickTime" data-time="{{1}}">
                近7天
            </view>
            <view class="time-item {{timeType == 2 ? 'time-active' : ''}}" bindtap="onClickTime" data-time="{{2}}">
                近30天
            </view>
            <view class="time-item {{timeType == 3 ? 'time-active' : ''}}" bindtap="onClickTime" data-time="{{3}}">
                自定义
            </view>
              
        </view>
    </view>
    <view class="sales-overview block-layout">
        <view class="block-title">{{activeKey == 'money' ? '销售金额概括' : '销售数量概括'}}</view>
        <view class="overview-content">
            <view class="overview-content-item">
                <block>
                    <view class="price" wx:if="{{activeKey == 'money'}}">{{analysisData.salesAmount ? analysisData.salesAmount + '万元' : 0}}</view>
                    <view class="price" wx:else>{{analysisData.salesQty}}</view>
                </block>
                <view class="price-type">{{activeKey == 'money' ? '销售金额' : '销售数量'}}</view>
                <view class="percent">
                    <view class="percent-item">
                        <text class="percent-item-text">环比</text>
                        <block>
                            <text class="percent-item-num round" wx:if="{{activeKey == 'money'}}">{{analysisData.salesAmountPercentMoM || 0}}%</text>
                            <text class="percent-item-num round" wx:else>{{analysisData.salesQtyPercentMoM || 0}}%</text>
                        </block>
                    </view>
                    <view class="percent-item">
                        <text class="percent-item-text">同比</text>
                        <block>
                            <text class="percent-item-num same" wx:if="{{activeKey == 'money'}}">{{analysisData.salesAmountPercentYoY || 0}}%</text>
                            <text class="percent-item-num same" wx:else>{{analysisData.salesQtyPercentYoY || 0}}%</text>
                        </block>
                    </view>
                </view>
            </view>
            <view class="overview-content-item">
                <block>
                    <view class="price" wx:if="{{activeKey == 'money'}}">{{analysisData.backAmount ? analysisData.backAmount + '万元' : 0}}</view>  
                    <view class="price" wx:else>{{analysisData.backQty}}</view> 
                </block>
                <view class="price-type">{{activeKey == 'money' ? '退货金额' : '退货数量'}}</view>
                <view class="percent">
                    <view class="percent-item">
                        <text class="percent-item-text">环比</text>
                        <text class="percent-item-num round" wx:if="{{activeKey == 'money'}}">{{ analysisData.backAmountPercentMoM || 0}}%</text>
                        <text class="percent-item-num round" wx:else>{{analysisData.backQtyPercentMoM || 0}}%</text>
                    </view>
                    <view class="percent-item">
                        <text class="percent-item-text">同比</text>
                        <text class="percent-item-num same" wx:if="{{activeKey == 'money'}}">{{analysisData.backAmountPercentYoY || 0}}%</text>
                        <text class="percent-item-num same" wx:else>{{analysisData.backQtyPercentYoY || 0}}%</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="trend-analysis block-layout">
        <view class="block-title">趋势分析</view>
        <scroll-view style="width: 100%; height: 500rpx" scroll-x="{{true}}">
            <view class="canvas-box" style="display: inline-block">
                <ec-canvas id="trend-chart" canvas-id="trend-chart" ec="{{ ec }}" force-use-old-canvas="true"></ec-canvas>
            </view>
        </scroll-view> 
    </view>
    <van-calendar
        type="range"
        show="{{ showCalendar }}"
        min-date="{{ minDate }}"
        max-date="{{ maxDate }}"
        color="#00b9c3"
        id="calendar-block"
        default-date="{{defaultDate}}"
        bind:confirm="onConfirmDate"
        bind:close="onCloseCalendar"
    />
</view>