<view class="root-layout bg-04">
    <scroll-view style="height: {{windowHeight}}rpx" scroll-y="true">
        <van-radio-group value="{{ activeStore }}">
            <view class="store-list" wx:for="{{storeList}}" wx:key="index">
                <view class="store-head" data-name="{{item.custId}}" bindtap="onClickRadio">
                    <view class="store-name">{{item.custName}}</view>
                    <van-radio name="{{item.custId}}"></van-radio>
                </view>
                <view class="store-data">
                    <view class="store-data-item">
                        <view class="store-data-amount">{{item.sumYesterdayMoney ? item.sumYesterdayMoney + '万' : 0}}</view>
                        <view class="store-data-title">昨日销售额</view>
                    </view>
                    <view class="store-data-item">
                        <view class="store-data-amount">{{item.sumYesterdayQty || 0}}</view>
                        <view class="store-data-title">昨日销量</view>
                    </view>
                </view>
            </view>
        </van-radio-group>
    </scroll-view>
    <view class="store-footer" id="store-footer">
        <view class="store-footer-btn" bindtap="onClickConfirm">确定</view>
    </view>
</view>