// pages/ccs/analysis/storeList/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    storeList: [],
    activeStore: '',
    windowHeight: 1200
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    // 计算页面高度
    const that = this
    wx.getSystemInfo({
      success: (res) => {
        const query = wx.createSelectorQuery()
        query.select('#store-footer').boundingClientRect()
        query.exec(function(exceRes){
          let scrollH = res.windowHeight - exceRes[0].height
          that.setData({
            windowHeight: scrollH * App.globalData.pxToRpxRatio
          })
        })
      },
    })
    await this.getTotalAnalysis() // 获取整体销售分析
    this.getStoreList() // 门店列表
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取整体销售分析
  async getTotalAnalysis() {
    const url = '/api/psi/salesAnalysis/myx/query/total'
    const supInfo = wx.getStorageSync('supInfo')
    const res = await App.getHttp()._post(url, {
      setsOfBooksId: supInfo.setsOfBooksId,
    })
    let storeList = []
    storeList.push({
      ...res,
      custId: 0,
      custName: '整体销售'
    })
    this.setData({
      storeList
    })
  },
  // 获取门店列表
  getStoreList() {
    const url = '/api/psi/salesAnalysis/myx/query/cust'
    const supInfo = wx.getStorageSync('supInfo')
    // 拿去缓存中的门店信息
    const store = wx.getStorageSync('storeInfo')
    App.getHttp()._post(url, {
      setsOfBooksId: supInfo.setsOfBooksId,
      custType: 7
    }).then(res => {
      let storeList = [...this.data.storeList]
      this.setData({
        storeList: storeList.concat(res)
      })
    })
    if(store) {
      this.setData({
        activeStore: store.custId
      })
    }
  },
  // 切换单选框
  onClickRadio(e) {
    const name = e.currentTarget.dataset.name
    this.setData({
      activeStore: name
    })
  },
  // 确定选择门店
  onClickConfirm() {
    const storeObj = this.data.storeList.find(item => item.custId == this.data.activeStore)
    wx.setStorageSync('storeInfo', { custId: storeObj.custId, custName: storeObj.custName })
    wx.navigateBack({
      delta: 1
    })
  }
})