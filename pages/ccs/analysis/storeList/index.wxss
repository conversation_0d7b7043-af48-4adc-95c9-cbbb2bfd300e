/* pages/ccs/analysis/storeList/index.wxss */
.root-layout {
    height: 100vh;
}
.store-list {
    background-color: #fff;
    margin: 24rpx;
    border-radius: 8rpx;
}
.store-head {
    border-bottom: 1px solid #E6E6E6;
    padding: 32rpx 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.store-name {
    font-size: 28rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 28rpx;
}
.store-data {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 32rpx 0;
}
.store-data-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
}
.store-data-amount {
    font-size: 32rpx;
    color: #242424;
    line-height: 32rpx;
}
.store-data-title {
    font-size: 24rpx;
    color: #707070;
    line-height: 24rpx;
    margin-top: 16rpx;
}
.store-footer {
    width: 100%;
    padding: 8rpx 24rpx 48rpx 24rpx;
    box-sizing: border-box;
    background-color: #fff;
}
.store-footer-btn {
    background: #00b9c3;
    border-radius: 8rpx;
    font-size: 32rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 80rpx;
    height: 80rpx;
    text-align: center;
}
