/* pages/ccs/feedback/detail/index.wxss */
page{
  height: 100%;
  background-color: #F2F2F2;
}
.page-layout{
  height: 100%;
}
.detail-top{
  margin: 24rpx 24rpx 0;
  background: #fff;
  border-radius: 8rpx;
}
.detail-tag{
  padding: 8rpx 12rpx;
  display: inline-block;
  line-height: 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-top: 24rpx;
  margin-left: 24rpx;
  background: #FFFBE6;
  color: #FAAE16;
  border: 2rpx solid #FAAE16;
}
.detail-title{
  margin: 24rpx;
  font-size: 28rpx;
  word-break: break-all;
  line-height: 44rpx;
  color: #707070;
}
.attch-title{
  margin: 24rpx;
  font-size: 28rpx;
  color: #242424;
}
.img-box{
  margin-left: 24rpx;
  margin-right: -16px;
  margin-bottom: 24rpx;
}
.image-class{
  display: block !important;
  font-size:0 !important;
  width: 160rpx !important;
  height: 160rpx !important;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.image-box-class{
  display: block !important;
  font-size:0 !important;
}
.detail-date{
  font-size: 28rpx;
  margin-left: 24rpx;
  line-height: 28rpx;
  color: #707070;
}
.btn-resolve{
  width: 100% !important;
  height: 96rpx !important;
  margin-top: 24rpx;
  background: #fff !important;
  color: #00b9c3 !important;
  border-radius: 8rpx !important;
  border: none !important;
  font-size: 32rpx !important;
  border-top: 2rpx solid #E6E6E6!important;
}
.detail-reply{
  margin: 24rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 8rpx !important;
  margin-bottom: 100rpx;
}
.list-item{
  display: block;
  padding: 24rpx;
  
}
.list-item:nth-of-type(n+2){
  border-top: 1rpx solid #E6E6E6!important;
}
.reply-date{
  font-size: 28rpx;
  line-height: 32rpx;
  color: #707070;
}
.reply-person-txt{
  display: inline-block;
  font-size: 32rpx;
  color: #707070;
  line-height: 32rpx;
}
.reply-person-name{
  display: inline-block;
  font-size: 32rpx;
  color: #242424;
  line-height: 32rpx;
}
.reply-txt{
  display: inline-block;
  color: #707070;
  font-size: 32rpx;
  line-height: 48rpx;
}
.reply-content{
  font-size: 32rpx;
  line-height: 48rpx;
  color: #242424;
  word-break: break-all;
}
.reply-person-box{
  margin-top: 24rpx;
  line-height: 32rpx;
}
.reply-content-box{
  margin-top: 24rpx;
  line-height: 32rpx;
}
.bottom-box{
  position: fixed;
  height: 280rpx;
  width: 100%;
  bottom: 0;
  left: 0;
  background: #fff;
}
.bottom-input{
  margin: 12rpx 26rpx;
  height: 64rpx;
  border: 2rpx solid #DBDBDB;
}
.getHeight{
  height: 180rpx;
}