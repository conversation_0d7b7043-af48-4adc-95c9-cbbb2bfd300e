<!--pages/ccs/feedback/detail/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="page-layout">
  <view class="detail-top">
    <!-- <view>水果好吃有限公司水果好吃有限公司水果好吃有 限公司</view> -->
    <view class="detail-tag">{{wxsUtil.getDictName(mapValue,dataInfo.problemType)}}</view>
    <view class="detail-title">
      {{dataInfo.problemDesc}}
    </view>
    <view wx:if="{{attchList.length > 0}}" class="attch-title">附件</view>
    <view class="img-box" wx:if="{{attchList.length > 0}}">
      <!-- <upload initFileIds="{{fileRelationIds}}"/> -->
      <van-image src="{{item.fileUrl}}" wx:for="{{attchList}}" wx:key="fileId" wx:for-item="item" image-class="image-class" custom-class="image-box-class" fit="contain"/>
    </view>
    <view class="detail-date">{{dataInfo.createTime}}</view>
    <van-button wx:if="{{dataInfo.stat == 2}}" type="default" custom-class="btn-resolve" bind:click="onResolve" disabled="{{!processable || replyContent}}">确认解决</van-button>
  </view>
  <view class="detail-reply">
    <veiw wx:for="{{dataInfo.replyList}}" wx:key="index" class="list-item">
      <view class="reply-date">处理时间：{{item.createTime}}</view>
      <view class="reply-person-box"><view class="reply-person-txt">答复人：</view><text class="reply-person-name">{{item.createByName}}</text></view>
      <view class="reply-content-box"><text class="reply-txt">问题答复：</text><text class="reply-content">{{item.replyContent}}</text></view>
    </veiw>
  </view>
  <view class="getHeight"></view>
  <view class="bottom-box" wx:if="{{dataInfo.stat == 2}}">
    <input placeholder="" value="{{replyContent}}" bindinput="onBindinput" class="bottom-input" bindconfirm="onInputComfirm" disabled="{{!processable}}"></input>
    <van-button wx:if="{{replyContent}}" type="default" custom-class="btn-resolve" bind:click="onInputComfirm" disabled="{{!processable}}">确认回复</van-button>
  </view>

</view>