// pages/ccs/feedback/detail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    dataInfo:{},
    attchList:[],
    replyList:[],
    replyContent: '',
    processable: false,
    // config:{key: 'fileRelationIds'},
    // formData:{
    //   fileRelationIds: '',
    // }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let id = options.id
    this.setData({id, mapValue: wx.getStorageSync('dictMap').problemType})
    // 根据id获取信息
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  getData(){
    App.getHttp()._get(`/api/mms/problem/${this.data.id}`).then(res => {
      this.setData({
        dataInfo: res,
        processable: res.stat !== 3 && (res.userIds === wx.getStorageSync('userInfo').userId || res.createBy === wx.getStorageSync('userInfo').loginId),
        fileRelationIds: res.fileRelationIds
      })
      // 获取附件
      if(res.fileRelationIds){
        App.getHttp()._get(`/api/base/fileAttach/listByIds?ids=${res.fileRelationIds}`).then(res => {
            this.setData({
                attchList: res
            })
        })
      }
    })
  },
  onBindinput(e) {
    this.setData({
      replyContent: e.detail.value
    })
  },
  onInputComfirm(e){
    let param={
      problemId: this.data.id,
      replyContent: this.data.replyContent,
    }
    let that = this;
    App.getHttp()._post(`/api/mms/problemReply/myx/create`, param).then(res => {
      wx.showToast({
        title: '回复成功',
        icon: "none",
        success: function(){
          that.setData({
            replyContent: ''
          })
          that.getData();
        }
      })
  })
  },
  onResolve(){
    let param={
      ids: [this.data.id],
    }
    App.getHttp()._post(`/api/mms/problem/myx/solve`, param).then(res => {
      wx.showToast({
        title: '确认解决成功',
        icon: "none",
        success: function(){
          wx.navigateBack({
            delta: 1
          })
        }
      })
    })
  }
})