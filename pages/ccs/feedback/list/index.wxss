/* pages/ccs/feedback/list/index.wxss */
.page-layout{
  height: 100vh;
  background: #F2F2F2;
}
.list-item-feedback{
  overflow: hidden;
  margin: 24rpx 24rpx 0;
  padding: 0 24rpx;
  height: auto;
  background: #fff;
  border-radius: 8rpx;
}
.list-item-feedback .item-title{
  margin-top: 24rpx;
  font-size: 30rpx;
  line-height: 46rpx;
  color: #242424;
}
.list-item-feedback .title-txt{
  display: inline;
}
.list-item-feedback .title-icon{
  padding: 8rpx 12rpx;
  display: inline-block;
  line-height: 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}
.type{
  background: #FFFBE6;
  color: #FAAE16;
  border: 2rpx solid #FAAE16;
}
.icon-has{
  background: #FFF2F0;
  color: #FF4A4D;
  border: 2rpx solid #FF4A4D;
}
.icon-no{
  background: #F5FFEB;
  color: #52C718;
  border: 2rpx solid #52C718;
}
.list-item-feedback .item-detail{
  font-size: 28rpx;
  line-height: 44rpx;
  margin-top: 24rpx;
  color: #707070;
}
.list-item-feedback .item-date{
  font-size: 28rpx;
  line-height: 28rpx;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
  color: #9E9E9E;
}