<!--pages/ccs/feedback/list/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="page-layout root-layout" id="root-layout">
    <van-sticky offset-top="0" class="top-layout" id="top-layout">
        
        <van-tabs active="{{ active }}" bind:change="chooseTab">
            <van-tab wx:for="{{tabList}}" wx:key="index" title="{{item.name}}" name="{{index}}">
            </van-tab>
        </van-tabs>
        <!-- <warehouse-tab bind:chooseTab="chooseTab" /> -->
    </van-sticky>
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
        <view class="item-layout" wx:if="{{showList.length > 0}}">
          <view class="list-item-feedback" wx:for="{{showList}}" wx:key="index" data-index="{{index}}" bindtap="onItemClick">
                <view class="item-title single-line-ellipsis">
                    <!-- <view class="title-txt">{{item.problemDesc}}</view> -->
                    <view class="title-icon type">{{wxsUtil.getDictName(mapValue,item.problemType)}}</view>
                    <view class="title-icon icon-has" wx:if="{{item.stat == '1'}}">未受理</view>
                    <view class="title-icon icon-no" wx:if="{{item.stat == '2'}}">已受理</view>
                    <view class="title-icon icon-no" wx:if="{{item.stat == '3'}}">已关闭</view>
                </view>
                <view class="item-detail two-line-ellipsis">
                    {{item.problemDesc}}
                </view>
                <view class="item-date">
                    {{item.createTime}}
                </view>
            </view>
        </view>
        <view class="m-t-25p" wx:else>
            <no-product noneTxt="暂无数据" />
        </view>
    </listView>
</view>
