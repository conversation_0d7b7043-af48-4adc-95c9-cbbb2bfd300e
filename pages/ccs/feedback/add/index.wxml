<!--pages/ccs/feedback/add/index.wxml-->
<view class="page-layout">
  <view class="header-title">
    <view class="header-baseInfo">基本信息</view>
    <view class="header-quest" bindtap="onListClick">
      <view>问题列表</view>
      <van-icon name="/asset/imgs/arrow-left.png" custom-class="header-icon" />
    </view>
  </view>
  <!-- <van-field label="问题列表" placeholder="" is-link readonly bindtap="onListClick"/> -->
  <van-cell-group custom-class="custom-group-layout">
    <!-- 受理方 -->
    <van-field value="{{ custName }}" label="受理方" placeholder="请选择受理方" is-link readonly required bindtap="onAcquirerClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" right-icon-class="right-icon-class" />
    <van-popup show="{{ showAcquirerPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="acquirerCancel">
      <van-picker title="选择受理方" show-toolbar columns="{{ acquirerColumns }}" bind:change="onPickerChange" bind:confirm="acquirerConfirm" bind:cancel="acquirerCancel" />
    </van-popup>
    <!-- 问题类型 -->
    <van-field value="{{ problemTypeName }}" label="问题类型" placeholder="请选择问题类型" is-link readonly required bindtap="onQuestTypeClick" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" />
    <van-popup show="{{ questTypeShowPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="questTypeCancel">
      <van-picker title="选择问题类型" show-toolbar columns="{{ questTypeColumns }}" bind:change="onPickerChange" bind:confirm="questTypeConfirm" bind:cancel="questTypeCancel" />
    </van-popup>
    <!-- 问题描述 -->
    <van-field value="{{ problemDesc }}" label="问题描述" type="textarea" placeholder="请输入问题描述" autosize  show-word-limit row="2" maxlength="200" required bind:change="onProblemDescChange" custom-class="field-class-textarea" input-align="right" input-class="textarea-class" label-class="label-class" />
    <!-- 联系人 -->
    <van-field value="{{ contactPerson }}" label="联系人" placeholder="请输入联系人" required bind:change="onContactPersonChange" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" maxlength="{{10}}" />
    <!-- 联系电话 -->
    <van-field value="{{ contactNumber }}" label="联系电话" placeholder="请输入联系电话" required bind:change="onContactNumberChange" custom-class="field-class" input-align="right" input-class="input-class" label-class="label-class" />
    <!-- 上传截图 -->
    <view class="upload-pic-box">
      <view class="upload-pic">上传截图（{{uploadNum}}/3）</view>
      <view class="uploader-box">
        <!-- <van-uploader file-list="{{ fileList }}" deletable="{{ true }}" bind:after-read="afterRead"/> -->
        <upload bind:change="fileChange" />
      </view>
    </view>
  </van-cell-group>
  <view class="btn-box">
    <van-button wx:if="{{!isCommit}}" block   bindtap="onSureBtnClick" custom-class="btn-class flex_center">提交</van-button>
  </view>
</view>