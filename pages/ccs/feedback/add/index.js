// pages/ccs/feedback/add/index.js
import { DOMAIN } from '../../../../utils/server'
const baseUrl = DOMAIN
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    acquirerColumns:[],
    isCommit: false,
    showAcquirerPop: false,
    questTypeColumns:[],
    questTypeShowPop: false,
    fileList: [],
    // 参数
    custSetsOfBooksId: '',
    custCode: '',
    custName: '',
    problemDesc: '',
    problemType: '',
    problemTypeName: '',
    contactPerson: '',
    contactNumber: '',
    fileRelationIds: '',
    uploadNum: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 查询所有受理方
    let param = {
      "pageIndex": 1,
      "pageSize": 100,
      param:{}
    }
    let acquirerColumns = []
    App.getHttp()._post('/api/sys/common/sysSetsOfBooks/devision/page', param, true).then(res => {
      acquirerColumns = res.recordList.map(item=>{
        item.text = item.name
        return item
      })
      this.setData({acquirerColumns})
    })
    // let acquirerColumns = wx.getStorageSync('custInfo').bsRelates.map(item=>{
    //   item.text = item.bvendorName
    //   return item
    // })
    // 问题类型数据字典
    let questTypeColumns = wx.getStorageSync('dictMap').problemType.map(item=>{
      item.text = item.name
      return item
    })
    this.setData({ questTypeColumns })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onAcquirerClick(){
    this.setData({
      showAcquirerPop: true
    })
  },
  acquirerCancel(){
    this.setData({
      showAcquirerPop: false
    })
  },
  acquirerConfirm(e){
    let item = e.detail.value;
    this.setData({
      custName: item.text,
      custCode: item.randomCode,
      custSetsOfBooksId: item.id,
      showAcquirerPop: false
    })
  },
  onQuestTypeClick(){
    this.setData({
      questTypeShowPop: true
    })
  },
  questTypeCancel(){
    this.setData({
      questTypeShowPop: false
    })
  },
  questTypeConfirm(e){
    let item = e.detail.value;
    this.setData({
      problemType: item.value,
      problemTypeName: item.name,
      questTypeShowPop: false
    })
  },
  onProblemDescChange(e){
    this.data.problemDesc = e.detail
  },
  onContactPersonChange(e){
    this.data.contactPerson = e.detail
  },
  onContactNumberChange(e){
    this.data.contactNumber = e.detail
  },
  afterRead(event) {
    const { file } = event.detail;
    // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
    wx.uploadFile({
      url: baseUrl + '/api/base/file/upload', // 仅为示例，非真实的接口地址
      filePath: file.url,
      name: 'file',
      formData: { user: 'test' },
      success(res) {
        // 上传完成需要更新 fileList
        const { fileList = [] } = this.data;
        fileList.push({ ...file, url: res.data });
        this.setData({ fileList });
      },
      fail: function (res) {
        console.log(res)
      },
    });
  },
  onListClick(){
    wx.navigateTo({
      url: '/pages/ccs/feedback/list/index'
    })
  },
  onSureBtnClick(){
    this.setData({
      isCommit: true
    })
    const mobilePattern = /^1[3456789]\d{9}$/
    let {custSetsOfBooksId, custCode,custName,problemDesc,problemType,contactPerson,contactNumber,fileRelationIds } = this.data
    let message = ''
    if(!custName){
      message = '受理方不能为空'
    }else if(!problemType){
      message = '问题类型不能为空'
    }else if(!problemDesc){
      message = '问题描述不能为空'
    }else if(!contactPerson){
      message = '联系人不能为空'
    }else if(!contactNumber){
      message = '联系人电话不能为空'
    }else if(!mobilePattern.test(contactNumber)){
      message = '联系人电话不正确'
    }
    if(message){
      wx.showToast({
        title: message,
        icon: "none"
      })
      this.setData({
        isCommit: false
      })
      return
    }
    const param={
      custSetsOfBooksId, custCode,custName,problemDesc,problemType,contactPerson,contactNumber,fileRelationIds
    }
    let that = this
    App.getHttp()._post('/api/mms/problem/myx/create', param, true).then(res => {
      wx.showToast({
        title: '提交成功',
        icon: "none",
        success:function(){
          wx.navigateBack({
            delta: 1,
          })
          that.setData({
            isCommit: false
          })
        },
      })
    }).catch(error => {
      that.setData({
        isCommit: false
      })
    })
  },
  fileChange(e){
    this.setData({fileRelationIds: e.detail.fileIds, uploadNum:e.detail.fileIds ? e.detail.fileIds.split(',').length : 0})
  }
})