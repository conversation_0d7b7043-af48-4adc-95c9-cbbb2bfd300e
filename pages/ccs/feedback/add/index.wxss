/* pages/ccs/feedback/add/index.wxss */
page{
  height: 100%;
  background-color: #F2F2F2;
}
.header-title{
  display: flex;
  justify-content: space-between;
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx; 
  color: #242424;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
  background: #fff;
}
.header-baseInfo{
  margin-left: 32rpx;
}
.header-quest{
  display: flex;
  margin-right: 32rpx;
  font-weight: 500;
}
.header-icon{
  height: 40rpx; 
  width: 32rpx;
  margin:4rpx 0 0 16rpx;
  transform: rotate(-180deg) !important;
}
.custom-group-layout{
  padding-left: 32rpx;
  background-color: white;
}
.field-class{
  line-height: 96rpx !important;
  padding: 0 0 0 18rpx !important;
  display: flex;
  align-items: center;
  font-size: 32rpx !important;
  font-family: PingFang SC-Regular, PingFang SC !important;
  font-weight: 400 !important;
}
.field-class-textarea{
  box-sizing: border-box;
  line-height: 96rpx !important;
  padding: 0 0 0 18rpx !important;
}
.label-class{
  color: #242424 !important;
  font-weight: 400 !important;
  font-size: 32rpx !important;
  font-family: PingFang SC-Regular, PingFang SC !important;
}
.field-class::before, .field-class-textarea::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0 !important;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-size: 28rpx !important;
}
.input-class{
  height: 96rpx !important;
  line-height: 96rpx !important;
  margin-right: 32rpx !important;
}
.textarea-class{
  margin-top: 32rpx !important;
  min-height: 96rpx !important;
  line-height: 42rpx !important;
  margin-right: 32rpx !important;
}
.van-field__body--textarea{
  padding: 0 !important;
}
.input-class .van-field__placeholder, .field-class-textarea .van-field__placeholder{
  color: #BDBDBD !important;
}
.van-field__word-limit{
  margin-right: 32rpx;
  font-size: 28rpx !important;
}
.van-icon-arrow {
  margin-left: -16rpx;
  margin-right: 32rpx;
}
.van-icon-arrow::before{
  width: 32rpx;
  height: 32rpx;
}

.upload-pic-box{
  background: #fff;
  padding-bottom: 16rpx;
}
.upload-pic{
  height: 92rpx;
  line-height: 92rpx;
  color: #242424;
  font-size: 32rpx;
  background: #fff;
}
.btn-box{
  margin-top: 24rpx;
  padding: 0 24rpx;
}
.btn-class{
  height: 96rpx !important;
  border-radius: 8rpx !important;
  border: 0 !important;
  background: #00b9c3 !important;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF !important;
  font-size: 32rpx !important;
  line-height: 96rpx !important;
}
.uploader-box{
}
.van-uploader__upload{
  width: 160rpx !important;
  height: 160rpx !important;
  margin-right: 16rpx !important;
  margin-bottom: 16rpx !important;
}
/* .van-tabs__wrap, .van-tabs__wrap scroll-view, .van-tabs__nav, .van-ellipsis, .van-tab{
  height: 44px !important;
} */