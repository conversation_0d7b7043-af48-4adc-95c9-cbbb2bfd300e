// pages/ccs/retail/order/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    typeList: [
      { title:'全部',name:'1' },
      { title: '已完成', name: '0205' },
      { title: '已退货', name: '0207' },
    ],
    queryState: '1',
    pageIndex: 1,
    pageSize: 10,
    dataList: [],
    keyword: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if(options.stat) {
      this.setData({
        queryState: options.stat
      })
    } else {
      this.getOrderList()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#tabsView').boundingClientRect()
        query.select('#filterSearch').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].bottom - App.globalData.deviceBottomOccPx
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getOrderList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 加载更多事件
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getOrderList()
  },
  // 获取列表事件
  getOrderList() {
    const url = '/api/psi/rtOutBill/myx/page'
    const custInfo = wx.getStorageSync('curCust')
    const params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        billNo: this.data.keyword,
        billType: this.data.queryState === '1' ? '' : this.data.queryState,
        custId: custInfo.custId
      }
    }
    App.getHttp()._post(url, params).then(res => {
      let dataList = this.data.pageIndex === 1 ? [] : this.data.dataList
      if(res && res.length > 0) {
        this.setData({
          dataList: dataList.concat(res)
        })
      } else {
        this.setData({
          dataList
        })
      }
    })
  },
  // 搜索事件
  onSearch() {
    this.setData({
      pageIndex: 1
    })
    this.getOrderList()
  },
  // 输入框修改事件
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 导航栏切换事件
  tapNavi(e) {
    this.setData({
      queryState: e.detail.name,
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  // 跳转详情
  onClickItem(e) {
    const id = e.currentTarget.dataset.id
    const billType = e.currentTarget.dataset.type
    wx.navigateTo({
      url: '/pages/ccs/retail/order/detail/index?id=' + id + '&billType=' + billType
    })
  }
})