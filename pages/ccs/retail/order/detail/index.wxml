<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<view class="root-layout">
    <view class="head-layout flex">
        <view class="bill-state">{{detailInfo.billType == '0205' ? '已完成' : '已退货'}}</view>
        <view class="bill-no flex align-items-center">
            <view>订单号:</view>
            <view class="no-text">{{detailInfo.billNo}}</view>
            <van-icon size="12" bindtap="onClickCopy" name="/asset/imgs/purchase/copy.png"></van-icon>
        </view>
    </view>
    <detail-address orderAddr="{{detailInfo}}"/>
    <block wx:if="{{detailInfo.addLineList && detailInfo.addLineList.length > 0}}">
        <view wx:for="{{detailInfo.addLineList}}" wx:key="index">
            <retailItem noMarginX="{{true}}" itemObj="{{detailInfo}}" fromDetail="{{true}}"></retailItem>
        </view>
    </block>
    <van-field input-align="right" readonly label="下单时间" value="{{detailInfo.createTime}}"></van-field>
    <van-field input-align="right" readonly label="完成时间" value="{{detailInfo.auditedDate}}"></van-field>
    <van-field input-align="right" readonly label="退货仓库" wx:if="{{billType == '0207'}}" value="{{detailInfo.warehouseName}}"></van-field>
    <van-field input-align="right" readonly label="配送方式" value="{{detailInfo.shipModeName}}"></van-field>
    <van-field input-align="right" readonly label="收款方式" wx:if="{{billType == '0205'}}" value="{{wxsUtil.formatPayType(payTypeDict, detailInfo.payType)}}"></van-field>
    <van-field input-align="right" readonly label="备注" value="{{detailInfo.remark}}"></van-field>
</view>