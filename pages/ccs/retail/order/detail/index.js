// pages/ccs/orderDetail/index.js
const App=getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    billId:'',
    detailInfo: {},
    billType: '',
    payTypeDict: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id){
      this.setData({
        billId: options.id
      })
    }
    if(options.billType) {
      this.setData({
        billType: options.billType
      })
    }
    const payTypeDict = wx.getStorageSync('dictMap').orderPayType
    this.setData({
      payTypeDict
    })
    this.getOrderDetail()
  },
  getOrderDetail(){
    const url = this.data.billType === '0205' ? '/api/psi/rtOutBill/' + this.data.billId : '/api/psi/rtReturnOutBill/' + this.data.billId
    App.getHttp()._get(url).then(res=>{
      if(res) {
        this.setData({
          detailInfo: res
        })
      }
    })
  },
  // 复制单号
  onClickCopy() {
    if(!this.data.detailInfo.billNo) {
      return
    }
    wx.setClipboardData({
      data: this.data.detailInfo.billNo,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制单号成功'
        })
      }
    })
  }
})