<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="page" id="page-layout">
  <!-- 顶部区域 -->
  <van-sticky offset-top="{{offsetTop}}" id="search-layout">
    <van-search value="{{ keyword }}" placeholder="请输入搜索关键词" use-action-slot bind:change="onChangeKeyword" bind:search="onConfirm">
      <view slot="action" class="search-right" bind:tap="onConfirm">搜索</view>
    </van-search>
  </van-sticky>
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <view class="history-layout">
      <block wx:if="{{showHis}}">
        <view class="flex align-items-center justify-content-between">
          <view class="title">历史记录</view>
          <view class="clean flex align-items-center">
            <block wx:if="{{showExpand}}">
              <view class="expand-item" bindtap="onChangeExpand" wx:if="{{isExand}}">
                展开<van-icon name="arrow-down" />
              </view>
              <view class="expand-item" bindtap="onChangeExpand" wx:else>
                收起<van-icon name="arrow-up" />
              </view>
            </block>
            <image class="icon" wx:if="{{hisList.length > 0}}" bindtap="onClearAll" src="/asset/imgs/purchase/delete.png"></image>
          </view>
        </view>
        <view class="listbox flex {{isExand ? 'expand-layout' : ''}}" id="label-list">
          <view class="label" wx:for="{{hisList}}" wx:key="historyId" bindtap="onClickHisKeyWord" data-keyword="{{item.keyWord}}">
            {{item.keyWord}}
          </view>
        </view>
      </block>
      <view wx:else>
        <view class="new-product">
          <block wx:if="{{goodsList.length > 0}}">
            <view class="goodsList" wx:for="{{goodsList}}" data-item="{{item}}" bindtap="onClickGood" wx:key="index">
              <view class="good-info">
                <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                <view class="good-name-box">
                  <view class="name">{{item.itemName}}</view>
                  <view class="specs">{{item.specs}}</view>
                </view>
                <view class="good-content-right">
                  <view class="good-price">{{item.price ? '¥' + item.price : '暂无报价'}}</view>
                  <view class="good-onHand">库存{{item.totalQty}}</view>
                </view>
              </view>
              <view class="good-action">
                <view class="add-to-checkoutPlatform" data-item="{{item}}" data-type="{{1}}" catchtap="onClickShowAddCart">
                  加入结账台
                </view>
                <view class="create-order" data-item="{{item}}" data-type="{{2}}" catchtap="onClickShowAddCart">
                  立即开单
                </view>
              </view>
            </view>
          </block>
          <!-- 缺省 -->
          <view class="m-t-25p" wx:else>
            <no-product noneTxt="暂无商品" />
          </view>
        </view>
      </view>
    </view>
  </listView>
  <retailAddCarts type="{{addType}}" bind:onClose="onCloseAddCarts" show="{{showAddCartsPop}}" itemInfo="{{addItemInfo}}" />
</view>