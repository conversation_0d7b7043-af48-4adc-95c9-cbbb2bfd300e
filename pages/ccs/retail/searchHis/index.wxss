.search-layout {
  width: 100%;
  padding: 0 24rpx 12rpx;
  box-sizing: border-box;
}
.search-layout .backImg {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.history-layout {
  padding: 0 24rpx;
  /* margin-top: 16rpx; */
}
.history-layout .title {
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
}
.history-layout .clean {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.history-layout .icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}
.history-layout .listbox {
  margin-top: 32rpx;
}
.history-layout .expand-layout {
  height: 160rpx;
  overflow: hidden;
}
.history-layout .listbox .label {
  height: 64rpx;
  padding: 0 40rpx;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 64rpx;
  font-weight: 400;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.expand-item {
  margin-right: 16rpx;
}
.new-product {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

.new-product .none {
  padding: 60rpx 0;
  margin-top: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
}

.new-product .goods-card {
  position: relative;
}

.new-product .goods-card:after {
  position: absolute;
  bottom: 1rpx;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  content: "";
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: rgba(0, 0, 0, 0.1);
}

.new-product .goods-card:last-child:after {
  height: 0;
}
.search-right {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
  margin: 0 24rpx;
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}
.goodsList {
  font-family: PingFangSC-Regular, PingFang SC;
  margin-top: 24rpx;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-radius: 8rpx;
}
.good-img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 32rpx;
}
.good-price {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 32rpx;
}
.good-info {
  display: flex;
}
.good-name-box{
  flex: 1;
}
.good-name-box .name {
  font-size: 32rpx;
  font-weight: 400;
  color: #242424;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 32rpx;
}
.good-name-box .specs {
  font-size: 26rpx;
  line-height: 32rpx;
  font-weight: 400;
  color: #8A8A8A;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-content-right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}
.good-onHand {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 24rpx;
}
.good-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 16rpx;
}
.add-to-checkoutPlatform, .create-order {
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  padding: 14rpx 32rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
}
.add-to-checkoutPlatform {
  margin-right: 16rpx;
}
/* components/addCarts/index.wxss */
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
/* 垂直 */
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}
.justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.content-layout{
  padding: 112rpx 24rpx 0 24rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}
.right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.content-layout .img{
  width: 160rpx;
  height: 160rpx;
  margin-right: 32rpx;
}
.itemName{
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  line-height: 40rpx;
  color: rgba(0,0,0,0.85);
  font-weight: 400;
}
.itemPrice{
  font-family: SanFranciscoText-Semibold;
  font-size: 36rpx;
  color: #242424;
  line-height: 42rpx;
  font-weight: 600;
}
/* 重写vant样式 */
#search-layout .van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}
