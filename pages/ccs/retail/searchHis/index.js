// pages_sub/pages/ccs/down/searchHis/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageIndex: 1,
    pageSize: 10,
    clientRectTop: 24,
    clientRectBottom: 66,
    clientRectHeight: 32,
    clientRectWidth: 99,
    showHis:true,
    hisList:[],
    goodsList:[],
    listViewH: 400,
    addItemInfo: {},
    windowPaddingTop: 176,
    offsetTop: 60,
    keyword: '',
    addItemInfo: {},
    showAddCartsPop: false,
    addType: 1,
    showExpand: false,
    isExand: false
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onLoad: function (options) {
    // this.initSearchHis()
    // 清空缓存的立即开单数据
    wx.removeStorageSync('createOrderInfo')
  },
  initPage() {
    const that = this
    wx.getSystemInfo({
      success(res) {
        const query = wx.createSelectorQuery()
        query.select('#search-layout').boundingClientRect()
        query.exec((exRes)=>{
          that.setData({
            listViewH: res.windowHeight - exRes[0].height
          })
        })
      }
    }) 
  },
  initSearchHis() {
    App.getHttp()._post('/api/mms/searchHis/myx/page', {
      pageIndex: 1,
      pageSize: 20,
      param: {
        busCode: 'retail_item_search', 
        busModule: 'psi'
      }
    }).then(res => {
      this.setData({
        hisList:res,
        showHis:true,
      })
      const self = this
      const query = wx.createSelectorQuery()
      query.select('#label-list').boundingClientRect()
      query.exec((exRes)=>{
        console.log(exRes[0].height)
        self.setData({
          showExpand: exRes[0].height > 80 ? true : false,
          isExand: exRes[0].height > 80 ? true : false
        })
      })
    })
  },
  addSearchHist(keyWord){
    // 储存搜索历史
    App.getHttp()._post('/api/mms/searchHis/myx/create', {keyWord: keyWord, busModule: 'psi', busCode: 'retail_item_search'})
  },
  onBackListener() {
    wx.navigateBack({
      delta: 1,
    })
  },
  onClickHisKeyWord(e){
    this.setData({
      pageIndex: 1,
      goodsList: [],
      keyword: e.target.dataset.keyword
    })
    this.getList(e.target.dataset.keyword)
  },
  onLoadMore: function () {
    this.setData({
      pageIndex: (this.data.pageIndex + 1)
    })
    this.getList()
  },
  onConfirm(e) {
    this.setData({
      pageIndex: 1,
      goodsList: [],
      showHis: true,
    })
    let keyword = this.data.keyword
    if (keyword) {
      this.addSearchHist(keyword)
    }
    this.getList()
  },
  getList() {
    const custInfo = wx.getStorageSync('curCust')
    let param = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        keyword: this.data.keyword,
        custId: custInfo.custId
      }
    }
    App.getHttp()._post('/api/psi/rtOutBill/myx/selectRtItem', param).then(res => {
      if (res && res.length > 0) {
        let goodsList = this.data.pageIndex === 1 ? [] : [...this.data.goodsList]
        this.setData({
          goodsList: goodsList.concat(res)
        })
        this.initPage()
      } else {
        if(this.data.pageIndex === 1) {
          this.setData({
            goodsList: []
          })
        }
      }
    })
    this.setData({
      showHis: false
    })
  },
  onClearAll(){
    const _this = this
    wx.showModal({
      title: '温馨提示',
      content: `确定要删除所有搜索历史？`,
      success (res) {
        if (res.confirm) {
          App.getHttp()._post('/api/mms/searchHis/myx/clear', { busCode: 'retail_item_search', busModule: 'psi' }).then(res => {
            _this.setData({
              hisList:[],
            })
          })
        } else if (res.cancel) {
          // console.log('用户点击取消')
        }
      }
    })
  },
  // 输入框内容改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 跳转详情
  onClickGood(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: "/pages/ccs/retail/skuDetail/index?itemId=" + item.itemId,
    });
  },
  // 关闭加入结账台
  onCloseAddCarts(e) {
    const item = e.detail
    if(item) {
      if(this.data.addType === 2) {
        const curCust = wx.getStorageSync("curCust");
        if(!curCust||!curCust.custId){
          wx.showToast({
            title: '缺少门店信息!',
            icon:'none'
          })
          return
        }
        wx.setStorageSync('createOrderInfo', [item])
        wx.navigateTo({
          url: '/pages/ccs/retail/createOrder/index'
        })
      } else {
        this.addShops(item)
      }
    }
    this.setData({
      showAddCartsPop: false
    })
    if(this.getTabBar()) {
      this.getTabBar().setData({
        isShow:true
      })
    }
  },
  // 立即开单/加入结账台
  onClickShowAddCart(e) {
    const item = e.currentTarget.dataset.item
    const type = e.currentTarget.dataset.type
    this.setData({
      showAddCartsPop: true,
      addType: type,
      addItemInfo: item
    })
    if(this.getTabBar()) {
      this.getTabBar().setData({
        isShow:false
      })
    }
  },
  // 加入结账台
  async addShops(item) {
    const url = '/api/psi/invCheckstand/addInvCheckstand'
    const supInfo = wx.getStorageSync("supInfo");
    const curCust = wx.getStorageSync("curCust");
    if(!curCust||!curCust.custId){
      wx.showToast({
        title: '缺少门店信息!',
        icon:'none'
      })
      return
    }
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: curCust.custId,
      invoiceCustCode: curCust.custCode,
      itemId: item.itemId,
      itemCode: item.itemCode,
      qty: item.qty,
      standType: 1
    }
    const res = await App.getHttp()._post(url, params)
    wx.showToast({
      icon: 'success',
      title: '加入成功'
    })
  },
  // 切换展开收起
  onChangeExpand() {
    this.setData({
      isExand: !this.data.isExand
    })
  }
})