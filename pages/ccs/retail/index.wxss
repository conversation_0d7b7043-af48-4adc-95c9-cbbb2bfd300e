/* pages/ccs/retail/index.wxss */
.root-layout {
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
::-webkit-scrollbar{
  width: 0;
  height: 0;
  color: transparent;
}
.bg-img {
  width: 100%;
  height: 500rpx;
}
.cust-layout {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 48rpx 48rpx 40rpx 48rpx;
}
.cust-icon {
  width: 88rpx;
  height: 88rpx;
}
.custName {
  margin-left: 32rpx;
  margin-right: 38rpx;
  flex: 1;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 36rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.filter-layout {
  padding: 24rpx;
}
.filter-layout .scan{
  width: 56rpx;
  height: 56rpx;
  padding:0 12rpx;
}
.filter-layout .right-order{
  width: 230rpx;
  height: 72rpx;
}
.filter-layout-search {
  display: flex;
  height: 72rpx;
  line-height: 72rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
}
.search-text {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #BDBDBD;
}
.middle-layout {
  background-color: #fff;
  margin: 0 24rpx;
  padding-bottom: 32rpx;
  border-radius: 8rpx;
}
.middle-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
}
.middle-title {
  font-size:28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 28rpx;
}
.middle-to-all {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #616161;
  line-height: 44rpx;
}
.list-layout{
  padding: 24rpx 24rpx 150rpx 24rpx;
}
.listItemLayout {
  margin-bottom: 24rpx;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-radius: 8rpx;
}
.good-img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 32rpx;
}
.good-price {
  font-size: 28rpx;
  line-height: 36rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
}
.good-price .rem {
  font-size: 24rpx;
  line-height: 36rpx;
}
.good-info {
  display: flex;
}
.good-name-box{
  margin-right: 32rpx;
  flex: 1;
}
.good-name-box .name {
  font-size: 28rpx;
  line-height: 36rpx;
  height: 72rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-name-box .specs {
  font-size: 28rpx;
  line-height: 36rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8a8a8a;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-content-right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}
.good-onHand {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 24rpx;
}
.good-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 16rpx;
}
.add-to-checkoutPlatform, .create-order {
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  padding: 14rpx 32rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
}
.add-to-checkoutPlatform {
  margin-right: 16rpx;
}
.float-btn-layout{
  position: fixed;
  left: 32rpx;
  right: 32rpx;
  bottom: 180rpx;
}
/**重新定义样式background: rgba(0,0,0,0.04);*/
.van-tabs{
  border-bottom: 1rpx solid transparent !important;
}
.van-tab{
  font-size: 32rpx !important;
}
.van-tabs__scroll{ 
  background-color: transparent !important;
}
