/* pages/ccs/detailsG/index.wxss */
.root-layout {
  background-color: #F2F2F2;
  width: 100%; 
  height: 100vh;
}

.swiper-layout {
  position: relative;
}

.swiper-layout .backimg {
  width: 64rpx;
  height: 64rpx;
  position: absolute;
  left: 24rpx;
}

.item-layout {
  padding: 24rpx;
}
.item-layout .price-box{
  border-radius: 8rpx 8rpx 0 0;
  padding: 20rpx 24rpx;
  background: linear-gradient(148deg, #FE8700 0%, #FF6201 100%);
}
.item-layout .price-box .itemprice .uom {
  font-family: PingFangSC-Medium, PingFang SC;
  font-size: 28rpx;
  line-height: 56rpx;
  font-weight: 500;
}
.item-layout .price-box .itemprice .over{
  margin-left: 24rpx;
  /* text-decoration: line-through; */
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 56rpx;
}
.item-layout .price-box .itemprice .current {
  font-family: PingFangSC-Medium, PingFang SC;
  font-size: 47rpx;
  line-height: 56rpx;
  font-weight: 500;
}
.item-layout .price-box .itemprice {
  color: #FFFFFF;
}

.item-layout .price-box .stock-count{
  font-size: 24rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 56rpx;
}
.item-layout .name-box {
  background-color: white;
  padding: 24rpx;
  border-radius: 0 0 8rpx 8rpx;
}
.item-layout .name-box .itemname {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #242424;
  line-height: 40rpx;
  font-weight: 500;
}
.item-layout .name-box .vendorname{
  margin-top: 24rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 32rpx;
}


.more-layout {
  padding: 24rpx;
  font-family: PingFangSC-Regular;
  font-size: 20rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 36rpx;
  font-weight: 400;
  text-align: center;
}

.pic-card {
  width: 100%;
  background-color: #ffffff;
  height: auto;
  display:block
}

.placeholder-layout {
  height: 112rpx;
}
.none-stock-layout {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 96rpx;
  height: 80rpx;
  background: #000000;
  opacity: 0.7;
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 80rpx;
  text-align: center;
  z-index: 100;
}
.foot-layout {
  z-index: 100;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 96rpx;
  box-sizing: border-box;
  padding: 8rpx 8rpx 8rpx 0;
  background-color: #FFFFFF;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}

.foot-layout .add {
  width: 270rpx;
  height: 80rpx;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
  font-weight: 400;
  background: #00b9c3;
  border-radius: 8rpx 0 0 8rpx;
}

.foot-layout .buy {
  width: 270rpx;
  height: 80rpx;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
  font-weight: 400;
  background: #F6A52C;
  
  border-radius: 0 8rpx 8rpx 0;
}

.foot-layout .badge{
  position: absolute;
  top: 5rpx;
  right: 0;
  box-sizing: border-box;
  min-width: 32rpx;
  padding: 0 6rpx;
  color: #fff;
  font-weight: 500;
  font-size: 24rpx;
  font-family:  SanFranciscoText-Medium;
  line-height: 1.2;
  text-align: center;
  background-color: #ee0a24;
  border: 1rpx solid #fff;
  border-radius: 16rpx;
  -webkit-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  -webkit-transform-origin: 100%;
  transform-origin: 100%;
}