// pages/ccs/detailsG/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    scrollTab: 2,
    windowWidthRpx: 375,
    scrollViewHeightPx: 500,
    scrollIntoView: 'swiper_layout',
    dInfo: {},
    mainPicUrl: [],
    preImageUrl: [],
    desPicUrl: [],
    showAddCartsPop: false,
    cartCount: 0,
    footlayoutAble: true,
    showPreView: false,
    addType: 1,
    windowHeadHeight: 64,
    windowHeadPaddingTop: 48,
    windowHeadPaddingRight: 218,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.itemId) {
      this.iniData(options)
    }
    // 先清空缓存的开单数据
    wx.removeStorageSync('createOrderInfo')
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    const rect = wx.getMenuButtonBoundingClientRect();
    query.select('#page-layout').boundingClientRect()
    query.select('#scroll-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        scrollViewHeightPx: res[0].height - res[1].top,
        windowHeadHeight: rect.height * App.globalData.pxToRpxRatio,
        windowHeadPaddingTop: rect.top * App.globalData.pxToRpxRatio,
        windowHeadPaddingRight: (rect.width + 23) * App.globalData.pxToRpxRatio
      })
    })
    wx.getSystemInfo({
      success: (result) => {
        this.setData({
          windowWidthRpx: (result.windowWidth * App.globalData.pxToRpxRatio)
        })
      },
    })
  },
  iniData: function (param) {
    const curCust = wx.getStorageSync("curCust");
    const params = {
      custId: curCust.custId,
      itemId: param.itemId
    }
    App.getHttp()._post('/api/psi/rtReturnOutBill/myx/selectRtDetail', params).then(res => {
      if (res) {
        const mainPicUrl = res.carouselImages ? res.carouselImages.map(v => {
          return {
            picUrl: v.url
          }
        }) : []
        const descriptionImages = res.descriptionImages ? res.descriptionImages.map(v => v.url) : []
        this.setData({
          dInfo: res.item || res.rtItem,
          mainPicUrl: mainPicUrl,
          preImageUrl: mainPicUrl.map(mainItem => mainItem.picUrl),
          desPicUrl: descriptionImages,
        })
      }
    })
    const vendorList = wx.getStorageSync('vendorList')
    if (vendorList && vendorList.length > 0) {
      const findIndex = vendorList.findIndex(res => res.vendorCode === params.vendorCode && res.channelCode === params.channelCode && res.productCode === params.productCode)
      this.setData({
        footlayoutAble: findIndex > -1
      })
    }
  },
  onClickSwiperItem(event) {
    // wx.previewImage({
    //   current: this.data.preImageUrl[index], // 当前显示图片的http链接
    //   urls: this.data.preImageUrl // 需要预览的图片http链接列表
    // })
    this.setData({
      showPreView: true
    })
  },
  onClickShop() {
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/shop/index',
    })
  },
  // 加入结账台/立即开单
  onClickShowAddCart(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      showAddCartsPop: true,
      addType: type,
    })
  },
  onCloseAddCarts(e) {
    const item = e.detail
    if (item) {
      if (this.data.addType === 2) {
        const curCust = wx.getStorageSync("curCust");
        if (!curCust || !curCust.custId) {
          wx.showToast({
            title: '缺少门店信息!',
            icon: 'none'
          })
          return
        }
        wx.setStorageSync('createOrderInfo', [item])
        wx.navigateTo({
          url: '/pages/ccs/retail/createOrder/index'
        })
      } else {
        this.addShops(item)
      }
    }
    this.setData({
      showAddCartsPop: false
    })
  },
  // 加入结账台方法
  async addShops(item) {
    const url = '/api/psi/invCheckstand/addInvCheckstand'
    const supInfo = wx.getStorageSync("supInfo");

    const curCust = wx.getStorageSync("curCust");
    if (!curCust || !curCust.custId) {
      wx.showToast({
        title: '缺少门店信息!',
        icon: 'none'
      })
      return
    }
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: curCust.custId,
      invoiceCustCode: curCust.custCode,
      itemId: item.itemId,
      itemCode: item.itemCode,
      qty: item.qty,
      standType: 1
    }
    const res = await App.getHttp()._post(url, params)
    this.selectComponent('#retail-head').getCheckoutPlatformCount()
    setTimeout(() => {
      wx.showToast({
        icon: 'success',
        title: '添加成功'
      })
    }, 500)
  },
  onTopChangeTab(e) {
    this.setData({
      scrollIntoView: e.detail.name
    })
    console.log('=onTopChangeTab=', this.data.scrollIntoView);
  }
})