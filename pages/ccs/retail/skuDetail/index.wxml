<!-- pages/ccs/detailsG/index.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" id="page-layout">
  <!-- 导航头部 -->
  <retailHead customTitle="商品详情" id="retail-head" />
  <!-- 热卖-新品 -->
  <van-tabs active="{{ scrollTab }}" nav-class="navStyle" bind:click="onTopChangeTab">
    <van-tab title="商品" name="swiper_layout"></van-tab>
    <van-tab title="详情" name="more_layout"></van-tab>
  </van-tabs>
  <scroll-view scroll-into-view="{{scrollIntoView}}" id="scroll-layout" scroll-y style="height: {{scrollViewHeightPx}}px;">
    <view class="swiper-layout" id="swiper_layout">
      <adSwiper adList="{{mainPicUrl}}" heightRpx="{{windowWidthRpx}}" indicatorActive="#00b9c3" autoHttp="{{false}}" indicatorDots="{{false}}" autoplay="{{true}}" indicatorCount="{{true}}" radiusRpx="0" bind:onClickAd="onClickAdListener" bind:clickSwiperItem="onClickSwiperItem" />
    </view>
    <view class="item-layout">
      <view class="price-box flex align-items-center justify-content-between">
        <view class="itemprice">
          <text class="uom">¥</text>
          <text class="current">{{dInfo.price?wxsUtil.moneyFormatInt(dInfo.price,'int'):'暂无报价'}}</text>
          <text>{{wxsUtil.moneyFormatInt(dInfo.price)}}</text>
        </view>
        <view class="stock-count">库存 {{dInfo.totalQty}}</view>
      </view>
      <view class="name-box">
        <view class="itemname">{{dInfo.itemName}}</view>
        <view class="vendorname">{{dInfo.vendorName}}</view>
      </view>
    </view>
    <view class="more-layout" id="more_layout">
      <van-tabs active="{{ activeMoreValue }}" bind:click="onMoreTabChange">
        <van-tab title="图文详情" name="2">
          <image class="pic-card" mode="widthFix" wx:for="{{desPicUrl}}" wx:key="index" src="{{item}}"  lazy-load></image>
        </van-tab>
      </van-tabs>
    </view>
    <view class="placeholder-layout"></view>
  </scroll-view>
  <view wx:if="{{dInfo.qtyOnhand=='无货'}}" class="none-stock-layout">该商品已无货，您可以看看别的～</view>
  <view class="foot-layout flex justify-content-end" hidden="{{!footlayoutAble}}">
    <view class="add" data-type="{{1}}" catchtap="onClickShowAddCart">加入结账台</view>
    <view class="buy" data-type="{{2}}" catchtap="onClickShowAddCart">立即开单</view>
  </view>
  <retailAddCarts type="{{addType}}" bind:onClose="onCloseAddCarts" show="{{showAddCartsPop}}" itemInfo="{{dInfo}}"></retailAddCarts>
  <pre-view show="{{showPreView}}" list="{{preImageUrl}}"></pre-view>
</view>