<view class="root-layout">
    <!-- 头部导航 -->
    <view class="head" id="head-layout" style="padding-top: {{windowPaddingTop}}rpx;height: {{headHeight}}rpx">
        <view class="head-left">
            <van-icon name="arrow-left" bind:tap="onClickBack" size="20" />
            <view class="head-title">零售退货</view>
        </view>
        <view class="head-right" bindtap="onClickToReturnPlatform">
            <van-icon size="22" info="{{cartCount}}" info-class="head-info" name="/asset/imgs/purchase/return-platform.png"></van-icon>
            <view class="return-platform">退货台</view>
        </view>
    </view>
    <!-- 搜索区域 -->
    <van-sticky class="search-layout" id="search-layout">
        <van-search value="{{ keyword }}" placeholder="请输入商品名称" use-action-slot bind:change="onChangeKeyword" bind:search="onSearch">
            <view slot="action" class="search-right" bind:tap="onSearch">搜索</view>
        </van-search>
        <van-button custom-class="right-order"  color="linear-gradient(to right, #00b9c3,#22d3dc)" block catchtap="onClickOrderScan">扫码立即开单</van-button>
    </van-sticky>
    <!-- 列表区域 -->
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
        <block wx:if="{{dataList.length > 0}}">
            <view class="listItemLayout" wx:for="{{dataList}}" wx:key="id">
                <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load />
                <view class="good-content">
                    <view>
                      <view class="good-name">{{item.itemName}}</view>
                      <view class="good-specs">{{item.specs||""}}</view>
                    </view>
                    <view class="good-content-bottom">
                        <view class="good-price">￥{{item.price || '暂无报价'}}</view>
                        <view class="add-to-return" data-item="{{item}}" bindtap="onClickAddToReturn">加入退货台</view>
                    </view>
                </view>
            </view>
        </block>
        <view class="m-t-25p" wx:else>
          <noneView ></noneView>
        </view>
    </listView>
    <van-popup show="{{ showPopup }}" round position="bottom" custom-style="height: 55%;">
        <view class="popup-block">
            <van-sticky>
                <view class="popup-head flex">
                    <view>加入退货台</view>
                    <van-icon catchtap="onCloseAdd" class="closeIcon" size="20" color="#8A8A8A" name="cross" />
                </view>
            </van-sticky>
            <view class="popup-content">
                <view class="flex m-t-24">
                    <image class="good-img" src="{{addItem.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                    <view class="content-right flexbox flex">
                        <view class="good-info flex">
                            <view>
                              <view class="good-name">
                                  {{addItem.itemName}}
                              </view>
                              <view class="good-specs">
                                  {{addItem.specs||""}}
                              </view>
                            </view>
                            <view class="good-price flex">¥{{addItem.price || '暂无报价'}}</view>
                        </view>
                    </view>
                </view>
                <van-cell border="{{false}}" title="数量">
                    <van-stepper value="{{ addItem.applyQty }}" min="0" catch:change="onChangeApplyQty" />
                </van-cell>
            </view>
            <view class="popup-footer flex align-items-center">
                <view class="footer-btn btn-confirm" catchtap="onClickConfirmAdd">提交</view>
            </view>
        </view>
    </van-popup>
</view>