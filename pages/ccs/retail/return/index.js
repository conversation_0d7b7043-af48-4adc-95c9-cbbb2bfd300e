// pages/ccs/retail/return/index.js
const App = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    keyword: "",
    windowPaddingTop: 48,
    headHeight: 64,
    listViewH: 500,
    dataList: [],
    pageIndex: 1,
    pageSize: 10,
    showPopup: false,
    addItem: {},
    applyQty: 0,
    cartCount: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.initPage();
    this.getGoodList()
    this.getReturnPlatformCount()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageIndex: 1,
      dataList: [],
    });
    this.getGoodList();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  initPage() {
    
    const rect = wx.getMenuButtonBoundingClientRect();
    const query = wx.createSelectorQuery();
    query.select("#search-layout").boundingClientRect();
    const that = this
    wx.getSystemInfo({
      success: function (res) {
        query.exec(function (exceRes) {
          let scrollH2 = res.windowHeight - exceRes[0].bottom - App.globalData.deviceBottomOccPx;
          that.setData({
            listViewH: scrollH2,
            windowPaddingTop: rect.top * App.globalData.pxToRpxRatio,
            headHeight: rect.height * App.globalData.pxToRpxRatio,
          });
        });
      }
    })
    
  },
  // 加载更多事件
  onLoadMore: function () {
    this.setData({
      pageIndex: this.data.pageIndex + 1,
    });
    this.getGoodList();
  },
  onClickBack() {
    wx.switchTab({
      url: '/pages/ccs/retail/index'
    })
  },
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail,
    });
  },
  onSearch() {
    this.setData({
      pageIndex: 1,
      dataList: [],
    });
    this.getGoodList();
  },
  // 获取商品列表
  getGoodList() {
    const url = '/api/psi/rtReturnOutBill/myx/selectRtBackItem'
    const custInfo = wx.getStorageSync('curCust')
    const params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        custId: custInfo.custId,
        keyword: this.data.keyword
      }
    }
    App.getHttp()._post(url,params).then(res => {
      if(res && res.length > 0) {
        let dataList = this.data.pageIndex === 1 ? [] : this.data.dataList
        this.setData({
          dataList: dataList.concat(res)
        })
      }
    })
  },
  // 关闭弹框
  onCloseAdd() {
    this.setData({
      showPopup: false
    })
  },
  onClickAddToReturn(e) {
    const item = e.currentTarget.dataset.item
    this.setData({
      showPopup: true,
      addItem: item,
      applyQty: 0
    })
  },
  onChangeApplyQty(e) {
    this.setData({
      applyQty: e.detail
    })
  },
  // 加入退货台
  onClickConfirmAdd() {
    if(this.data.applyQty === 0) {
      wx.showToast({
        icon: 'none',
        title: '请输入大于0的数量'
      })
      return false
    }
    const url = '/api/psi/invCheckstand/addInvCheckstand'
    const supInfo = wx.getStorageSync('supInfo')
    const custInfo = wx.getStorageSync('curCust')
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: custInfo.custId,
      invoiceCustCode: custInfo.custCode,
      itemId: this.data.addItem.itemId,
      itemCode: this.data.addItem.itemCode,
      qty: this.data.applyQty,
      standType: 3 // 1 零售台 3 零售退货台
    }
    App.getHttp()._post(url,params).then(res => {
      this.setData({
        showPopup: false,
        addItem: {},
        applyQty: 0
      })
      this.getReturnPlatformCount()
      setTimeout(() => {
        wx.showToast({
          icon: 'success',
          title: '添加成功'
        })
      }, 500);
    })
  },
  // 获取退货台数量
  getReturnPlatformCount() {
    const url = '/api/psi/invCheckstand/getRtBackList'
    const supInfo = wx.getStorageSync('supInfo')
    const curCust = wx.getStorageSync('curCust')
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: curCust.custId,
    }
    App.getHttp()._post(url,params).then(res => {
      if(res && res.length > 0) {
        this.setData({
          cartCount: res.length
        })
      } else {
        this.setData({
          cartCount: ''
        })
      }
    })
  },
  onClickToReturnPlatform() {
    wx.navigateTo({
      url: '/pages/ccs/retail/returnPlatform/index'
    })
  },
   // 扫码开单
   onClickOrderScan(){
    wx.scanCode({
      success: (res)=> {
        if(res.errMsg === 'scanCode:ok'){
          this.getScanGoods(res.result)
        }else{
          wx.showToast({
            title: res.errMsg,
          })
        }
      }
    })
  },
   //获取商品内容
   async getScanGoods(barCode) {
     //整理数据, 跳转结账台
     const supInfo = wx.getStorageSync("supInfo");
     const curCust = wx.getStorageSync("curCust");
     const addParams = {
       invoiceSetsOfBooksId: supInfo.setsOfBooksId,
       invoiceCustId: curCust.custId,
       invoiceCustCode: curCust.custCode,
       standType: 3,
       barCode: barCode
     };
    App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', addParams).then(res => {
      this.onClickToReturnPlatform()
    })
    // const result = await App.getHttp()._post('/api/psi/barCodeFlow/scanShimgeRtReturnGroupBarCode', { barCode })
    // console.log('getScanGoods',result && result.length > 0)
    //  if (result && result.length > 0) {
    //   //整理数据, 跳转结账台
    //   const supInfo = wx.getStorageSync("supInfo");
    //       const curCust = wx.getStorageSync("curCust");
    //       const addParams = {
    //         invoiceSetsOfBooksId: supInfo.setsOfBooksId,
    //         invoiceCustId: curCust.custId,
    //         invoiceCustCode: curCust.custCode,
    //         standType: 3,
    //         barCode: res.result
    //       };
    //   App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', addParams).then(res => {
    //     this.onClickToReturnPlatform()
    //   })
    // } else {
    //   wx.showModal({
    //     title: '无效条码',
    //     content: '条码不存在,请检查!',
    //     showCancel: false
    //   })
    // }
  }
});
