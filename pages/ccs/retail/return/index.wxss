/* pages/ccs/retail/return/index.wxss */
.search-right {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
  margin: 0 24rpx;
}
.head,
.head-left,
.head-right {
  display: flex;
  align-items: center;
}
.head {
  padding-left: 24rpx;
  padding-bottom: 26rpx;
}
.head-title {
  font-size: 36rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 42rpx;
  margin-left: 16rpx;
}
.head-right {
  margin-left: 140rpx;
}
.return-platform {
  font-size: 36rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 52rpx;
  margin-left: 8rpx;
}
.head-info {
  right: -124rpx !important;
}
.listItemLayout {
  display: flex;
  margin: 24rpx;
  padding: 24rpx;
}
.good-img {
  height: 160rpx;
  width: 160rpx;
  margin-right: 32rpx;
}
.good-content {
  flex: 1;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.good-name {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-specs{
  font-size: 26rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8A8A8A;
  line-height: 36rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-content-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.good-price {
  font-size: 32rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 32rpx;
}
.add-to-return {
  border-radius: 8rpx;
  border: 1px solid #dbdbdb;
  padding: 14rpx 32rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
}
.popup-head {
  position: relative;
  padding: 24rpx 0;
  justify-content: center;
}
.closeIcon {
  position: absolute;
  right: 32rpx;
}
.popup-content {
  padding: 0 24rpx;
}
.good-info {
    flex-direction: column;
    justify-content: space-between;
}
.popup-footer {
  justify-content: space-around;
  position: fixed;
  bottom: 40rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 8rpx 24rpx;
  border-top: 1px solid #E6E6E6;
}
.footer-btn {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 48rpx;
  border-radius: 8rpx;
  padding: 16rpx 0rpx;
  text-align: center;
  border: 1px solid #dbdbdb;
  width: 100%;
}
.btn-confirm {
  background: #00b9c3;
  color: #fff;
  border: none;
}
/* 重写页面组件的样式 */
.search-layout .van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}
