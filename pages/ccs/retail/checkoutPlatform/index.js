// pages/ccs/retail/checkoutPlatform/index.js
const App = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    dataList: [],
    checkedList: [],
    curIdx: 0,
    showDialog: false,
    curPrice: '',
    checkAll: false,
    scrollHeight: 1060,
    showPicker: false,
    warehouseOptions: [],
    collapseNames: [],
    isEdit: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getCheckoutPlatformList();
    this.getWarehouseList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    const that = this
    const query = wx.createSelectorQuery();
    query.select("#footer-layout").boundingClientRect();
    query.select("#edit-layout").boundingClientRect();
    wx.getSystemInfo({
      success: function (res) {
        query.exec(function (exceRes) {
          let scrollH2 = res.windowHeight - exceRes[0].height - exceRes[1].height
          that.setData({
            scrollHeight: (scrollH2 - 12) * App.globalData.pxToRpxRatio,
          });
        });
      }
    })
   },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
   
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() { },
  // 获取仓库列表
  getWarehouseList() {
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          text: res[r].name,
          id: res[r].id
        })
      }
      this.setData({
        warehouseOptions: warehouseColumns
      })
    })
  },
  // 获取结账台列表
  getCheckoutPlatformList() {
    const url = "/api/psi/invCheckstand/getRtList";
    const supInfo = wx.getStorageSync("supInfo");
    const curCust = wx.getStorageSync("curCust");
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: curCust.custId,
    };
    App.getHttp()
      ._post(url, params)
      .then((res) => {
        if (res && res.length > 0) {
          this.setData({
            dataList: res.map(item => {
              return {
                ...item,
              }
            }),
          });
        } else {
          this.setData({
            dataList: []
          })
        }
      });
  },
  onClickShowPicker(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      showPicker: true,
      curIdx: index
    })
  },
  // 修改选择
  onChangeCheck(e) {
    const list = this.data.dataList.filter(item => e.detail.includes(item.itemId))
    if (list.length === 0) {
      this.setData({
        checkAll: false
      })
    } else {
      if (this.data.checkAll && list.length != this.data.dataList.length) {
        this.setData({
          checkAll: false
        })
      } else if (!this.data.checkAll && list.length === this.data.dataList.length) {
        this.setData({
          checkAll: true
        })
      }
    }
    this.setData({
      checkedList: e.detail,
    })
  },
  // 切换全选
  onChangeAll(e) {
    const ids = this.data.dataList.map(item => item.itemId)
    this.setData({
      checkAll: e.detail,
      checkedList: e.detail ? ids : [],
    })
  },
  // 点击改价
  onClickEdit(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      curIdx: index,
      showDialog: true
    })
  },
  // 关闭改价
  onCloseDialog() {
    this.setData({
      curPrice: '',
      curIdx: 0
    })
  },
  // 修改价格
  onChangePrice(e) {
    this.setData({
      curPrice: e.detail
    })
  },
  // 确认改价
  onConfirmChangePrice() {
    const key = `dataList[${this.data.curIdx}].price`
    this.setData({
      [key]: this.data.curPrice
    })
  },
  // 开单
  onClickConfirm() {
    if (this.data.checkedList.length === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      })
      return false
    }
    const list = this.data.dataList.filter(item => this.data.checkedList.includes(item.itemId))
    if (list.find(item => !item.qty || item.qty === 0)) {
      wx.showToast({
        title: '请填写商品数量',
        icon: 'none'
      })
      return false
    }
    if (list.find(item => !item.price)) {
      wx.showToast({
        title: '请填写商品价格',
        icon: 'none'
      })
      return false
    }
    if (list.find(item => !item.warehouseId)) {
      wx.showToast({
        title: '请选择仓库',
        icon: 'none'
      })
      return false
    }
    const curCust = wx.getStorageSync("curCust");
    if (!curCust || !curCust.custId) {
      wx.showToast({
        title: '缺少门店信息!',
        icon: 'none'
      })
      return
    }
    wx.setStorageSync('createOrderInfo', list)
    wx.navigateTo({
      url: '/pages/ccs/retail/createOrder/index'
    })
  },
  // 右滑删除商品
  onCloseCell(e) {
    const index = e.currentTarget.dataset.index
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: (res) => {
        const url = '/api/psi/invCheckstand/delete'
        const ids = [e.currentTarget.dataset.id]
        App.getHttp()
          ._post(url, { ids }, true)
          .then(res => {
            // this.getCheckoutPlatformList()
            if (res.code === 200) {
              let checkIdx = this.data.checkedList.findIndex(item => item === e.currentTarget.dataset.id)
              let checkedList = this.data.checkedList
              checkedList.splice(checkIdx, 1)
              let dataList = this.data.dataList
              dataList.splice(index, 1)
              this.setData({
                dataList,
                checkedList
              })
              if (checkedList.length === 0) {
                this.setData({
                  checkAll: false
                })
              }
            }
          })
      }
    })
  },
  // 关闭选择仓库
  onCancelWarehouse() {
    this.setData({
      showPicker: false
    })
  },
  // 确认选择仓库
  onConfirmWarehouse(e) {
    const key1 = `dataList[${this.data.curIdx}].warehouseId`
    const key2 = `dataList[${this.data.curIdx}].warehouseName`
    const detail = e.detail.value
    this.setData({
      [key2]: detail.text,
      [key1]: detail.id,
      showPicker: false
    })
    this.getOnhand(detail.id)
  },
  // 获取对应仓库的库存
  getOnhand(warehouseId) {
    const url = "/api/psi/currentInv/selectItemStorage";
    const params = {
      itemId: this.data.dataList[this.data.curIdx].itemId,
      warehouseId: warehouseId,
    };
    const key1 = `dataList[${this.data.curIdx}].qty`
    const key2 = `dataList[${this.data.curIdx}].qtyAvi`
    App.getHttp()
      ._post(url, params)
      .then((res) => {
        if (res && res.length > 0) {
          this.setData({
            // [key1]: res[0].qtyAvi || 0,
            [key2]: res[0].qtyAvi || 0,
          })
        }
      });
  },
  onChangeApplyQty(e) {
    const index = e.currentTarget.dataset.index
    const key = `dataList[${index}].qty`
    this.setData({
      [key]: e.detail
    })
  },
  onChangeEdit() {
    const isEdit = !this.data.isEdit
    this.setData({
      isEdit
    })
  },
  // 删除多个商品
  onClickDelete() {
    if (this.data.checkedList.length === 0) {
      wx.showToast({
        icon: 'none',
        title: '请选择要删除的商品'
      })
      return false
    }
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: (res) => {
        const url = '/api/psi/invCheckstand/delete'
        const list = this.data.dataList.filter(item => this.data.checkedList.includes(item.itemId))
        const ids = list.map(item => (item.id))
        App.getHttp()
          ._post(url, { ids }, true)
          .then(res => {
            if (res.code === 200) {
              let dataList = this.data.dataList.filter(item => !ids.includes(item.id))
              this.setData({
                dataList,
                checkedList: [],
                checkAll: false
              })
            }
          })
      }
    })
  },
  // 扫码扣减商品
  onClickReduceScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/scanPDA/retailPlatform/index?mode=reduce',
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.getCheckoutPlatformList();
            }
          },
        }
      })
    } else {
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const supInfo = wx.getStorageSync("supInfo");
            const curCust = wx.getStorageSync("curCust");
            const addParams = {
              invoiceSetsOfBooksId: supInfo.setsOfBooksId,
              invoiceCustId: curCust.custId,
              invoiceCustCode: curCust.custCode,
              standType: 1,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/invCheckstand/decreaseInvCheckstand', addParams)
            this.getCheckoutPlatformList();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail: (err) => {
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 扫码增加商品
  onClickAddScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/scanPDA/retailPlatform/index?mode=add',
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.getCheckoutPlatformList();
            }
          },
        }
      })
    } else {
      // 允许从相机和相册扫码
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const supInfo = wx.getStorageSync("supInfo");
            const curCust = wx.getStorageSync("curCust");
            const addParams = {
              invoiceSetsOfBooksId: supInfo.setsOfBooksId,
              invoiceCustId: curCust.custId,
              invoiceCustCode: curCust.custCode,
              standType: 1,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', addParams)
            this.getCheckoutPlatformList();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail: (err) => {
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 折叠监听
  onChangeCollape(event) {
    this.setData({
      collapseNames: event.detail,
    });
  },
});
