<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
    <view class="edit-layout" id="edit-layout">
        <text bindtap="onChangeEdit">{{isEdit ? '完成' : '编辑'}}</text>
    </view>
    <scroll-view class="scroll-layout" style="height: {{scrollHeight}}rpx" scroll-y>
        <van-checkbox-group value="{{ checkedList }}" bind:change="onChangeCheck">
          <van-collapse  value="{{ collapseNames }}" bind:change="onChangeCollape">
            <van-swipe-cell wx:for="{{dataList}}" wx:key="id" async-close  right-width="{{ 70 }}">
                <view class="good-content">
                    <view class="good-head">
                        <van-field placeholder="请选择仓库" bind:click-input="onClickShowPicker" data-index="{{index}}" readonly is-link label="{{item.warehouseName}}" value="{{item.qtyAvi?('库存 '+item.qtyAvi):''}}" input-align="right"></van-field>
                    </view>
                    <view class="good-info">
                        <van-checkbox name="{{item.itemId}}"></van-checkbox>
                        <image class="good-img" mode="aspectFit" src="{{item.itemUrl}}" lazy-load />
                        <view class="good-info-right">
                            <view>
                              <view class="good-name">{{item.itemName}}</view>
                              <view class="specs  single-line-ellipsis">{{item.specs}}</view>
                            </view>
                            <view class="price-num">
                                <view class="good-price">
                                    <view>{{item.price||0}}</view>
                                    <van-icon bindtap="onClickEdit" data-index="{{index}}" size="18" color="#8A8A8A" name="edit" />
                                </view>
                                <van-stepper value="{{ item.qty }}" data-index="{{index}}" min="0" max="{{item.qtyAvi}}" catch:change="onChangeApplyQty" />
                            </view>
                        </view>
                    </view>
                    <van-collapse-item  name="{{'collapse'+index}}" wx:if="{{item.barCodeFlowResponseDTOList}}">
                      <view slot="title">已扫码信息({{item.barCodeFlowResponseDTOList.length}})</view>
                      <view slot="value">时间</view>
                      <view class="flex font-sub-hint m-b-12" wx:for="{{item.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex" >
                        <view class="bar-code">条码：{{barCodeItem.barCode}}  扫码时间：{{barCodeItem.createTime}}</view>
                      </view>
                    </van-collapse-item>
                </view>
                <view slot="right" data-id="{{item.id}}" data-index="{{index}}" class="van-swipe-cell__right" bindtap="onCloseCell">删除</view>
            </van-swipe-cell>
          </van-collapse>
        </van-checkbox-group>
    </scroll-view>
    <view class="footer-layout" id="footer-layout">
      <scan-btns routeName="RetailCheckoutPlatform" catch:clickReduceScan="onClickReduceScan" catch:clickAddScan="onClickAddScan"/>
      <!-- <view class="flex scan-btn-layout">
        <view class="flexbox scan-btn" catchtap="onClickReduceScan">扫码扣减商品</view>
        <view class="scan-holder"></view>
        <view class="flexbox scan-btn" catchtap="onClickAddScan">扫码添加商品</view>
        <van-checkbox value="{{ checked }}" bind:change="onChange">连扫</van-checkbox>
      </view> -->
      <view class="footer flex align-items-center justify-content-between">
        <view class="footer-left">
            <van-checkbox value="{{ checkAll }}" bind:change="onChangeAll">全选</van-checkbox>
            <view class="footer-total">
                <view class="total-qty">
                    共{{wxsUtil.retailOrderTotalQty(dataList, checkedList)}}件
                </view>
                <view class="total-amount">
                    <text class="amount-label">合计:</text>
                    <text class="amount-text">￥{{wxsUtil.retailOrderTotalAmount(dataList, checkedList)}}</text>
                </view>
            </view>
        </view>
        <block>
            <view class="footer-btn del-btn" bindtap="onClickDelete" wx:if="{{isEdit}}">删除</view>
            <view class="footer-btn" bindtap="onClickConfirm" wx:else>开单</view>
        </block>
        </view>
    </view>
    <van-dialog use-slot title="改价" id="price-dialog" show="{{ showDialog }}" show-cancel-button confirm-button-color="#00b9c3" bind:confirm="onConfirmChangePrice" bind:close="onCloseDialog">
        <view class="changePrice">
            <van-field value="{{curPrice}}" type="number" bind:change="onChangePrice" placeholder="请输入改价金额"></van-field>
        </view>
    </van-dialog>
    <van-dialog id="van-dialog" />
    <!-- 选择仓库 -->
    <van-popup show="{{ showPicker }}" round position="bottom" custom-style="height: 60%" bind:click-overlay="onCancelWarehouse">
        <van-picker columns="{{ warehouseOptions }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
    </van-popup>
</view>