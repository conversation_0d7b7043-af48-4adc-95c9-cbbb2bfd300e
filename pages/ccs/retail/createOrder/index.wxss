.root-layout {
    padding-bottom: 160rpx;
    overflow: auto;
}
.order-info {
    border-radius: 8rpx;
    margin: 24rpx;
}
.goods-layout {
    background-color: #fff;
    margin: 24rpx;
}
.good-head {
  border-radius: 8rpx 8rpx 0 0;
  border-bottom: 1px solid #E6E6E6;
  /* font-size: 28rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 36rpx;
    padding: 24rpx 32rpx; */
}
.good-info {
    display: flex;
    padding: 24rpx 32rpx;
}
.good-img {
    height: 160rpx;
    width: 160rpx;
    margin-right: 32rpx;
}
.good-name-box{
  flex: 1;
  font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 36rpx;
}
.good-name-box .name {
    color: #242424;
    height: 72rpx;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap: break-word;
    white-space: normal;
}
.good-name-box .specs {
  color: #8A8A8A;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-info-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
}
.good-price {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #242424;
    line-height: 36rpx;
}
.good-qty {
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 24rpx;
}
.good-amount {
    display: flex;
    justify-content: flex-end;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 24rpx;
    padding: 8rpx 32rpx 24rpx 0;
    border-bottom: 1px solid #E6E6E6;

}
.amount-label {
    font-size: 24rpx;
}
.amount-text {
    font-size: 32rpx;
}
.ship-info {
    margin: 24rpx;
    border-radius: 8rpx;
}
.footer {
    z-index: 99;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 32rpx 32rpx 52rpx 32rpx;
}
.footer-qty {
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 24rpx;
    margin-bottom: 8rpx;
}
.footer-amount {
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #3D3D3D;
    line-height: 24rpx;
}
.footer-amount-label {
    font-size: 24rpx;
}
.footer-amount-text {
    font-size: 32rpx;
}
.footer-btn {
    background: #00b9c3;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 36rpx;
    padding: 14rpx 32rpx;
}
.label-layout {
    margin-left: 10rpx;
}
.change-price {
  padding: 48rpx;
}
.change-price .van-cell {
  background: #f5f5f5 !important;
  border-radius: 8rpx;
}

.bar-code{
  word-break: break-all;
}
.m-b-12{
  margin-bottom: 12rpx;
}