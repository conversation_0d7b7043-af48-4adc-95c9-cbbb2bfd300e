<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
  <!-- 表头信息 -->
  <view class="order-info">
    <van-field label-class="label-layout"  label="联系电话" maxlength="11" type="digit" clearable placeholder="请输入" input-align="right" data-field="telPhone" bind:change="onChangeField" value="{{telPhone}}"></van-field>
    <van-field label-class="label-layout"  label="联系人" clearable placeholder="请输入" input-align="right" data-field="contactPerson" bind:change="onChangeField" value="{{contactPerson}}"></van-field>
    <van-field label-class="label-layout"  model:value="{{regionText}}" label="所在地区" readonly is-link placeholder="请选择" clickable input-align="right" bindtap="openCitySelect" />
    <van-field label-class="label-layout"  type="textarea" autosize clearable label="详细地址" placeholder="请输入" data-field="address" bind:change="onChangeField" input-align="right" value="{{address}}"></van-field>
  </view>
  <!-- 商品信息 -->
  <van-collapse value="{{ collapseNames }}" bind:change="onChangeCollape">
    <view class="goods-layout" wx:for="{{goodList}}" wx:key="index">
      <!-- <view class="good-head">{{item.warehouseName}}</view> -->
      <view class="good-head">
        <van-field placeholder="" bind:click-input="onClickShowPicker" data-index="{{index}}" readonly is-link label="{{item.warehouseName}}"  input-align="right"></van-field>
      </view>
      <view class="good-info">
        <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load />
        <view class="good-name-box">
          <view class="name">{{item.itemName}}</view>
          <view class="specs">{{item.specs}}</view>
        </view>
        <view class="good-info-right">
          <view class="good-price" catchtap="onClickEdit" data-price="{{item.price}}" data-index="{{index}}">
            {{item.price ? '￥' + item.price : '暂无报价'}}
            <view class="text-right">
              <van-icon size="18" color="#00b9c3" name="edit" />
            </view>
          </view>
          <view class="good-qty">共{{item.qty}}件</view>
        </view>
      </view>
      <view class="good-amount">
        <view class="amount-label">合计:</view>
        <view class="amount-text">￥{{item.qty * (item.price || 0)}}</view>
      </view>
      <van-collapse-item name="{{'collapse'+index}}" wx:if="{{item.barCodeFlowResponseDTOList}}">
        <view slot="title">已扫码信息({{item.barCodeFlowResponseDTOList.length}})</view>
        <view slot="value">时间</view>
        <view class="flex font-sub-hint m-b-12" wx:for="{{item.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex">
          <view class="bar-code">条码：{{barCodeItem.barCode}} 扫码时间：{{barCodeItem.createTime}}</view>
        </view>
      </van-collapse-item>
    </view>
  </van-collapse>
  <!-- 配送信息 -->
  <view class="ship-info">
    <van-field label-class="label-layout"  label="配送方式" readonly is-link placeholder="请选择" input-align="right" bind:click-input="onClickShowAction" data-type="shipMode" value="{{shipModeName}}"></van-field>
    <picker mode="date" bindchange="bindDateChange" value="{{planDeliveryDate}}">
      <van-field label-class="label-layout" required="{{shipMode && shipMode != 5}}" label="配送时间" is-link placeholder="请选择" readonly input-align="right" value="{{planDeliveryDate}}"></van-field>
    </picker>
    <van-field label-class="label-layout"  label="收款方式" readonly is-link placeholder="请选择" input-align="right" bind:click-input="onClickShowAction" data-type="orderPayType" value="{{payTypeName}}"></van-field>
    <van-field label-class="label-layout" type="textarea" autosize clearable label="备注" placeholder="选填" data-field="remark" bind:change="onChangeField" input-align="right" value="{{remark}}"></van-field>
  </view>
  <view class="footer">
    <view class="footer-left">
      <view class="footer-qty">共{{wxsUtil.retailOrderTotalQty(goodList, checkedList)}}件</view>
      <view class="footer-amount">
        <text class="footer-amount-label">合计:</text>
        <text class="footer-amount-text">￥{{wxsUtil.retailOrderTotalAmount(goodList,checkedList)}}</text>
      </view>
    </view>
    <view class="footer-btn" bindtap="onConfirmOrder">提交订单</view>
  </view>
  <!-- 配送方式/收款方式 -->
  <van-action-sheet show="{{ showAction }}" actions="{{ actionsOptions }}" bind:select="onSelectAction" bind:cancel="onCloseAction" cancel-text="取消" />
  <van-popup show="{{showPicker}}" bind:close="onCloseCitySelect" class="van-popup-city-select" round position="bottom" :lock-scroll="true">
    <city-select bind:addressChange="citySelectChange" bind:closeCitySelect="onCloseCitySelect" />
  </van-popup>
  <van-dialog use-slot title="改价" id="price-dialog" show="{{ showPriceDialog }}" show-cancel-button confirm-button-color="#00b9c3" bind:confirm="onConfirmChangePrice" bind:close="onClosePriceDialog">
    <view class="change-price">
      <van-field value="{{priceInfo.price}}" type="number" bind:change="onChangePrice" placeholder="请输入改价金额"></van-field>
    </view>
  </van-dialog>
  <!-- 选择仓库 -->
  <van-popup show="{{ showWarehousePicker }}" round position="bottom" custom-style="height: 60%" bind:click-overlay="onCancelWarehouse">
        <van-picker columns="{{ warehouseOptions }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
    </van-popup>
</view>