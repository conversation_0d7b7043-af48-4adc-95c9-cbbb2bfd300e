// pages/ccs/retail/createOrder/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    contactPerson: '',
    telPhone: '',
    address: '',
    region: [],
    regionText: '',
    goodList: [],
    checkedList: [], // 计算合计需要的数组
    actionsOptions: [],
    showAction: false,
    actionType: '',
    shipModeName: '',
    shipMode: '', // 配送方式
    payType: '',
    payTypeName: '', // 收款方式
    remark: '',
    planDeliveryDate: '',
    showPicker: false,
    showPriceDialog: false,
    priceInfo: { index: -1, price: '' },
    collapseNames: [],
    showPicker:false,
    showWarehousePicker:false,
    warehouseOptions:[],
    curIdx:0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getWarehouseList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const goodList = wx.getStorageSync('createOrderInfo')
    if (goodList) {
      this.setData({
        goodList,
        checkedList: goodList.map(item => (item.itemId))
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 切换省市区
  bindRegionChange(e) {
    this.setData({
      region: e.detail.value,
      regionText: e.detail.value.join('')
    })
  },
  // 输入框内容修改
  onChangeField(e) {
    const field = e.currentTarget.dataset.field
    this.setData({
      [field]: e.detail
    })
  },
  bindDateChange(e) {
    this.setData({
      planDeliveryDate: e.detail.value
    })
  },
  onClickShowAction(e) {
    const type = e.currentTarget.dataset.type
    const typeDict = wx.getStorageSync('dictMap')[type]
    this.setData({
      actionsOptions: typeDict,
      showAction: true,
      actionType: type
    })
  },
  onCloseAction() {
    this.setData({
      showAction: false
    })
  },
  onSelectAction(e) {
    const detail = e.detail
    if (this.data.actionType === 'shipMode') {
      this.setData({
        shipMode: detail.value,
        shipModeName: detail.name
      })
    } else {
      this.setData({
        payType: detail.value,
        payTypeName: detail.name
      })
    }
    this.setData({
      showAction: false
    })
  },
  // 确认提交订单
  onConfirmOrder() {
    // if (!this.data.telPhone) {
    //   wx.showToast({
    //     title: '请填写联系电话',
    //     icon: 'none'
    //   })
    //   return false
    // }
    // if (!this.data.contactPerson) {
    //   wx.showToast({
    //     title: '请填写联系人',
    //     icon: 'none'
    //   })
    //   return false
    // }
    // if (this.data.region.length === 0) {
    //   wx.showToast({
    //     title: '请选择所在地区',
    //     icon: 'none'
    //   })
    //   return false
    // }
    // if (!this.data.address) {
    //   wx.showToast({
    //     title: '请填写详细地址',
    //     icon: 'none'
    //   })
    //   return false
    // }
    // if (!this.data.shipMode) {
    //   wx.showToast({
    //     title: '请选择配送方式',
    //     icon: 'none'
    //   })
    //   return false
    // }
    if (this.data.shipMode == 1 && !this.data.planDeliveryDate) {
      wx.showToast({
        title: '请选择配送时间',
        icon: 'none'
      })
      return false
    }
    // if (!this.data.payType) {
    //   wx.showToast({
    //     title: '请选择收款方式',
    //     icon: 'none'
    //   })
    //   return false
    // }
    const url = '/api/psi/rtOutBill/myx/submtByMyx'
    const custInfo = wx.getStorageSync('curCust')
    let exitPriceRrror = false
    const params = {
      custId: custInfo.custId,
      custCode: custInfo.custCode,
      custName: custInfo.custName,
      telPhone: this.data.telPhone,
      shipMode: this.data.shipMode,
      contactPerson: this.data.contactPerson,
      planDeliveryDate: this.data.planDeliveryDate,
      payType: this.data.payType,
      contactAddr: this.data.regionText + this.data.address,
      remark: this.data.remark,
      provinceId: this.data.region[0]?this.data.region[0].id:'',
      provinceName: this.data.region[0]?this.data.region[0].name:'',
      cityId: this.data.region[1]?this.data.region[1].id:'',
      cityName: this.data.region[1]?this.data.region[1].name:'',
      districtId: this.data.region[2]?this.data.region[2].id:'',
      districtName: this.data.region[2]?this.data.region[2].name:'',
      townId: this.data.region[3]?this.data.region[3].id:'',
      townName: this.data.region[3]?this.data.region[3].name:'',
      warehouseId: this.data.goodList[0].warehouseId, // 取第一个的仓库id
      addLineList: this.data.goodList.map(item => {
        if(!exitPriceRrror) exitPriceRrror = (!item.price||item.price<0)
        return {
          barcodeFlowList:item.barCodeFlowResponseDTOList,
          standId: item.id,
          itemId: item.itemId,
          itemCode: item.itemCode,
          warehouseId: item.warehouseId,
          billQty: item.qty,
          pricecBillF: item.price,
          amountBillF: item.price * item.qty
        }
      })
    }
    if(exitPriceRrror){
        wx.showToast({
        title: '商品价格需大于0',
        icon: 'none'
      })
      return 
    }
    App.getHttp()._post(url, params).then(res => {
      wx.removeStorageSync('createOrderInfo')
      wx.redirectTo({
        url: '/pages/ccs/retail/result/index?id=' + res
      })
    })
  },
  openCitySelect: function () {
    // 弹出地址选择
    this.setData({
      showPicker: true
    })
  },
  onCloseCitySelect: function () {
    // 关闭地址选择框
    this.setData({
      showPicker: false
    })
  },
  citySelectChange(e) {
    let res = e.detail
    this.setData({
      showPicker: false
    })
    console.log(res)
    if (res) {
      this.setData({
        region: res,
        regionText: res.map((v) => {
          return v.name
        }).join(' ')
      })
    }
  },
  // 点击改价
  onClickEdit(e) {
    const { index, price } = e.currentTarget.dataset
    this.data.priceInfo.index = index
    this.data.priceInfo.price = price
    this.setData({
      priceInfo:this.data.priceInfo,
      showPriceDialog: true
    })
  },
  // 关闭改价
  onCloseDialog() {
    this.data.priceInfo = { index: -1, price: '' }
    this.data.showPriceDialog = false
  },
  // 修改价格
  onChangePrice(e) {
    this.data.priceInfo.price = e.detail
  },
  // 确认改价
  onConfirmChangePrice() {
    const key = `goodList[${this.data.priceInfo.index}].price`
    this.setData({
      [key]: this.data.priceInfo.price
    })
  },
  // 折叠监听
  onChangeCollape(event) {
    this.setData({
      collapseNames: event.detail,
    });
  },
   // 获取仓库列表
   getWarehouseList() {
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          text: res[r].name,
          id: res[r].id
        })
      }
      this.setData({
        warehouseOptions: warehouseColumns
      })
    })
  },
  onClickShowPicker(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      showWarehousePicker: true,
      curIdx: index
    })
  },
   // 关闭选择仓库
   onCancelWarehouse() {
    this.setData({
      showWarehousePicker: false
    })
  },
  // 确认选择仓库
  onConfirmWarehouse(e) {
    const key1 = `goodList[${this.data.curIdx}].warehouseId`
    const key2 = `goodList[${this.data.curIdx}].warehouseName`
    const detail = e.detail.value
    this.setData({
      [key2]: detail.text,
      [key1]: detail.id,
      showWarehousePicker: false
    })
  },
})