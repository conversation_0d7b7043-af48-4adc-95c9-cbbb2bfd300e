<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
  <view class="line"></view>
  <view id="head-layout">
    <van-field label-class="label-layout" required label="退货仓" readonly right-icon="arrow" placeholder="请选择" input-align="right" value="{{curWarehouse.text}}" bind:click-icon="checkkWarehouse" bind:click-input="checkkWarehouse"></van-field>
    <van-field label-class="label-layout" label="退货原因" type="textarea" autosize clearable placeholder="选填" input-align="right" data-field="returnReason" bind:change="onChangeField" value="{{returnReason}}"></van-field>
    <van-field label-class="label-layout"  label="联系电话" maxlength="11" type="digit" clearable placeholder="请输入" input-align="right" data-field="telPhone" bind:change="onChangeField" value="{{telPhone}}"></van-field>
    <van-field label-class="label-layout"  label="联系人" clearable placeholder="请输入" input-align="right" data-field="contactPerson" bind:change="onChangeField" value="{{contactPerson}}"></van-field>
    <van-field label-class="label-layout"  model:value="{{regionText}}" label="所在地区" readonly is-link placeholder="请选择" clickable input-align="right" bindtap="openCitySelect" />
    <van-field label-class="label-layout"  type="textarea" autosize clearable label="详细地址" placeholder="请输入" data-field="address" bind:change="onChangeField" input-align="right" value="{{address}}"></van-field>
  </view>
  <view class="edit-layout" id="edit-layout">
    <text bindtap="onChangeEdit">{{isEdit ? '完成' : '编辑'}}</text>
  </view>
  <scroll-view class="list-layout" style="height: {{scrollHeight}}rpx" scroll-y>
    <van-checkbox-group id="check-box" value="{{ checkList }}" bind:change="onChangeCheck">
      <van-collapse value="{{ collapseNames }}" bind:change="onChangeCollape">
          <view class="m-t-12" wx:for="{{dataList}}" wx:key="id">
        <van-swipe-cell  async-close right-width="{{ 70 }}">
            <view class="good-list">
              <van-checkbox class="check-box-item" name="{{item.itemId}}"></van-checkbox>
              <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load />
              <view class="good-content">
                <view>
                  <view class="good-name">{{item.itemName}}</view>
                  <view class="good-specs">{{item.specs}}</view>
                </view>
                <view class="good-content-bootom">
                  <view class="good-price">
                    <view class="price-text">{{item.price || ''}}</view>
                    <van-icon bindtap="onClickEdit" data-index="{{index}}" size="18" color="#8A8A8A" name="edit" />
                  </view>
                  <van-stepper value="{{ item.qty }}" data-index="{{index}}" min="1" catch:change="onChangeApplyQty" />
                </view>

              </view>
            </view>
            <!-- <van-collapse-item name="{{'collapse'+index}}">
              <view slot="title">已扫码信息(2)</view>
              <view slot="value">时间</view>
              <view class="flex align-items-center justify-content-between font-sub-hint">
                <view>98739321392131231</view>
                <view>2023-11-25 15:30:00</view>
              </view>
              <view class="flex align-items-center justify-content-between font-sub-hint">
                <view>98739321392131233</view>
                <view>2023-11-25 15:33:00</view>
              </view>
            </van-collapse-item> -->
            <van-collapse-item name="{{'collapse'+index}}" wx:if="{{item.barCodeFlowResponseDTOList}}">
              <view slot="title">已扫码信息({{item.barCodeFlowResponseDTOList.length}})</view>
              <view slot="value">时间</view>
              <view class="flex font-sub-hint m-b-12" wx:for="{{item.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex">
                <view class="bar-code">条码：{{barCodeItem.barCode}} 扫码时间：{{barCodeItem.createTime}}</view>
              </view>
            </van-collapse-item>
            <view slot="right" data-id="{{item.id}}" data-index="{{index}}" class="van-swipe-cell__right" catchtap="onCloseCell">删除</view>
        </van-swipe-cell>
          </view>
      </van-collapse>
    </van-checkbox-group>
  </scroll-view>
  <view class="footer-layout" id="footer-layout">
    <scan-btns routeName="RetailReturnPlatform" catch:clickReduceScan="onClickReduceScan" catch:clickAddScan="onClickAddScan"/>
    <!-- <view class="flex scan-btn-layout">
      <view class="flexbox scan-btn" catchtap="onClickReduceScan">扫码扣减商品</view>
      <view class="scan-holder"></view>
      <view class="flexbox scan-btn" catchtap="onClickAddScan">扫码添加商品</view>
    </view> -->
    <view class="footer">
      <view class="footer-left">
        <van-checkbox value="{{ checkAll }}" bind:change="onChangeAll">全选</van-checkbox>
        <view class="footer-total">
          <view class="total-qty">
            共{{wxsUtil.retailOrderTotalQty(dataList, checkList)}}件
          </view>
          <view class="total-amount">
            <text class="amount-label">合计:</text>
            <text class="amount-text">￥{{wxsUtil.retailOrderTotalAmount(dataList, checkList)}}</text>
          </view>
        </view>
      </view>
      <block>
        <view class="footer-btn del-btn" bindtap="onClickDelete" wx:if="{{isEdit}}">删除</view>
        <view class="footer-btn" bindtap="onClickConfirm" wx:else>退货</view>
      </block>
    </view>
  </view>
  <van-popup show="{{ showSelectWarhouse }}" round position="bottom" custom-style="height: 60%" bind:click-overlay="onCancelWarehouse">
    <van-picker columns="{{ warehouseColumns }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
  </van-popup>
  <van-dialog use-slot title="改价" show="{{ showDialog }}" show-cancel-button confirm-button-color="#00b9c3" bind:confirm="onConfirmChangePrice" bind:close="onCloseDialog">
    <view class="changePrice">
      <van-field value="{{curPrice}}" type="number" bind:change="onChangePrice" placeholder="请输入改价金额"></van-field>
    </view>
  </van-dialog>
  <van-dialog id="van-dialog" />
  <van-popup show="{{showPicker}}" bind:close="onCloseCitySelect" class="van-popup-city-select" round position="bottom" :lock-scroll="true">
    <city-select bind:addressChange="citySelectChange" bind:closeCitySelect="onCloseCitySelect" />
  </van-popup>
</view>