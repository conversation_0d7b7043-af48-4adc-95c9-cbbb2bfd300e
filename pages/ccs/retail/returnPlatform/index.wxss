.list-layout {
  margin: 24rpx 0;
}
.good-list {
  background-color: #fff;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
}
.good-img {
  height: 160rpx;
  width: 160rpx;
  margin-right: 32rpx;
}
.good-name {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-specs{
  font-size: 26rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8A8A8A;
  line-height: 36rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-content {
    height: 160rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.check-box-item {
    margin: 0 32rpx 0 24rpx;
}
.good-content-bootom {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.good-price {
    font-size: 32rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 32rpx;
    display: flex;
    align-items: center;
}
.price-text {
    margin-right: 10rpx;
}
.changePrice {
    padding: 48rpx;
}
.scan-btn-layout{
  padding: 24rpx;
}
.scan-btn-layout .scan-holder{
  width: 48rpx;
  height: 1rpx;
}
.scan-btn-layout .scan-btn{
  padding: 12rpx 0;
  border-radius: 8rpx;
  background: linear-gradient(to right, #00b9c3,#22d3dc);
  cursor: pointer;
  color: white;
  text-align: center;
}
.footer {
  padding: 32rpx 24rpx 52rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
}
.footer-btn {
  background: #00b9c3;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 36rpx;
  padding: 14rpx 32rpx;
}
.changePrice .van-cell {
    background: #F5F5F5!important;
    border-radius: 8rpx;
}
.footer-left {
  display: flex;
  align-items: center;
}
.footer-total {
  margin-left: 32rpx;
}
.total-qty {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 24rpx;
}
.total-amount {
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #3D3D3D;
  line-height: 24rpx;
}
.amount-label {
  font-size: 24rpx;
}
.amount-text {
  font-size: 32rpx;
}
.van-swipe-cell__right {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 140rpx;
  color: #fff;
  font-size: 28rpx;
  background: #ff4a4d;
}
.edit-layout {
  padding: 24rpx 24rpx 0 24rpx;
  text-align: right;
}
.del-btn {
  background: #ff4a4d;
}
.label-layout {
  margin-left: 10rpx;
}
.bar-code{
  word-break: break-all;
}
.m-b-12{
  margin-bottom: 12rpx;
}