// pages_sub/pages/ccs/down/order/return/returnResult/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    billId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id) {
      this.setData({
        billId: options.id
      })
    }
  },
  onClickCheckDetail(){
    // 跳转到零售退货详情 关闭所有页面
   wx.redirectTo({
     url: '/pages/ccs/retail/order/detail/index?id=' + this.data.billId + '&billType=' + '0207'
   })
  },
  onClickBack(){
    // 跳转到零售退货 关闭所有页面
    wx.redirectTo({
      url: '/pages/ccs/retail/return/index',
    })
  }
 
})