// pages/ccs/retail/returnPlatform/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showSelectWarhouse: false,
    warehouseColumns: [],
    curWarehouse: {},
    returnReason: '',
    scrollHeight: 400,
    checkAll: false,
    dataList: [],
    checkList: [],
    showDialog: false,
    curIdx: 0,
    curPrice: '',
    isEdit: false,
    telPhone: '',
    contactPerson: '',
    address: '',
    region: [],
    collapseNames: [],
    regionText: '',
    showPicker: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getWarehouseColumns()
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    const query = wx.createSelectorQuery();
    query.select("#head-layout").boundingClientRect();
    query.select("#footer-layout").boundingClientRect();
    query.select("#edit-layout").boundingClientRect();
    const that = this
    wx.getSystemInfo({
      success: function (res) {
        query.exec(function (exceRes) {
          let scrollH2 = res.windowHeight - exceRes[0].height - exceRes[1].height - exceRes[2].height;
          that.setData({
            scrollHeight: (scrollH2 - 12) * App.globalData.pxToRpxRatio,
          });
        });
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  getWarehouseColumns(){
    // 选择仓库
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      let defaultWarehouse={}
      for (let r = 0; r < res.length; r++) {
        const warehouse ={
          text: res[r].name,
          id: res[r].id
        }
        warehouseColumns.push(warehouse)
        if(res[r].isDefault===2){
          defaultWarehouse = warehouse
        }
      }
      this.setData({
        warehouseColumns: warehouseColumns,
        curWarehouse: defaultWarehouse
      })
    })
  },
  // 获取仓库列表
  checkkWarehouse(e) {
    this.setData({
      showSelectWarhouse: true,
    })
  },
  // 确认选择仓库
  onConfirmWarehouse(event) {
    let detail = event.detail.value
    console.log(detail)
    this.setData({
      showSelectWarhouse: false,
      curWarehouse: detail
    })
  },
  // 关闭选择仓库
  onCancelWarehouse() {
    this.setData({
      showSelectWarhouse: false,
    })
  },
  // 切换全选
  onChangeAll(e) {
    const ids = this.data.dataList.map(item => item.itemId)
    this.setData({
      checkAll: e.detail,
      checkList: e.detail ? ids : []
    })
  },
  // 获取退货台列表
  getData() {
    const url = '/api/psi/invCheckstand/getRtBackList'
    const supInfo = wx.getStorageSync('supInfo')
    const curCust = wx.getStorageSync('curCust')
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: curCust.custId,
    }
    App.getHttp()._post(url, params).then(res => {
      if (res && res.length > 0) {
        this.setData({
          dataList: res
        })
      }
    })
  },
  // 修改数量
  onChangeApplyQty(e) {
    const index = e.currentTarget.dataset.index
    const qty = e.detail
    const key = `dataList[${index}].qty`
    this.setData({
      [key]: qty
    })
  },
  // 切换选择
  onChangeCheck(e) {
    const list = this.data.dataList.filter(item => e.detail.includes(item.itemId))
    if (list.length === 0) {
      this.setData({
        checkAll: false
      })
    } else {
      if (this.data.checkAll && list.length != this.data.dataList.length) {
        this.setData({
          checkAll: false
        })
      } else if (!this.data.checkAll && list.length === this.data.dataList.length) {
        this.setData({
          checkAll: true
        })
      }
    }
    this.setData({
      checkList: e.detail
    })
  },
  onClickEdit(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      curIdx: index,
      showDialog: true
    })
  },
  onConfirmChangePrice() {
    const key = `dataList[${this.data.curIdx}].price`
    this.setData({
      [key]: this.data.curPrice
    })
  },
  onChangePrice(e) {
    this.setData({
      curPrice: e.detail
    })
  },
  onCloseDialog() {
    this.setData({
      curPrice: '',
      curIdx: 0
    })
  },
  onClickConfirm() {
    if (!this.data.curWarehouse.id) {
      wx.showToast({
        icon: 'none',
        title: '请先选择仓库'
      })
      return false
    }
    // if (!this.data.telPhone) {
    //   wx.showToast({
    //     icon: 'none',
    //     title: '请先填写联系电话'
    //   })
    //   return false
    // }
    // if (!this.data.contactPerson) {
    //   wx.showToast({
    //     icon: 'none',
    //     title: '请先填写联系人'
    //   })
    //   return false
    // }
    // if (this.data.region.length === 0) {
    //   wx.showToast({
    //     icon: 'none',
    //     title: '请选择所在地区'
    //   })
    //   return false
    // }
    // if (!this.data.address) {
    //   wx.showToast({
    //     icon: 'none',
    //     title: '请填写详细地址'
    //   })
    //   return false
    // }
    if (this.data.checkList.length === 0) {
      wx.showToast({
        icon: 'none',
        title: '请选择要退货的商品'
      })
      return false
    }
    const url = '/api/psi/rtReturnOutBill/myx/submtByMyx'
    const list = this.data.dataList.filter(item => this.data.checkList.includes(item.itemId))
    if (list.find(item => !item.price)) {
      wx.showToast({
        icon: 'none',
        title: '请填写商品价格'
      })
      return false
    }
    const curCust = wx.getStorageSync('curCust')
    let exitDiff = false
    const params = {
      warehouseId: this.data.curWarehouse.id,
      custId: curCust.custId,
      custCode: curCust.custCode,
      shipMode: 5,
      remark: this.data.returnReason,
      telPhone: this.data.telPhone,
      contactPerson: this.data.contactPerson,
      contactAddr: this.data.regionText + this.data.address,
      provinceId: this.data.region.length>0?this.data.region[0].id:'',
      provinceName: this.data.region.length>0?this.data.region[0].name:'',
      cityId: this.data.region.length>1?this.data.region[1].id:'',
      cityName: this.data.region.length>1?this.data.region[1].name:'',
      districtId: this.data.region.length>2?this.data.region[2].id:'',
      districtName: this.data.region.length>2?this.data.region[2].name:'',
      townId: this.data.region.length>3?this.data.region[3].id:'',
      townName: this.data.region.length>3?this.data.region[3].name:'',
      addLineList: list.map(item => {
        if (!exitDiff&&item.barCodeFlowResponseDTOList) exitDiff = item.qty < item.barCodeFlowResponseDTOList.length
        return {
          itemId: item.itemId,
          itemCode: item.itemCode,
          standId: item.id,
          billQty: item.qty,
          pricecBillF: item.price,
          amountBillF: item.qty * item.price,
          warehouseId: this.data.curWarehouse.id
        }
      })
    }
    if(exitDiff){
      wx.showModal({
        title: '数量不合规',
        content: '退货商品数量不得小于条码数量!',
      })
      return 
    }
    App.getHttp()._post(url, params).then(res => {
      wx.redirectTo({
        url: '/pages/ccs/retail/returnPlatform/result/index?id=' + res
      })
    })
  },
  // 右滑删除商品
  onCloseCell(e) {
    const index = e.currentTarget.dataset.index
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: (res) => {
        const url = '/api/psi/invCheckstand/delete'
        const ids = [e.currentTarget.dataset.id]
        App.getHttp()
          ._post(url, { ids }, true)
          .then(res => {
            if (res.code === 200) {
              let checkIdx = this.data.checkList.findIndex(item => item === e.currentTarget.dataset.id)
              let checkedList = this.data.checkList
              checkedList.splice(checkIdx, 1)
              let dataList = this.data.dataList
              dataList.splice(index, 1)
              this.setData({
                dataList,
                checkList: checkedList
              })
              if (checkedList.length === 0) {
                this.setData({
                  checkAll: false
                })
              }
            }
          })
      }
    })
  },
  onChangeEdit() {
    const isEdit = !this.data.isEdit
    this.setData({
      isEdit
    })
  },
  // 删除多个商品
  onClickDelete() {
    if (this.data.checkList.length === 0) {
      wx.showToast({
        icon: 'none',
        title: '请选择要删除的商品'
      })
      return false
    }
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: (res) => {
        const url = '/api/psi/invCheckstand/delete'
        const list = this.data.dataList.filter(item => this.data.checkList.includes(item.itemId))
        const ids = list.map(item => (item.id))
        App.getHttp()
          ._post(url, { ids }, true)
          .then(res => {
            if (res.code === 200) {
              let dataList = this.data.dataList.filter(item => !ids.includes(item.id))
              this.setData({
                dataList,
                checkList: [],
                checkAll: false
              })
            }
          })
      }
    })
  },
  // 输入框内容修改
  onChangeField(e) {
    const field = e.currentTarget.dataset.field
    this.setData({
      [field]: e.detail
    })
  },
  // 地址选择 start
  openCitySelect: function () {
    // 弹出地址选择
    this.setData({
      showPicker: true
    })
  },
  onCloseCitySelect: function () {
    // 关闭地址选择框
    this.setData({
      showPicker: false
    })
  },
  citySelectChange(e) {
    let res = e.detail
    this.setData({
      showPicker: false
    })
    if (res) {
      this.setData({
        region: res,
        regionText: res.map((v) => {
          return v.name
        }).join(' ')
      })
    }
  },
  // 扫码扣减商品
  onClickReduceScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/scanPDA/retailReturnPlatform/index?mode=reduce',
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.getData();
            }
          },
        }
      })
    } else {
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const supInfo = wx.getStorageSync("supInfo");
            const curCust = wx.getStorageSync("curCust");
            const addParams = {
              invoiceSetsOfBooksId: supInfo.setsOfBooksId,
              invoiceCustId: curCust.custId,
              invoiceCustCode: curCust.custCode,
              standType:3,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/invCheckstand/decreaseInvCheckstand', addParams)
            this.getData();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail:(err)=>{
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 扫码增加商品
  onClickAddScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/scanPDA/retailReturnPlatform/index?mode=add',
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.getData();
            }
          },
        }
      })
    } else {
      wx.scanCode({
        success: async(res)=> {
          if (res.errMsg === 'scanCode:ok') {
            const supInfo = wx.getStorageSync("supInfo");
            const curCust = wx.getStorageSync("curCust");
            const addParams = {
              invoiceSetsOfBooksId: supInfo.setsOfBooksId,
              invoiceCustId: curCust.custId,
              invoiceCustCode: curCust.custCode,
              standType: 3,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/invCheckstand/addInvCheckstand', addParams)
            this.getData()
          } else {
            wx.showToast({
              title: res.errMsg,
            })
          }
        }
      })
    }
  },
  // 折叠监听
  onChangeCollape(event) {
    this.setData({
      collapseNames: event.detail,
    });
  },
})