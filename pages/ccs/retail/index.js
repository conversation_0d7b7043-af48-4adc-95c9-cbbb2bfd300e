// pages/ccs/retail/index.js
const App = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    custInfo: {},
    activityList: [
      {
        icon: "/asset/imgs/retail/retail-done.png",
        text: "已完成",
        to: "/pages/ccs/retail/order/index?stat=" + "0205",
      },
      {
        icon: "/asset/imgs/retail/retail-return.png",
        text: "零售退货",
        to: "/pages/ccs/retail/return/index",
      },
    ],
    classList: [],
    curClassId: "",
    pageIndex: 1,
    pageSize: 10,
    goodsData: [],
    listViewH: 270,
    addItemInfo: {},
    showAddCartsPop: false,
    addType: 1, // 1 加入结账台 2 立即开单
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) { },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this;
    const query = wx.createSelectorQuery();
    query.select("#root-layout").boundingClientRect();
    query.select("#scroll-layout").boundingClientRect();
    query.exec(function (exceRes) {
      that.setData({
        listViewH: exceRes[0].height - exceRes[1].top - App.globalData.deviceBottomOccPx,
      });
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    if (typeof this.getTabBar === "function" && this.getTabBar()) {
      // this.getTabBar().setData({
      //   selected: 2,
      // });
      this.getTabBar().initSelected(getApp().tabbarIndex);
    }
    // 初始化数据
    this.data.pageIndex = 1
    this.getCustInfo();
    this.getClassList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageIndex: 1,
    });
    this.getGoodsData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() { },
  onLoadMore: function () {
    this.setData({
      pageIndex: this.data.pageIndex + 1,
    });
    this.getGoodsData();
  },
  // 跳转结账台
  onClickToShop() { },
  async getCustInfo() {
    const custInfo = wx.getStorageSync("curCust");
    const url = "/api/mmd/common/termstor/getBasePage";
    const params = {
      pageIndex: 1,
      pageSize: 999,
      param: {
        isUsable: 2,
        state: 2,
      },
    };
    const res = await App.getHttp()._post(url, params);
    if (res && res.length > 0) {
      if (custInfo && res.find((item) => item.id === custInfo.custId)) {
        this.setData({
          custInfo: custInfo,
        });
      } else {
        this.setData({
          custInfo: { custId: res[0].id, custName: res[0].name },
        });
        wx.setStorageSync("curCust", {
          custId: res[0].id,
          custName: res[0].name,
          custCode: res[0].code,
        });
      }
    }
  },
  // 搜索跳转
  onClickSearchListener(e) {
    wx.navigateTo({
      url: "/pages/ccs/retail/searchHis/index",
    });
  },
  // 跳转订单列表
  onClickToOrder() {
    wx.navigateTo({
      url: "/pages/ccs/retail/order/index",
    });
  },
  // 获取商品分类
  async getClassList() {
    const res = await App.getHttp()._post("/api/mmd/itemClass/myx/getTree", { sortName: 'code' });//code排序
    if (res && res.length > 0) {
      this.setData({
        classList: res,
        curClassId: res[0].id,
      });
      this.getGoodsData()
    }
  },
  // 切换商品分类
  onChangeTab(e) {
    const bigClassId = e.detail.name;
    this.setData({
      curClassId: bigClassId,
      pageIndex: 1,
    });
    this.getGoodsData();
  },
  // 获取商品数据
  async getGoodsData() {
    const url = "/api/psi/rtOutBill/myx/selectRtItem";
    const curCust = wx.getStorageSync("curCust");
    if (!curCust || !curCust.custId) {
      wx.showToast({
        title: '头部,缺少门店信息!',
        icon: 'none'
      })
      return
    }
    const params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        custId: curCust.custId,
        bigClassId: this.data.curClassId,
        keyword: "",
      },
    };
    App.getHttp()._post(url, params).then(res => {
      let dataList = this.data.pageIndex === 1 ? [] : this.data.goodsData;
      this.setData({
        goodsData: dataList.concat(res && res.length > 0 ? res : []),
      });
    }).catch(err => {
      this.setData({
        goodsData: [],
      });
    });
  },
  // 跳转选择门店
  onClickToSelectCust() {
    wx.navigateTo({
      url: "/pages/ccs/retail/selectCust/index",
    });
  },
  // 关闭加入结账台
  onCloseAddCarts(e) {
    const item = e.detail;
    // 清空缓存中的立即开单信息
    wx.removeStorageSync("createOrderInfo");
    if (item) {
      if (this.data.addType === 2) {
        const curCust = wx.getStorageSync("curCust");
        if (!curCust || !curCust.custId) {
          wx.showToast({
            title: '头部,缺少门店信息!',
            icon: 'none'
          })
          return
        }
        wx.setStorageSync("createOrderInfo", [item]);
        wx.navigateTo({
          url: "/pages/ccs/retail/createOrder/index",
        });
      } else {
        this.addShops(item);
      }
    }
    this.setData({
      showAddCartsPop: false,
    });
    if (this.getTabBar()) {
      this.getTabBar().setData({
        isShow: true,
      });
    }
  },
  // 立即开单/加入结账台
  onClickShowAddCart(e) {
    const item = e.currentTarget.dataset.item;
    const type = e.currentTarget.dataset.type;
    this.setData({
      showAddCartsPop: true,
      addType: type,
      addItemInfo: item,
    });
    if (this.getTabBar()) {
      this.getTabBar().setData({
        isShow: false,
      });
    }
  },
  addShops(item, jumpToPlatform = false, isIgnoreCustError = 1) {
    const url = "/api/psi/invCheckstand/addInvCheckstand";
    const supInfo = wx.getStorageSync("supInfo");
    const curCust = wx.getStorageSync("curCust");
    if (!curCust || !curCust.custId) {
      wx.showToast({
        title: '头部,缺少门店信息!',
        icon: 'none'
      })
      return
    }
    const params = {
      invoiceSetsOfBooksId: supInfo.setsOfBooksId,
      invoiceCustId: curCust.custId,
      invoiceCustCode: curCust.custCode,
      itemId: item.itemId,
      itemCode: item.itemCode,
      qty: item.qty,
      standType: 1,
      barCode: item.barCode,
      isIgnoreCustError: isIgnoreCustError
    };
    App.getHttp()
      ._post(url, params, true)
      .then((res) => {
        if (res.code === 62262) {
          wx.showModal({
            title: '异常提示',
            content: res.chnDesc,
            showCancel: true,
            success: (res) => {
              if (res.confirm) {
                this.addShops(item, jumpToPlatform, 2)
              }
            }
          })
        } else {
          if (jumpToPlatform) {
            setTimeout(() => {
              wx.navigateTo({
                url: "/pages/ccs/retail/checkoutPlatform/index",
              });
            }, 600);
          }
          wx.showToast({
            icon: "success",
            title: "添加成功",
          });
          this.selectComponent("#retail-head").getCheckoutPlatformCount();
        }
      });
  },
  // 跳转商品详情
  onClickToDetail(e) {
    const itemId = e.currentTarget.dataset.itemId;
    wx.navigateTo({
      url: "/pages/ccs/retail/skuDetail/index?itemId=" + itemId,
    });
  },
  // 扫码
  onClickScan() {
    // 允许从相机和相册扫码
    wx.scanCode({
      success: async (res) => {
        console.log('==scanCode=', res);
        if (res.errMsg === 'scanCode:ok') {
          this.addShops({ barCode: res.result }, true);
        } else {
          wx.showModal({
            title: '扫码失败',
            content: res.errMsg,
            showCancel: false
          })
        }
      },
      fail: (err) => {
        wx.showModal({
          title: '扫码失败',
          content: err.errMsg,
          showCancel: false
        })
      },
    })
  },
  // 扫码开单
  onClickOrderScan() {
    const curCust = wx.getStorageSync("curCust");
    if (!curCust || !curCust.custId) {
      wx.showToast({
        title: '头部,缺少门店信息!',
        icon: 'none'
      })
      return
    }
    // 清空缓存中的立即开单信息
    wx.removeStorageSync("createOrderInfo");
    wx.scanCode({
      success: async (res) => {
        if (res.errMsg === 'scanCode:ok') {
          this.getScanGoods(res.result)
        } else {
          wx.showModal({
            title: '扫码失败',
            content: res.errMsg,
            showCancel: false
          })
        }
      },
      fail: (err) => {
        wx.showModal({
          title: '扫码失败',
          content: err.errMsg,
          showCancel: false
        })
      },
    })
  },
  //获取商品内容
  async getScanGoods(barCode, isIgnoreCustError = 1) {
    const result = await App.getHttp()._post('/api/psi/barCodeFlow/scanShimgeGroupBarCode', { barCode, isIgnoreCustError }, true)
    if (result.code === 62263) {
      wx.showModal({
        title: '异常提示',
        content: result.chnDesc,
        showCancel: true,
        success: (res) => {
          if (res.confirm) {
            this.getScanGoods(barCode, 2)
          }
        }
      })
    } else if (result.content && result.content.length > 0) {
      wx.setStorageSync("createOrderInfo", result.content);
      wx.navigateTo({
        url: "/pages/ccs/retail/createOrder/index",
      });
    } else {
      wx.showModal({
        title: '无效条码',
        content: '条码不存在,请检查!',
        showCancel: false
      })
    }
  }
});
