<!-- pages/ccs/retail/index.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<view class="root-layout bg-04" id="root-layout">
  <!-- 导航头部 -->
  <retailHead showReturn="{{false}}" customTitle="零售" id="retail-head" />
  <!-- 门店区域 -->
  <view class="cust-layout" id="cust-layout" bindtap="onClickToSelectCust">
    <image class="cust-icon" src="/asset/imgs/purchase/cust-avatar.png" />
    <view class="custName">{{custInfo.custName}}</view>
    <van-icon size="16" color="#fff" name="arrow" />
  </view>
  <!-- 零售订单 -->
  <view class="middle-layout" id="middle-layout">
    <view class="middle-head">
      <view class="middle-title">零售订单</view>
      <!-- <view class="middle-to-all" bindtap="onClickToOrder">全部</view> -->
    </view>
    <gridLayout wx:key="index" columnNum="3" iconSize="48px" layoutList="{{activityList}}" />
  </view>
  <!-- 搜索区域 -->
  <view class="filter-layout" id="filter-layout-search">
    <view class="filter-layout-search  align-items-center">
      <image class="scan" src="/asset/imgs/scan.png"  catchtap="onClickScan"/>
      <van-icon name="/asset/imgs/purchase/search.png" size="16" />
      <view class="search-text flexbox" catchtap="onClickSearchListener">搜索商品名称</view>
      <van-button custom-class="right-order"  color="linear-gradient(to right, #4bb0ff,#0077ff)" block catchtap="onClickOrderScan">扫码立即开单</van-button>
    </view>
  </view>
  <!-- tab标签 -->
  <view class="goods-layout" id="goods-layout">
    <van-tabs active="{{ curClassId }}" title-active-color="#00b9c3" border="{{false}}" ellipsis="{{false}}" tab-class="tabStyle" bind:change="onChangeTab">
      <van-tab title="{{item.name}}" name="{{item.id}}" wx:for="{{classList}}" wx:key="id"></van-tab>
    </van-tabs>
  </view>
  <listView class="scroll-layout" id="scroll-layout" viewHeightPx="{{listViewH}}" bind:loadmore="onLoadMore">
    <view class="list-layout" wx:if="{{goodsData.length > 0}}">
      <view class="listItemLayout" wx:for="{{goodsData}}" data-item-id="{{item.itemId}}" wx:key="itemId" >
        <view class="good-info">
          <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
          <view class="good-name-box">
            <view class="name">{{item.itemName}}</view>
            <view class="specs">{{item.specs}}</view>

          </view>
          <view class="good-content-right">
            <view class="good-price">
              <text>{{item.price ? '¥' + wxsUtil.moneyFormatInt(item.price,'int') : '暂无报价'}}</text>
              <text class="rem">{{wxsUtil.moneyFormatInt(item.price)}}</text>
            </view>
            <view class="good-onHand">库存{{item.totalQty}}</view>
          </view>
        </view>
        <view class="good-action">
          <view class="add-to-checkoutPlatform" data-item="{{item}}" data-type="{{1}}" catchtap="onClickShowAddCart">
            加入结账台
          </view>
          <view class="create-order" data-item="{{item}}" data-type="{{2}}" catchtap="onClickShowAddCart">
            立即开单
          </view>
        </view>
      </view>
    </view>
    <view class="m-t-64" wx:else>
      <noneView noneTxt="暂无商品"></noneView>
    </view>
  </listView>
  <view class="float-btn-layout">
    
  </view>
  <retailAddCarts type="{{addType}}" bind:onClose="onCloseAddCarts" show="{{showAddCartsPop}}" itemInfo="{{addItemInfo}}" />
</view>