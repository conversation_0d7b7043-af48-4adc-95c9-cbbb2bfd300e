// pages/ccs/retail/selectCust/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    curCustId: '',
    pageIndex: 1,
    pageSize: 999, // 一次性加载完
    custList: [],
    listViewH: 550
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const custInfo = wx.getStorageSync('curCust')
    if(custInfo) {
      this.setData({
        curCustId: custInfo.custId
      })
    }
    this.getCustList()
    const that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#footer-layout').boundingClientRect()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].height
          console.log(res.windowHeight , exceRes[0].height, scrollH2)
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取门店列表
  getCustList() {
    const url = '/api/mmd/common/termstor/getBasePage'
    const params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        isUsable: 2,
        state: 2,
      },
    };
    App.getHttp()._post(url, params).then(res => {
      if(res && res.length > 0) {
        this.setData({
          custList: res
        })
      }
    })
  },
  // 切换选择
  onChangeCheck(e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      curCustId: id
    })
  },
  // 确定选择门店 
  onClickConfirm() {
    const curCust = this.data.custList.find(item => item.id === this.data.curCustId)
    wx.setStorageSync('curCust', { custId: curCust.id, custCode: curCust.code, custName: curCust.name })
    wx.navigateBack({
      delta: 1
    })
  }
})