<!-- pages/ccs/retail/selectCust/index.wxml -->
<view class="root-layout bg-04">
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}">
        <van-radio-group value="{{ curCustId }}" wx:if="{{custList.length > 0}}">
        <view class="cust-info" data-id="{{item.id}}" bindtap="onChangeCheck" wx:for="{{custList}}" wx:key="id">
            <view class="cust-name">{{item.name}}</view>
            <van-radio name="{{item.id}}"></van-radio>
        </view>
        </van-radio-group>
        <view class="m-t-25p" wx:else>
          <noneView ></noneView>
        </view>
    </listView>
    <view class="footer" id="footer-layout">
        <view class="confirm-btn" bindtap="onClickConfirm">确定</view>
    </view>
</view>