<!--pages/ccs/message/typeList/typeList.wxml-->
<view class="page root-layout" id="page-layout">
    <listView  class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
    <!-- <scroll-view
        scroll-y 
        style="width: 100%; height: {{istViewH}}px;"
        refresher-enabled="{{true}}"
        refresher-threshold="{{100}}"
        refresher-default-style="white"
        refresher-background="#00b9c3"
        refresher-triggered="{{triggered}}"
        bindrefresherrefresh="onRefresh"
        bindscrolltolower="loadMore"
    > -->
        <view class="item-layout" wx:if="{{list.length>0}}">
            <view class="itemWrap" wx:for="{{list}}" wx:key="indexx" bindtap="selectMsgListener" data-item="{{item}}">
                <view class="top-date">
                    {{item.pushTime || item.creationDate}}
                </view>
                <view wx:if="{{item.noticeFace}}" class="face-item-content">
                    <image class="img" src="{{item.noticeFace}}" mode="scaleToFill"></image>
                    <view class="txt title flex align-items-center">{{item.noticeName}}</view>
                    <view wx:if="{{item.readStatus==1}}" class="dot"></view>
                </view>
                <view wx:else class="item-content">
                    <view class="title">{{item.noticeName}}</view>
                    <view class="item-noticeName" wx:if="{{item.content}}">{{item.content}}</view>
                    <view wx:if="{{item.readStatus==1}}" class="dot"></view>
                </view>
            </view>
        </view>
        <view class="m-t-25p" wx:else>
            <no-product noneTxt="暂无数据" />
        </view>
    </listView>
</view>
