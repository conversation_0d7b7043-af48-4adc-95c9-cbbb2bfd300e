/* pages/ccs/message/typeList/typeList.wxss */
  .root-layout {
    background-color: rgba(0,0,0,0.04);
  }
  .item-layout{
    padding: 0 24rpx;
  }
  .itemWrap {
      margin-top: 48rpx;
  }
  .itemWrap .top-date {
    height: 40rpx;
    font-family: SanFranciscoText-Regular;
    font-size: 24rpx;
    color: rgba(0,0,0,0.45);
    text-align: center;
    line-height: 40rpx;
    font-weight: 400;
  }
  .item-content {
    height: 204rpx;
    background-color: #FFFFFF;
    border-radius: 12rpx;
    padding: 32rpx 24rpx;
    margin-top: 16rpx;
    box-sizing: border-box;
    position: relative;
  }
  .title {
    height: 44rpx;
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: rgba(0,0,0,0.85);
    line-height: 44rpx;
    font-weight: 500;
  }
  .item-noticeName {
    margin-top: 16rpx;
    height: 40rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: rgba(0,0,0,0.75);
    line-height: 40rpx;
    font-weight: 400;
  }

  .face-item-content{
    height: 440rpx;
    background-color: #FFFFFF;
    border-radius: 12rpx;
    margin-top: 16rpx;
    box-sizing: border-box;
    position: relative;
  }
  .face-item-content .img{
    width: 100%;
    height: 340rpx;
    border-top-right-radius: 12rpx;
    border-top-left-radius: 12rpx;
  }
  .face-item-content .txt{
    height: 100rpx;
    padding: 0 24rpx;
  }
  .dot{
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #00b9c3;
    position: absolute;
    left: 0;
    top: 0;
  }
