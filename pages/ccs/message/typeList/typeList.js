// pages/ccs/message/typeList/typeList.js
const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        listViewH:300,
        noticeType: '',
        pageIndex: 1,
        pageSize: 20,
        list: []
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        const type = options.type
        this.setData({
            noticeType: type
        })
        switch(type) {
            case '1': 
                wx.setNavigationBarTitle({
                  title: '通知公告',})
            break;
            case '2': 
                wx.setNavigationBarTitle({
                  title: '政策发布'})
            break;
            case '3': 
                wx.setNavigationBarTitle({
                  title: '系统通知'})
            break;
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
        const query = wx.createSelectorQuery()
        query.select('#page-layout').boundingClientRect()
        query.exec((res)=>{
          this.setData({
            listViewH:res[0].height
          })
        })
       
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        this.data.list = []
        this.getData()
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    // ---------------------methods---------------------
    getData() {
        let param = {
            "pageIndex": this.data.pageIndex,
            "pageSize": this.data.pageSize,
            "param": {
                "noticeType": this.data.noticeType,
                "custCode":wx.getStorageSync('mastCode')
            }
        }
        App.getHttp()._post('myx/ccs-mobile-web/msg/notice/message/page', param).then(res => {
            if (res && res.length > 0) {
              this.setData({
                list:this.data.list.concat(res)
              })
            } else {
                wx.showToast({
                    title: '没有更多数据了...',
                    duration: 3000,
                    icon: 'none'
                })
            }
        })
    },
    loadMore() {
        let tempPageIndex = this.data.pageIndex * 1 + 1
        this.setData({
            pageIndex: tempPageIndex
        })
        this.getData()
    },
    onRefresh() {
      let tempPageIndex = 1
        this.setData({
            list:[],
            pageIndex: tempPageIndex
        })
      this.getData() 
    },
    selectMsgListener(e) {
     const item =  e.currentTarget.dataset.item
       wx.navigateTo({
        url: `/pages/ccs/message/msgDetail/msgDetail?orderFlag=${item.orderFlag}&id=${item.noticeScopeUserId}`,
       })
    }
})