const App = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        noticeMsg: {}
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        let param = {
            "pageIndex": 1,
            "pageSize": 1,
            "param": {
                id: options.id,
            }
        }
        console.log(param)
        App.getHttp()._post('/api/message/notifyMessage/getUserPage', param, true).then(res => {
            console.log('itemList', res)
            this.setData({
                noticeMsg: res.recordList[0]
            })
        })
    },

})