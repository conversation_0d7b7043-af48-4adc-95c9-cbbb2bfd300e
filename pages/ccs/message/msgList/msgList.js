const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH: 500,
    active: 0,
    tabList: [],
    showList: [],
    searchValue: '',
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    warehouseInfo: {}, // 当前tab信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let tabList = wx.getStorageSync('dictMap').NoticeMessageType.map(item => {
      item.type = item.value
      return item;
    })
    console.log('tabList', this.data.tabList)
    this.setData({ tabList })
    this.getUnreadCount()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.onRefresh()
    this.getUnreadCount()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onSearchCancel() {
    this.setData({ searchValue: '' })
  },
  onSearchClick(e) {
    console.log(11)
    this.setData({
      searchValue: e.detail,
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.loadMore()
  },
  onSearchChange(e) {
    this.setData({ searchValue: e.detail })
    console.log('searchValue', this.data.searchValue)
  },
  chooseTab(e) {
    console.log('warehouseInfo', e.detail)
    let active = e.detail.index
    this.setData({
      active,
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.loadMore()
  },
  onRefresh() {
    this.setData({
      showList: [],
      pageIndex: 1,
      pages: 0,
    })
    this.loadMore()
  },
  loadMore() {
    if (this.data.pages && this.data.pages < this.data.pageIndex + 1) {
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    console.log(this.data.tabList, this.data.active)
    const userInfo = wx.getStorageSync('userInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param = {
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        // warehouseId: this.data.warehouseInfo.id,
        type: this.data.tabList[this.data.active].type,
        title: this.data.searchValue,
        // receiveUserId: userInfo.userId,
        // receiveUserSetsId: supInfo.setsOfBooksId,
      }
    }
    console.log(param)
    App.getHttp()._post('/api/message/notifyMessage/getUserPage', param, true).then(res => {
      console.log('itemList', res)
      this.setData({
        showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages,
        tabList: this.data.tabList
      })
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  onClickArrow() {
    console.log('jll')
    wx.navigateBack({
      delta: 1,
    })
  },
  onClearClick() {
    let param={

    }
    App.getHttp()._post('/api/message/notifyMessage/markReadByUser', param, true).then(res => {
      this.getUnreadCount();
    })
  },
  getUnreadCount(){
    let tabList = this.data.tabList
    let proArr  = []
    const userInfo = wx.getStorageSync('userInfo')
    const supInfo = wx.getStorageSync('supInfo')
    tabList.forEach((item,index)=>{
      let param ={
          isRead: 1,
          type: item.type,
          receiveUserId: userInfo.userId,
          receiveUserSetsId: supInfo.setsOfBooksId,
      }
      let promiseItem = App.getHttp()._post('/api/message/notifyMessage/gerCurrentUserUnreadCount', param, true)
      proArr.push(promiseItem)
    })
    Promise.all(proArr).then(resArr=>{
      resArr.forEach((item,index)=>{
        tabList[index].info = item.content?(item.content*1||'') : ''
      })
      console.log('restabList', tabList)
      this.setData({tabList})
    })
  },
  onClicItem(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    App.getHttp()._post(`/api/message/notifyMessage/markRead/${this.data.showList[index].id}`, {}, true).then(res => {
      if(this.data.showList[index].type == 200){
        wx.navigateTo({
          url: `/pages/ccs/feedback/detail/index?id=${this.data.showList[index].bizId}`,
        })
      }else{
        wx.navigateTo({
          url: `/pages/ccs/message/msgDetail/msgDetail?id=${this.data.showList[index].id}`,
        })
      }
    })
  }
})