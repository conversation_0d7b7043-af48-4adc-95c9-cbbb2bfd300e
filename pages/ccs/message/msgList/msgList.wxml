<!--pages/ccs/message/msgList/msgList.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="page-layout root-layout" id="root-layout">
    <van-sticky offset-top="0" class="top-layout" id="top-layout">
        <nav-bar title="消息中心" imgUrl="{{'/asset/imgs/allRead.png'}}" bind:navImgClick="onClearClick"/>
        <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{false}}" bind:onSearchTxtClick="onSearchClick" bind:onConfirm="onSearchClick" />
        <van-tabs active="{{ active }}" bind:change="chooseTab" swipe-threshold="3">
            <van-tab wx:for="{{tabList}}" wx:key="index" title="{{item.name}}" name="{{index}}" info="{{item.info}}">
            </van-tab>
        </van-tabs>
        <!-- <warehouse-tab bind:chooseTab="chooseTab" /> -->
    </van-sticky>
    <view class="make-height"></view>
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
        <view class="item-layout" wx:if="{{showList.length>0}}">
            <veiw wx:for="{{showList}}" wx:key="key" wx:for-item="item" wx:for-key="key"  catchtap="onClicItem" data-value="{{index}}">
                <view class="list-item-order" wx:if="{{active === 0}}" >
                    <view class="item-title single-line-ellipsis">
                        {{item.title}}
                    </view>
                    <view class="item-detail two-line-ellipsis">
                        {{item.content}}
                    </view>
                    <view class="item-date">
                        {{item.sendTime}}
                    </view>
                </view>
                <view wx:else class="list-item-feedback">
                    <view class="item-title two-line-ellipsis">
                        <view class="title-txt">{{wxsUtil.subStringLen(item.title,40)}}</view>
                        <view class="title-icon type" wx:if="{{item.subTypeName}}">{{item.subTypeName}}</view>
                    </view>
                    <view class="item-detail two-line-ellipsis">
                        {{item.content}}
                    </view>
                    <view class="item-date">
                        {{item.sendTime}}
                    </view>
                </view>
            </veiw>
        </view>
        <view class="m-t-25p" wx:else>
            <no-product noneTxt="暂无数据" />
        </view>
    </listView>
</view>