// pages/ccs/sets/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    acc:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      acc:wx.getStorageSync('acc')
    })
  },

  onClickChangePsw(){
    wx.navigateTo({
      url: '/pages/ccs/sets/changePsw/index',
    })
    // wx.showToast({
    //   title: '完善中,敬请期待!',
    //   icon:'none'
    // })
  },
  onClickAboutUs(){
    wx.navigateTo({
      url: '/pages/ccs/sets/aboutUs/index',
    })
  },
  onClickContactUs(){
    wx.navigateTo({
      url: '/pages/ccs/sets/contactUs/index',
    })
  },
  onClickExit(){
   const acc =  wx.getStorageSync('acc')
   const psw =  wx.getStorageSync('psw')
   const protocolChecked =  wx.getStorageSync('protocolChecked')
    wx.clearStorage()
    wx.setStorageSync('acc', acc)
    wx.setStorageSync('psw', psw)
    wx.setStorageSync('protocolChecked', protocolChecked)
    wx.reLaunch({
      url: '/pages/login/index',
    })
  }
})