// pages/ccs/sets/changePsw/pswRules/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    password:{
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isFirstChecked: false, // 数字
    isSecondChecked: false, // 小写
    isThirdChecked: false, // 大写
    isFourthChecked: false, // 特殊
    isFifthChecked: false, // 长度
  },
  observers: {
    'password': function (value) {
      this.handlePswValue(value)
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handlePswValue(password) {
      let numReg = /\d/
      let capitalReg = /[.*A-Z]/
      let lowercaseReg = /[.*a-z]/
      let specialCharactersReg = new RegExp('[`~!@#$^&*()=|{}\':;\',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“\'。，、？ ]')
      let isFirstChecked = true // 数字
      let isSecondChecked = true // 小写
      let isThirdChecked = true // 大写
      let isFourthChecked = true // 特殊
      let isFifthChecked = true // 长度
      // 判断是否包含至少一个数字
      if (!numReg.test(password)) {
        isFirstChecked = false
      }
      // 判断是否包含至少一个小写字母
      if (!lowercaseReg.test(password)) {
        isSecondChecked = false
      }
      // 判断是否包含至少一个大写字母
      if (!capitalReg.test(password)) {
        isThirdChecked = false
      }
      // 判断是否包含至少一个特殊字符
      if (!specialCharactersReg.test(password)) {
        isFourthChecked = false
      }
      // 判断长度是否8-16位
      if (password.length > 16 || password.length < 8) {
        isFifthChecked = false
      }
      this.setData({
        isFirstChecked,
        isSecondChecked,
        isThirdChecked,
        isFourthChecked,
        isFifthChecked,
      })
    }
  }
})
