// pages/ccs/sets/changePsw/index.js
const app = getApp()
const util = require('../../../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    psw: '',
    newPsw: '',
    telFocus: false,
    pswFocus: false,
    originpswpswFocus: false,
    originpswErrorMsg: '',
    originpsw: '',
    showPsw3: false,
    showPsw2: false,
    showPsw1: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  onChangeTel(event) {
    this.setData({
      psw: event.detail,
      pswErrorMsg: ''
    })
  },
  onChangePsw(event) {
    this.setData({
      newPsw: event.detail,
      newPswErrorMsg: ''
    })
  },
  onChangeOrigin(event) {
    this.setData({
      originpsw: event.detail,
      originpswErrorMsg: ''
    })
  },
  resetPassword() {
    // wx.showToast({
    //   title: '二期任务',
    //   duration: 3000,
    //   icon: 'none'
    // })
    // return
    if (!(this.data.originpsw)) {
      this.setData({
        originpswErrorMsg: '原密码不能为空',
        originpswpswFocus: true
      })
      return false
    }
    if (!(this.data.psw)) {
      this.setData({
        pswErrorMsg: '密码不能为空',
        pswFocus: true
      })
      return false
    }
    if (!(this.data.newPsw)) {
      this.setData({
        newPswErrorMsg: '密码不能为空',
        newPswFocus: true
      })
      return false
    }
    if (this.data.psw !== this.data.newPsw) {
      wx.showToast({
        title: '两次输入密码必须包持一致',
        duration: 3000,
        icon: 'none'
      })
      return false
    }
    let params = {
      password: util.encodePassword(this.data.newPsw),
      originpsw: util.encodePassword(this.data.originpsw),
    }
    app.getHttp()._post(`/api/sys/changeUserPassword`, params).then((res) => {
      wx.showToast({
        title: '密码修改成功，请重新登陆',
        duration: 3000,
        icon: 'none'
      })
      this.logout()
    })

  },
  //返回登录页
  logout() {
    const loginId = wx.getStorageSync('loginId')
    const psw = wx.getStorageSync('psw')
    wx.clearStorage()
    wx.setStorageSync('loginId', loginId)
    //  wx.setStorageSync('psw', psw)
    wx.redirectTo({
      url: '/pages/login/index',
    })
  },
  clickSee(e){
    let type = e.currentTarget.dataset.value // 获取传入的参数
    let obj = {}
    if(type == 1){
      obj.showPsw1 = !this.data.showPsw1
    }else if (type == 2){
      obj.showPsw2 = !this.data.showPsw2
    }else if (type == 3){
      obj.showPsw3 = !this.data.showPsw3
    }
    this.setData(obj)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})