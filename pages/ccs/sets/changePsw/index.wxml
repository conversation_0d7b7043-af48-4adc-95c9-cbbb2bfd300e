<!--pages/ccs/sets/changePsw/index.wxml-->
<view class="page p-base">
  <view class="page-form">
    <van-field value="{{originpsw}}" name="psw" clearable custom-class="cell-custom-class"  placeholder="请输入您的原密码" error-message="{{originpswErrorMsg}}" focus="{{originpswpswFocus}}" bind:change="onChangeOrigin" password="{{!showPsw1}}" label="原密码">
      <van-icon class="right-icon" slot="right-icon" bindtap="clickSee" size="24" name="{{!showPsw1 ?'/asset/svgs/eye-see.svg':'/asset/svgs/eye.svg'}}" data-value="1"/>
    </van-field>
    <van-field value="{{psw}}" name="psw" clearable custom-class="cell-custom-class" placeholder="请输入8-16位新密码" focus="{{pswFocus}}" error-message="{{pswErrorMsg}}" bind:change="onChangeTel"  password="{{!showPsw2}}" label="新密码">
      <van-icon class="right-icon" slot="right-icon" bindtap="clickSee" size="24" name="{{!showPsw2?'/asset/svgs/eye-see.svg':'/asset/svgs/eye.svg'}}"  data-value="2"/>
    </van-field>
    <psw-rules password="{{psw}}" wx:if="{{psw.length}}"/>
    <van-field value="{{newPsw}}" name="newPsw" focus="{{newPswFocus}}" clearable custom-class="cell-custom-class" placeholder="请再次输入8-16位新密码" error-message="{{newPswErrorMsg}}" bind:change="onChangePsw"  password="{{!showPsw3}}" label="确认新密码">
      <van-icon class="right-icon" slot="right-icon" bindtap="clickSee" size="24" name="{{!showPsw3?'/asset/svgs/eye-see.svg':'/asset/svgs/eye.svg'}}" data-value="3"/>
    </van-field>
    <!-- <view class="mt5">密码规则：</view>
    <view class="mt5">1.包含数字</view>
    <view class="mt5">2.包含小写字母</view>
    <view class="mt5">3.包含大写字母</view>
    <view class="mt5">4.包含特殊字母</view>
    <view class="mt5">5.密码长度为8-16位</view> -->
    <button class="submit-box" style='opacity:{{psw.length>0&&newPsw.length>0?1:0.5}}' bindtap="resetPassword">确认重置</button>
  </view>

</view>