// pages/ccs/purchase/index.js
Page({

  /**
   * 页面的初始数据
   */
  /**
   * 客户类型 
   * 5 经销商 -- 向上 
   * 6 分销商 7 门店 8 网点 -- 向下
   */
  data: {
    purchaseType: '',
    activityList: [
      {
        icon: "/asset/imgs/purchase/activity-tejia.png",
        text: "一口价",
        to: "/pages_sub/pages/ccs/down/policy/buyoutPrice/index",
      },
      {
        icon: "/asset/imgs/purchase/activity-manjian.png",
        text: "满减",
        to: "/pages_sub/pages/ccs/down/policy/reductionDiscount/index?promotionType=1",
      },
      {
        icon: "/asset/imgs/purchase/activity-manzhe.png",
        text: "满折",
        to: "/pages_sub/pages/ccs/down/policy/reductionDiscount/index?promotionType=2",
      },
      {
        icon: "/asset/imgs/purchase/activity-manzeng.png",
        text: "满赠",
        to: "/pages_sub/pages/ccs/down/policy/salesGift/index",
      },
      {
        icon: "/asset/imgs/purchase/activity-taocan.png",
        text: "众筹",
        to: "/pages_sub/pages/ccs/down/policy/crowdfunding/index",
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 全局自定义custom-tab-bar需要
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  saleOrgChange(event) {
    this.selectComponent("#purchaseIndex").init(event.detail);
  },
  getCartsCount() {
    this.selectComponent("#listHead").getCartsCount();
  }
})