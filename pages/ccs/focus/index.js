// pages/ccs/focus/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageIndex: 1,
    pageSize: 10,
    focusList: [],
    showAddCartsPop: false,
    showFilterAction:false,
    isUsable:'',
    showTypeAction:false,
    custType: '',
    isSupMode: true,
    calculateViewHeightPx: '300',
    saleOrgOptions: [], // 供应商数据源 
    showPop: false,
    supplier: {
      vendorCode: '',
      vendorId: '',
      vendorName: '',
      vendorSetsOfBooksId: '',
      invoiceCustCode: '',
      invoiceCustId: '',
      invoiceCustName: '',
      invoiceSetsOfBooksId: '',
      channelCode: '',
      channelId: '',
      channelName: '',
      saleOrgCode: '',
      saleOrgName: '',
      saleOrgCodeName: '',
      custGroupCode: '',
      companyCode: '',
      saleRegionCode: '',
      creditGrade: '',
      supId: '',
    }, // 当前供应商
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const custInfo =  wx.getStorageSync('custInfo')
    if (options.saleOrgCode) {
      this.data.supplier.saleOrgCode = options.saleOrgCode
      this.data.supplier.saleOrgCodeName = options.saleOrgName
      this.data.supplier.saleOrgName = options.saleOrgName
    }
    this.setData({
      custType: custInfo.custType,
      isSupMode:custInfo.custType==5,//经销商优先是向上模块
      invoiceCustId: options.invoiceCustId,
      supplier: this.data.supplier
    }, () => {
      if (this.data.supplier.saleOrgCode) {
        this.getCustData({
          saleOrgCode:  this.data.supplier.saleOrgCode,
          saleOrgCodeName: this.data.supplier.saleOrgCodeName
        })
        this.getsupplierList('notOrg')
      } else {
        this.getsupplierList()
      }
    })
  },
  

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.data.pageIndex=1
    this.data.focusList=[]
    this.getCartsCount()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    this.setData({
      pageIndex: this.data.pageIndex + 1
    }, () => {
      this.getNoticeGoods()
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout-org').boundingClientRect()
    query.exec((res) => {
      this.setData({
        calculateViewHeightPx: res[0].height - res[1].height - 20,
      })
    })
  },
  // 调出选择框
  onClickSelectSup() {
    this.setData({
      showPop: true
    })
  },
  // 获取销售组织
  async getsupplierList(type) {
    const res = await App.getHttp()._post(
      "/api/mmd/common/bsSaleOrgCode",
      {
        pageIndex: 1,
        pageSize: 5,
        param: {},
      }
    );
    if (!(type === 'notOrg')) {
      this.getCustData(res[0])
    }
    this.setData({
      saleOrgOptions: res.map(item => {
        return {
          ...item,
          text: item.saleOrgCodeName
        }
      })
    })
  },

  questTypeCancel(){
    this.setData({
      showPop: false
    })
  },
  questTypeConfirm(e){
    let item = e.detail.value;
    this.getCustData(item)
    this.setData({
      showPop: false
    })
  },


  // 获取主客户
  async getCustData(salOrg) {
    const recordList = await App.getHttp()._post(
      "/api/mmd/common/bsSup/page",
      {
        pageIndex: 1,
        pageSize: 5,
        param: {saleOrgCode: salOrg.saleOrgCode,},
      }
    );
    let supplier = this.data.supplier
    if (recordList && recordList.length > 0) {
      // 默认当前客户, 优先取默认设置,没有再拿第一条
      const findDefaultIndex = recordList.findIndex((findSup) => !findSup.mainAccountId);
      const val = recordList[findDefaultIndex > -1 ? findDefaultIndex : 0];
      Object.keys(supplier).forEach((key) => {
        if (key === 'invoiceCustId') {
          this.data.supplier[key] = val.scustId;
        } else if (key === 'invoiceCustCode') {
          this.data.supplier[key] = val.scustCode;
        } else if (key === 'invoiceCustName') {
          this.data.supplier[key] = val.scustName;
        } else if (key === 'invoiceSetsOfBooksId') {
          this.data.supplier[key] = val.bsetsOfBooksId;
        } else if (key === 'vendorCode') {
          this.data.supplier[key] = val.bvendorCode;
        } else if (key === 'vendorId') {
          this.data.supplier[key] = val.bvendorId;
        } else if (key === 'vendorName') {
          this.data.supplier[key] = val.bvendorName;
        } else if (key === 'vendorSetsOfBooksId') {
          this.data.supplier[key] = val.ssetsOfBooksId;
        } else if (key === 'supId') {
          this.data.supplier[key] = val.id;
        } else if (key === 'channelId') {
          const channelIdArr = val.channelIds.split(',');
          this.data.supplier[key] = channelIdArr.length > 0 ? channelIdArr[channelIdArr.length - 1] : '';
        } else if (key === 'saleRegionCode') {
          this.data.supplier[key] = val.regionCode;
        } else {
          this.data.supplier[key] = val[key];
        }
      });
      // this.refreshQuery();
    }
    supplier.saleOrgCode = salOrg.saleOrgCode
    supplier.saleOrgCodeName = salOrg.saleOrgCodeName
    this.setData({
      pageIndex: 1,
      focusList: [],
      supplier
    }, () => {
      this.getNoticeGoods()
    })
  },

  //获取关注商品列表
  getNoticeGoods() {
    let params = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        // itemSeries: list && list.length > 0 ? list[0].value : undefined, // 产品细列条件入参
        saleOrgCode: this.data.supplier.saleOrgCode,
        channelCode: this.data.supplier.channelCode,
        channelId: this.data.supplier.channelId,
        invoiceCustCode: this.data.supplier.invoiceCustCode,
        invoiceCustId: this.data.supplier.invoiceCustId,
        invoiceSetsOfBooksId: this.data.supplier.invoiceSetsOfBooksId,
        vendorCode: this.data.supplier.vendorCode,
        vendorId: this.data.supplier.vendorId,
        vendorSetsOfBooksId: this.data.supplier.vendorSetsOfBooksId,
        // sortList,
        // keyword: this.$refs.favorites.getSearchKeyWord(),
        supId: this.data.supplier.supId,
      }
    }
    let url = '/api/vcs/favoriteItem/page'
    App.getHttp()._post(url, params).then(res => {
      this.setData({
        focusList: this.data.focusList.concat(res||[]),
      }, () => {
        if (this.data.focusList && this.data.focusList.length > 0) {
          this.getStock()
        }
      })
      if (!res.length > 0) {
        wx.showToast({
          title: '没有更多数据了...',
          duration: 3000,
          icon: 'none'
        })
      }
    })
  },
  // 获取库存数据
  async getStock() {
    
    const queryParam = [];
    let goodsList = this.data.focusList
    goodsList.forEach(item => {
      queryParam.push({
        itemCode: item.itemCode,
        orgCode: this.data.supplier.saleOrgCode,
        beSell: item.beSell,
        itemType: item.itemTypeName,
      });
    })
    const content = await App.getHttp()._post(
      '/api/interface/erp/stock/query',
      {query:queryParam}
    );
    for (let f = 0; f < goodsList.length; f++) {
      const fItem = goodsList[f];
      const findTrue = content.find((item) => item.itemCode === fItem.itemCode);
      if (findTrue) {
        goodsList[f].stockQty = findTrue.stockQty || 0;
      } else {
        goodsList[f].stockQty = 0;
      }
    }
    this.setData({
      focusList: goodsList
    })
  },
  // 跳转商品详情
  onClickSkuDetail(e) {
    const item = e.currentTarget.dataset.item
    const supplier = this.data.supplier;
    let paramString = ''
    Object.keys(supplier).forEach((key) => {
      paramString = `${paramString}&${key}=${supplier[key]}`
    })
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/skuDetail/index?itemId=${item.itemId}${paramString}`,
    })
  },
  // 调出添加购物车卡片
  onAddToCarts(e) {
    let addItemInfo = e.currentTarget.dataset.item
    addItemInfo.qty = 1
    this.setData({
      addItemInfo: addItemInfo,
      showAddCartsPop: true
    })
  },
  // 添加购物车卡片关闭事件
  onCloseAddCarts(e) {
    if (e.detail) {
      this.addShops(e.detail)
    }
    this.setData({
      showAddCartsPop: false
    })
  },
  // 添加购物车回调
  addShops(item) {
    if (item.qty == 0) {
      wx.showToast({
        title: "购买数量不能为0",
        icon: "error",
      });
      return;
    }
    if (item.isBulkOrder === 2 && item.qty % item.itemNumber > 0) {
      wx.showToast({
        title: "整托商品下单数量必须是整托数的整数倍!",
        icon: "none",
      });
      return;
    }
    if (item.beSell === 2 && item.qty > item.stockQty) {
      wx.showToast({
        title: "促销商品下单数量超出了库存数量!",
        icon: "none",
      });
      return;
    }
    const reault = item;
    const supplier = this.data.supplier;
    const params = {
      channelCode: supplier.channelCode, //渠道
      channelId: supplier.channelId, //渠道
      supId: supplier.supId, //协同关系Id
      productCode: reault.productCode || '', //产品组
      saleOrgCode: supplier.saleOrgCode, // 销售组织
      orderType: 0, // 订单类型
      invoiceCustId: supplier.invoiceCustId, // 客户ID
      invoiceCustCode: supplier.invoiceCustCode, // 客户ID
      invoiceSetsOfBooksId: supplier.invoiceSetsOfBooksId, // 客户ID
      vendorSetsOfBooksId: supplier.vendorSetsOfBooksId, // 供应方账套ID
      vendorCode: supplier.vendorCode, // 供应商ID
      vendorId: supplier.vendorId, // 供应商ID
      sourceSystem: 2,
    };
    params.items = [
      {
        itemId: reault.itemId, // 商品ID
        itemName: reault.itemName, // 商品名称
        itemCode: reault.itemCode, // 商品编码
        itemCount: reault.qty, // 购买数量
        orderType: 0,
      }
    ]
    const url = '/api/vcs/mobile-web/myx/supOrder/addToCart'
    App.getHttp()._post(url, params).then(res => {
      wx.showToast({
        title: '加入成功',
        icon: 'success'
      })
      this.getCartsCount()
    })
  },
  // 取消收藏
  async onClickCollect(e) {
    let item = e.currentTarget.dataset.item;
    let url = '/api/vcs/mobile-web/myx/supOrder/deleteFavoriteItemBySaleOrgCode'
    await App.getHttp()._post(
      url,
      {
        ...this.data.supplier,
        itemId: item.itemId,
        itemName: item.itemName,
        itemCode: item.itemCode,
        itemUrl: item.itemUrl,
        specs: item.specs,
        type: item.type,
        brandId: item.brandId,
      }
    );
    this.setData({
      pageIndex: 1,
      focusList: []
    }, () => {
      this.getNoticeGoods()
    })
  },
  // 获取购物车数据
  getCartsCount() {
    this.selectComponent('#shopCar').getCartsCount()
  },
})