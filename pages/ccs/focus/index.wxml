<!-- pages/ccs/focus/index.wxml -->
<view class=" page root-layout" id="root-layout">
  <view class="filter-layout flex justify-content-between font-main" id="top-layout-org">
    <view class="supplierName" bindtap="onClickSelectSup">
      <view class="supplierName-text">{{supplier.saleOrgCodeName}}</view>
    </view>
    <goods-cart class="shopCar" id="shopCar" />
    <!-- <van-popup custom-class="popup-content-dialog" class="popup-dialog" closeable round show="{{ showPop }}" bind:close="onClose">
      <view class="popup-content-tag">
        <van-tag wx:for="{{saleOrgOptions}}" wx:key="index" plain="{{item.saleOrgCode !== supplier.saleOrgCode}}" round size="large" custom-class="popup-content-tag-item" type="primary" data-item="{{item}}" bind:tap="clickTag">{{item.saleOrgCodeName}}</van-tag>
      </view>
    </van-popup> -->
    <van-popup show="{{ showPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="questTypeCancel">
      <van-picker title="选择问题类型" show-toolbar columns="{{ saleOrgOptions }}" bind:confirm="questTypeConfirm" bind:cancel="questTypeCancel" />
    </van-popup>
  </view>
  <scroll-view scroll-y bindscrolltolower="onReachBottom"  style="width: 100%; height: {{calculateViewHeightPx}}px;">
    <!-- 关注列表 -->
    <block wx:if="{{focusList.length > 0}}">
      <view class="swiper-layout " wx:for="{{focusList}}" wx:for-index="tIndex" wx:key="tIndex">
        <view class="flex_center swiper-box">
          <goods-card class="goods-card flexbox" wx:key="tIndex" data-t-index="{{tIndex}}" item="{{item}}" bindtap="onClickSkuDetail" data-item="{{item}}" data-type="2" showCart isHorizontal bind:addToCarts="onAddToCarts" showRetailPrice="{{!isSupMode}}" showQtyOnhand="{{!isSupMode}}" bind:onClickCollect="onClickCollect" />
        </view>
      </view>
    </block>
    <!-- 缺省 -->
    <view class="none" wx:else>
      <no-product noneTxt="暂无收藏商品" />
    </view>
  </scroll-view>
  <add-carts show="{{showAddCartsPop}}" bind:onClose="onCloseAddCarts" item="{{addItemInfo}}" isSup="{{isSupMode}}"></add-carts>
</view>