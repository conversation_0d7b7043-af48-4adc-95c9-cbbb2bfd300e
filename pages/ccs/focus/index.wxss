/* pages/ccs/focus/index.wxss */
page{
  height: 100vh;
}
.root-layout{
  background: rgba(0,0,0,0.04);
}
.root-layout .none{
  padding-top: 160rpx;
}
.root-layout .filter-layout{
  padding: 10px 24rpx;
}
.root-layout  .filter-layout .right{
  color: #00b9c3;
  line-height: 32rpx;
}
.root-layout  .filter-layout .left{
  line-height: 32rpx;
}
.root-layout  .filter-layout .filter-icon{
  width: 32rpx;
  height: 32rpx;
  margin-left: 4rpx;
}

.supplierName {
  width: calc(100% - 100rpx);
  text-align: center;
}
.supplierName-text {
  /* width: 340rpx; */
  display: inline-block;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 44rpx;
  position: relative;
  padding-right: 40rpx;
  /* width: 100%; */
}
.supplierName-text::after {
  content: "";
  position: absolute;
  top: 18rpx;
  right: 0rpx;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 12rpx solid #8a8a8a;
  transform: rotate(180deg);
}
.shopCar {
  float: right;
}
.popup-content-dialog {
  padding: 40rpx;
  width: 100%;
}
.popup-content-tag {
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}
.popup-content-tag-item {
  /* width: 100%; */
  margin: 20rpx 20rpx 0;
  text-align: center;
  padding: 10rpx 40rpx;
}
.root-layout .card-layout{
  padding: 0 24rpx 10rpx 24rpx;
}
.root-layout .swiper-layout{
  margin-bottom: 24rpx;
}
.root-layout .swiper-box{
  background-color: white;
  border-radius: 8rpx;
}
.root-layout .goods-card{
  position:relative;
}
.root-layout .goods-card:after {
  position: absolute;
  bottom: 1rpx;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: rgba(0,0,0,0.1);
 }
 .root-layout .goods-card:last-child:after{  
  height:0;
}

.root-layout .swipe-right-del {
  position: relative;
  width:180rpx;
  height: 100%;
  background-color: #FF4A4D;
  color: #fff;
  text-align: center;
  line-height: 100%;
}
.root-layout .swipe-right-del .delete-button {
  position: absolute;
  padding: 0;
  margin: 0;
  width: 100%;
  top: 50%;
  transform: translate(0, -50%);
  color: #fff;
  border: 0;
  outline-style: none;
  outline: 0;
  background-color: #FF4A4D;
}

.root-layout .swipe-right-del .delete-button::after {
  border: none;
}
.root-layout .foot-layout{
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  padding-left: 24rpx;
  height: 100rpx;
  background-color: #FFFFFF;
}
.root-layout .foot-layout .all{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.45);
  font-weight: 400;
}
.root-layout .radio-img{
  width: 48rpx;
  height: 48rpx;
}
.root-layout .foot-layout .total{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.85);
  font-weight: 400;
  text-align: right;
  margin: 0 34rpx;
}
.root-layout .foot-layout .end{
  width: 240rpx;
  background: rgba(0,0,0,0.20);
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 100rpx;
}
.foot-layout .end-active{
  width: 240rpx;
  background: #ee1616;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 100rpx;
}
