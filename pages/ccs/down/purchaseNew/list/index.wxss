/* .root-layout {
  background: rgba(0, 0, 0, 0.04);
} */
.filter-layout {
  padding: 24rpx;
  background: #fff;
}
.filter-layout-search {
  justify-content: center;
  width: 686rpx;
  height: 72rpx;
  line-height: 72rpx;
  border-radius: 8rpx;
  /* opacity: 0.8; */
  background: #f0f0f0;
}
.search-text {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #8a8a8a;
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}
.content {
  overflow: hidden;
}
.scroll-left {
  width: 166rpx;
}
.scroll-right {
  box-sizing: border-box;
}
.filter-item {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #616161;
  margin-right: 48rpx;
  line-height: 44rpx;
}
.acitveFilter {
  color: #00b9c3;
}
.filter-down {
  transform: rotate(180deg);
}
.filter-img {
  width: 20rpx;
  height: 20rpx;
}
.goodsList {
  font-family: PingFangSC-Regular, Ping<PERSON>ang SC;
  margin: 24rpx 24rpx 24rpx 0;
}
.good-img-layout {
  position: relative;
}
.good-img {
  width: 172rpx;
  height: 172rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}
.good-noQty {
  width: 128rpx;
  height: 128rpx;
  position: absolute;
  left: 22rpx;
  top: 22rpx;
}
.good-info {
  height: 172rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}
.good-name {
  font-size: 28rpx;
  font-weight: 400;
  color: #242424;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-adjust {
  font-weight: 400;
  color: #9e9e9e;
  font-size: 20rpx;
  align-items: flex-end;
  justify-content: space-between;
}
.good-price {
  color: #FF4A4D;
}
.price-symbol, .price-rem {
  font-size: 16rpx;
}
.price-text {
  font-size: 28rpx;
}
.onhand-type {
  font-weight: 400;
  font-size: 20rpx;
  margin-left: 48rpx;
}
.type-1 {
  color: #FF4A4D;
}
.type-2 {
  color: #52C718;
}
.content-right {
  flex: 1;
  margin-left: 24rpx;
}
/* 重写样式 */
.van-sidebar-item--selected {
  color: #00b9c3 !important;
  border-color: #00b9c3 !important;
}
