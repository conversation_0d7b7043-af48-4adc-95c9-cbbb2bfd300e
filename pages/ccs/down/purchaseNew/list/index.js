// pages/ccs/down/purchaseNew/list/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    curSupplierId: '',
    paramSupplierId: '',
    bigClassId: '', // 一级分类id
    classList: [],
    offsetTop: 60,
    contentTop: 120,
    activeKey: 0,
    windowHeight: 667,
    goodsList: [],
    pageIndex: 1,
    pageSize: 10,
    filterParams: {
      orderByPrice: 0, // 1 降序 2 升序
      orderBySales: 0,
    },
    showAddCartsPop: false,
    addItemInfo: {},
    orderByPriceActive: false,
    orderByPrice: 0,
    orderBySalesActive: false,
    orderBySales: 0,
    topNum: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.data.bigClassId=options.bigClassId
    const rect = wx.getMenuButtonBoundingClientRect()
    const query = wx.createSelectorQuery()
    query.select('#listHead').boundingClientRect()
    query.select('#filter-layout').boundingClientRect()
    query.select('#goodFilter').boundingClientRect()
    let windowHeight = ''
    wx.getSystemInfo({
      success(res) {
        windowHeight = res.screenHeight
      }
    })
    query.exec((res) => {
      this.setData({
        offsetTop: rect.bottom + 6,
        windowHeight: (windowHeight - (res[0] ? res[0].height : 0) - (res[1] ? res[1].height : 0) - (res[2] ? res[2].height : 0)) * App.globalData.pxToRpxRatio,
        contentTop: (rect.bottom + 18) * App.globalData.pxToRpxRatio
      })
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    // 先判断切换的供应商为向上还是向下 跳转到对应不同的页面
    const purchaseType = wx.getStorageSync('vendorInfo').purchaseType
    if(purchaseType === 'sup') {
      wx.redirectTo({
        url: '/pages/ccs/sup/purchase/list/index?bigClassId=' + this.data.bigClassId
      })
    } else {
      await this.getClassificationList() // 加载分类数据
      await this.getList()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 加载商品分类
  async getClassificationList() {
    const res = await App.getHttp()._post('/api/mmd/itemClass/myx/getTree', {})
    if (res && res.length > 0) {
      const findClass = res.find(item => item.id == this.data.bigClassId)
      let classList = []
      if(findClass&&findClass.childItemClassList){
        classList = findClass.childItemClassList
      }
      classList.unshift({
        name: '本期推荐'
      })
      this.setData({
        classList
      })
    }
  },
  // 点击切换供应商
  onClickSelectSup() {
    wx.navigateTo({
      url: '/pages/ccs/down/purchaseNew/searchSup/index'
    })
  },
  // 左边切换分类
  onChangeLeftSidebar(e) {
    console.log(e.detail)
    this.setData({
      activeKey: e.detail,
      goodsList: []
    })
    this.getList()
  },
  // 查询商品数据
  getList() {
    let curMidItemClass = this.data.classList[this.data.activeKey]
    if(this.data.activeKey === 0) {
      const params = {
        pageIndex:this.data.pageIndex,
        pageSize:this.data.pageSize,
        param:{
          rcmdType: 1,
        },
      }
      const supplier = wx.getStorageSync('vendorInfo')
      if(supplier) {
        params.param.invoiceSetsOfBooksId = supplier.bsetsOfBooksId
        params.param.vendorSetsOfBooksId = supplier.ssetsOfBooksId
        params.param.invoiceCustCode = supplier.scustCode
        params.param.vendorCode = supplier.bvendorCode
      }
      App.getHttp()._post('/api/psi/mmsController/myx/down/getItemRcmd', params).then(res => {
        if (res && res.length > 0) {
          let goodsList = this.data.pageIndex === 1 ? [] : [...this.data.goodsList]
          this.setData({
            goodsList: goodsList.concat(res)
          })
        }
      })
    } else {
      let param = {
        pageIndex: this.data.pageIndex,
        pageSize: this.data.pageSize,
        param: {
          keyWord: '',
          bigItemClassId: this.data.bigClassId, // 一级品类
          midItemClassId: curMidItemClass.id, // 二级品类
          ...this.data.filterParams
        }
      }
      const supplier = wx.getStorageSync('vendorInfo')
      if(supplier) {
          param.param.invoiceSetsOfBooksId = supplier.bsetsOfBooksId
          param.param.vendorSetsOfBooksId = supplier.ssetsOfBooksId
          param.param.invoiceCustCode = supplier.scustCode
          param.param.vendorCode = supplier.bvendorCode
      }
      App.getHttp()._post('/api/psi/myx/purOrder/getPurchaseItemPage', param).then(res => {
        if (res && res.length > 0) {
          let goodsList = this.data.pageIndex === 1 ? [] : [...this.data.goodsList]
          this.setData({
            goodsList: goodsList.concat(res)
          })
        }
      })
    }
  },
  // 加入购物车
  onClickToShopCar(e) {
    let addItemInfo = e.currentTarget.dataset.item
    addItemInfo.qty = addItemInfo.boxMatchNumber || 1
    this.setData({
      addItemInfo: addItemInfo,
      showAddCartsPop: true,
    })
  },
  onchange(v) {
    if (!v.detail) return
    const reault = v.detail
    this.setData({
      addItemInfo: reault,
    })
  },
  onCloseAddCarts(e) {
    if (e.detail) {
      this.addShops(e.detail)
    } else {
      this.setData({
        showAddCartsPop: false
      })
    }
  },
  addShops(item) {
    if (item.qty === 0) {
      wx.showToast({
        title: '购买数量不能为0',
        icon: 'error'
      })
      return
    }
    const reault = item
    const supplier = wx.getStorageSync('vendorInfo')
    const params = {
      channelCode: reault.channelCode, //渠道
      productCode: reault.productCode, //产品组
      orderType: 0, // 订单类型
      qtyModel: 2, // 1扣减购物车 2增加购物车 3直接填充购买数量
      invoiceCustId: supplier.scustId, // 客户ID
      invoiceCustCode: supplier.scustCode, // 客户ID
      invoiceSetsOfBooksId: supplier.bsetsOfBooksId, // 客户ID
      vendorSetsOfBooksId: supplier.ssetsOfBooksId, // 供应方账套ID
      vendorCode: supplier.bvendorCode, // 供应商ID
      vendorId: supplier.bvendorId, // 供应商ID
      itemList: [{
        itemId: reault.itemId, // 商品ID
        itemCode: reault.itemCode, // 商品ID
        purchaseQty: reault.qty // 购买数量
      }]
    }
    App.getHttp()._post('/api/psi/shoppingCart/addToCarts', params).then(res => {
      this.selectComponent('#listHead').getCartsCount()
      this.setData({
        showAddCartsPop: false
      })
      wx.showToast({
        title: '添加成功',
        icon: 'success',
        image: "/asset/imgs/purchase/add-success.png",
      })
    })
  },
  onClickOrderByPrice() {
    let key = `filterParams.orderByPrice`
    if(this.data.orderByPrice === 0) {
      this.setData({
        orderByPriceActive: true,
        orderByPrice: 2,
        [key]: 2,
        pageIndex: 1,
        topNum: 0
      })
    } else {
      this.setData({
        orderByPrice: this.data.orderByPrice === 1 ? 2 : 1,
        [key]: this.data.orderByPrice === 1 ? 2 : 1,
        pageIndex: 1,
        topNum: 0
      })
    }
    this.getList()
  },
  onClickOrderBySales() {
    let key = `filterParams.orderBySales`
    if(this.data.orderBySales === 0) {
      this.setData({
        orderBySalesActive: true,
        orderBySales: 2,
        [key]: 2,
        pageIndex: 1,
        topNum: 0
      })
    } else {
      this.setData({
        orderBySales: this.data.orderBySales === 1 ? 2 : 1,
        [key]: this.data.orderBySales === 1 ? 2 : 1,
        pageIndex: 1,
        topNum: 0
      })
    }
    this.getList()
  },
  // 跳转详情
  onClickGood(e) {
    const item = e.currentTarget.dataset.item
    const vendorInfo = wx.getStorageSync("vendorInfo");
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/skuDetail/index?itemId=${item.itemId}&vendorCode=${item.vendorCode ? item.vendorCode : vendorInfo.bvendorCode}&custCode=${item.custCode}&channelCode=${item.channelCode}&productCode=${item.productCode}`
    })
  },
  // 搜索跳转
  onClickSearchListener(e) {
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/searchHis/index',
    })
  },
  // 触底加载
  onTolower() {
    this.setData({
      pageIndex: this.data.pageIndex + 1
    })
    this.getList()
  }
})