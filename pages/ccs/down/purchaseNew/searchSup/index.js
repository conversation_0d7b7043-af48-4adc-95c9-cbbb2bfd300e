// pages/ccs/down/purchaseNew/searchSup/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    keyword: '',
    supplierList: [],
    originSupplierList: [],
    curSupplierId: '',
    paramSupplierId: '',
    custInfo: {},
    listViewH: 475,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const supplier = wx.getStorageSync('vendorInfo')
    if(supplier) {
      this.setData({
        curSupplierId: supplier.supId||supplier.bsId,
        paramSupplierId: supplier.supId||supplier.bsId
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#list-layout').boundingClientRect()
    query.select('#footer-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH: res[1].top - res[0].top 
      })
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getsupplierList() // 加载供应商
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取供应商数据
  async getsupplierList() {
    const custInfo = wx.getStorageSync('custInfo')
    // 向下的供应商
    let bsRelates = custInfo.bsRelates || []
    // 写入purchaseType字段判断供应商类型 向下/向上
    bsRelates.forEach(item => {
      item.purchaseType = 'down'
      item.uniqueId = item.bsId
    })
    // 向上的供应商
    let supRelates = custInfo.supRelates || []
    supRelates.forEach(item => {
      item.purchaseType = 'sup'
      item.uniqueId = item.supId
    })
    // 两个类型供应商组合的数据
    let res = []
    // 做优先排序 当前账套为什么类型，什么类型的供应商就排前面
    if(custInfo.custType === 5) {
      res.push(...supRelates)
      res.push(...bsRelates)
    } else {
      res.push(...bsRelates)
      res.push(...supRelates)
    }
    let supplierList = this.data.supplierList
    if (res && res.length > 0) {
      supplierList = res
      this.setData({
        supplierList,
        originSupplierList: supplierList,
        custInfo
      })
    } else {
      // 未获取到供应商
      this.setData({
        supplierList: [],
        originSupplierList: []
      })
    }
  },
  // 切换单选框
  onClickRadio(e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      curSupplierId: id
    })
  },
  // 输入框改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 搜索事件
  onClickSearch(e) {
    let arr = [...this.data.originSupplierList]
    const custInfo = this.data.custInfo
    if(custInfo.custType === 5) {
      this.setData({
        supplierList: arr.filter(item => (item.saleOrgCodeName ? item.saleOrgCodeName.indexOf(this.data.keyword) != -1 : false) || (item.channelName ? item.channelName.indexOf(this.data.keyword) != -1 : false) || (item.bvendorName ? item.bvendorName.indexOf(this.data.keyword) != -1 : false))
      })
    } else {
      this.setData({
        supplierList: arr.filter(item => item.bvendorName.indexOf(this.data.keyword) != -1)
      })
    }
  },
  // 确定修改供应商
  onClickConfirm() {
    let that = this
    if(that.data.curSupplierId != that.data.paramSupplierId) {
      let changeSupObj = that.data.originSupplierList.find(item => item.uniqueId === that.data.curSupplierId)
      wx.setStorageSync('vendorInfo', changeSupObj)
    }
    wx.navigateBack({
      delta: 1
    })
  },
  // 取消回退
  onClickCancel() {
    wx.navigateBack({
      delta: 1
    })
  }
})