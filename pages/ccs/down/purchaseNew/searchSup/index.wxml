<view class="root-layout" >
    <van-sticky>
        <van-search value="{{ keyword }}" placeholder="请输入搜索关键词" use-action-slot bind:change="onChangeKeyword" bind:search="onClickSearch">
            <view slot="action" class="search-right" bind:tap="onClickSearch">搜索</view>
        </van-search>
    </van-sticky>
    <!-- 供应商列表 -->
    <listView viewHeightPx="{{listViewH}}" id="list-layout" >
        <van-radio-group value="{{curSupplierId}}">
            <view class="supItem flex-box align-center" data-id="{{item.uniqueId}}" bindtap="onClickRadio" wx:for="{{supplierList}}" wx:key="index">
                <block wx:if="{{item.purchaseType == 'sup'}}">
                    <text class="supplierType supplierType-{{ item.purchaseType }}">厂商采购</text>
                    <view class="sup-item-left">
                        <view class="field-item">
                            <view class="label-item">销售公司</view>
                            <view class="value-item">{{item.saleOrgCodeName}}</view>
                        </view>
                        <view class="field-item">
                            <view class="label-item">渠道</view>
                            <view class="value-item">{{item.channelName}}</view>
                        </view>
                        <view class="field-item">
                            <view class="label-item">事业部</view>
                            <view class="value-item">{{item.bvendorName}}</view>
                        </view>
                    </view>
                </block>
                <block wx:else>
                    <view class="item-left">
                        <text class="supplierType supplierType-{{ item.purchaseType }}">经销采购</text>
                        <text class="supplierName">{{item.bvendorName}}</text>
                    </view>
                </block>
                <van-radio name="{{item.uniqueId}}"></van-radio>
            </view>
        </van-radio-group>
    </listView>
    <!-- 底部按钮 -->
    <view class="footer flex-box align-center" id="footer-layout">
        <view class="cancel" bindtap="onClickCancel">取消</view>
        <view class="confirm" bindtap="onClickConfirm">确定</view>
    </view>
</view>