.root-layout {
  height: 100vh;
  background: rgba(0, 0, 0, 0.04);
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}

.search-right {
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #00b9c3;
    margin: 0 24rpx;
}
.supItem {
    padding: 24rpx;
    background-color: #fff;
    margin: 24rpx;
    border-radius: 8rpx;
}
.item-left {
    flex: 1;
    word-break: break-all;
    margin-right: 16rpx;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap:break-word;
    word-break: break-all;
    white-space: normal;
}
.sup-item-left {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.field-item {
  display: flex;
  align-items: center;
}
.label-item {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #8A8A8A;
  line-height: 44rpx;
  margin-right: 16rpx;
  width: 120rpx;
}
.value-item {
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #161C24;
    line-height: 44rpx;
    flex: 1;
    word-break: break-all;
    margin-right: 16rpx;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap:break-word;
    word-break: break-all;
    white-space: normal;
}
.supplierType {
  height: 40rpx;
  line-height: 40rpx;
  padding: 0 8rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #faae16;
  border-radius: 8rpx;
  border: 1px solid #faae16;
  margin-right: 10rpx;
}
.supplierType-sup {
  color: #faae16;
  border-color: #faae16;
  
}
.supplierType-down {
  color: #52c718;
  border-color: #52c718;
}
.supplierName {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
}
.footer {
    box-sizing: border-box;
    width: 100vw;
    position: fixed;
    bottom: env(safe-area-inset-bottom);
    background-color: #fff;
    padding: 8rpx 34rpx;
    justify-content: space-between;
    /* width:90vw; */
}
.cancel, .confirm {
    padding: 16rpx 130rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
}
.cancel {
    color: #242424;
    border-radius: 8rpx;
    border: 1px solid #DBDBDB;
}
.confirm {
    color: #FFFFFF;
    background: #00b9c3;
    border-radius: 4px;
}
/* 重写vant样式 */
.van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}
