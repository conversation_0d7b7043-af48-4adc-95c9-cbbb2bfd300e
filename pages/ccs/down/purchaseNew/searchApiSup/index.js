// pages/ccs/down/purchaseNew/searchSup/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    keyword: '',
    saleOrgCode: '',
    supplierList: [],
    item: {}, // 选中的经销商数据
    originSupplierList: [],
    scustId: '',
    custInfo: {},
    listViewH: 475,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      saleOrgCode: options.saleOrgCode,
      scustId: options.scustId
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#list-layout').boundingClientRect()
    query.select('#footer-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH: res[1].top - res[0].top 
      })
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.removeStorageSync('groupObj')
    this.getsupplierList() // 加载供应商
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取供应商数据
  async getsupplierList() {
    App.getHttp()._post('/api/mmd/common/bsSup/page', {
      pageIndex: 1,
      pageSize: 5,
      param: {
        isUsable: 2,
        saleOrgCode: this.data.saleOrgCode
      }
    }).then(res => {
      if (res && res.length > 0) {
        this.setData({
          supplierList: res
        })
      }
    })
  },
  // 切换单选框
  onClickRadio(e) {
    const item = e.currentTarget.dataset.item
    this.setData({
      item,
      scustId: item.scustId
    })
  },
  // 输入框改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 搜索事件
  onClickSearch(e) {
    // 搜索时间
  },
  // 确定修改供应商
  onClickConfirm() {
    let that = this
    let item = that.data.item
    const channelIdArr = item.channelIds.split(',');
    let groupObj = {
      agge: item.scustCode,
      invoiceCustCode: item.scustCode,
      invoiceCustId: item.scustId,
      invoiceCustName: item.scustName,
      invoiceSetsOfBooksId: item.bsetsOfBooksId,
      vendorCode: item.bvendorCode,
      vendorId: item.bvendorId,
      vendorName: item.bvendorName,
      vendorSetsOfBooksId: item.ssetsOfBooksId,
      supId: item.id,
      channelId: channelIdArr.length > 0 ? channelIdArr[channelIdArr.length - 1] : '',
      saleRegionCode: item.regionCode,
      agencyLevel: item.agencyLevel
    }
    if(that.data.item.scustId) {
      wx.setStorageSync('groupObj', groupObj)
    }
    wx.navigateBack({
      delta: 1
    })
  },
  // 取消回退
  onClickCancel() {
    wx.navigateBack({
      delta: 1
    })
  }
})