<view class="root-layout" >
    <!-- <van-sticky>
        <van-search value="{{ keyword }}" placeholder="请输入搜索关键词" use-action-slot bind:change="onChangeKeyword" bind:search="onClickSearch">
            <view slot="action" class="search-right" bind:tap="onClickSearch">搜索</view>
        </van-search>
    </van-sticky> -->
    <!-- 供应商列表 -->
    <listView viewHeightPx="{{listViewH}}" id="list-layout" >
        <van-radio-group value="{{scustId}}">
            <view class="supItem flex-box align-center" data-item="{{item}}" bindtap="onClickRadio" wx:for="{{supplierList}}" wx:key="index">
                <block>
                    <text class="supplierType">经销采购</text>
                    <view class="item-left">
                        <view class="supplierName">{{item.scustName}}</view>
                        <br/>
                        <view class="supplierName">{{item.scustCode}}</view>
                    </view>
                </block>
                <van-radio name="{{item.scustId}}"></van-radio>
            </view>
        </van-radio-group>
    </listView>
    <!-- 底部按钮 -->
    <view class="footer flex-box align-center" id="footer-layout">
        <view class="cancel" bindtap="onClickCancel">取消</view>
        <view class="confirm" bindtap="onClickConfirm">确定</view>
    </view>
</view>