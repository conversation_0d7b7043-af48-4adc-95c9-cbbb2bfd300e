// pages_sub/pages/ccs/down/searchHis/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageIndex: 1,
    pageSize: 10,
    clientRectTop: 24,
    clientRectBottom: 66,
    clientRectHeight: 32,
    clientRectWidth: 99,
    filterParams: {
      orderByPrice: undefined,
      orderBySales: undefined,
      lowerPrice: '',
      maxPrice: ''
    },
    showHis:true,
    hisList:[],
    goodsList:[],
    listViewH: 400,
    addItemInfo: {},
    windowPaddingTop: 176,
    offsetTop: 60,
    keyword: '',
    orderByPriceActive: false,
    orderByPrice: 0,
    orderBySalesActive: false,
    orderBySales: 0,
    showFilter: false,
    priceStart: '',
    priceEnd: '',
    showAddCartsPop: false,
    typeList: [],
    activeFilterType: '',
    isExpand: true
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onLoad: function (options) {
    if(options.brandClassList) {
      this.setData({
        filterParams: {
          orderByPrice: undefined,
          orderBySales: undefined,
          tempSpecies: options.brandClassList.split('-')
        }
      })
    }
    // this.initSearchHis()
    this.getClassList()
  },
  initPage() {
    const rect = wx.getMenuButtonBoundingClientRect()
    this.setData({
      clientRectTop: rect.top,
      clientRectBottom: (rect.bottom + 10), //10补偿余量
      clientRectWidth: (rect.width + 12), //12补偿间隙
      clientRectHeight: (rect.height),
      windowPaddingTop: (rect.bottom + 6) * App.globalData.pxToRpxRatio,
      offsetTop: rect.bottom + 6,
    })
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#listHead').boundingClientRect()
    query.exec((res)=>{
      this.setData({
        listViewH:res[0].height-res[1].height
      })
    })
  },
  initSearchHis() {
    App.getHttp()._post('/api/mms/searchHis/myx/page', {
      pageIndex: 1,
      pageSize: 20,
      param: {
        busCode: 'purchase_item_search', 
        busModule: 'psi'
      }
    }).then(res => {
      this.setData({
        hisList:res,
        showHis:true,
      })
    })
  },
  addSearchHist(keyWord){
    // 储存搜索历史
    App.getHttp()._post('/api/mms/searchHis/myx/create', {keyWord: keyWord, busCode: 'purchase_item_search', busModule: 'psi'})
  },
  onBackListener() {
    wx.navigateBack({
      delta: 1,
    })
  },
  onClickHisKeyWord(e){
    this.setData({
      pageIndex: 1,
      goodsList: [],
      keyword: e.target.dataset.keyword
    })
    this.getList(e.target.dataset.keyword)
  },
  onLoadMore: function () {
    this.setData({
      pageIndex: (this.data.pageIndex + 1)
    })
    this.getList()
  },
  onConfirm(e) {
    this.setData({
      pageIndex: 1,
      goodsList: [],
      showHis: true,
    })
    let keyword = this.data.keyword
    if (keyword) {
      this.addSearchHist(keyword)
    }
    this.getList()
  },
  getList() {
    let param = {
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      param: {
        keyword:this.data.keyword,
        bigItemClassId: this.data.activeFilterType,
        // bigItemClassId: this.data.filterParams.tempSpecies[0], // 一级品类
        // midItemClassId: this.data.filterParams.tempSpecies[1], // 二级品类
        ...this.data.filterParams
      }
    }
    let vendorInfo = wx.getStorageSync('vendorInfo')
    param.param.invoiceSetsOfBooksId = vendorInfo.bsetsOfBooksId
    param.param.vendorSetsOfBooksId = vendorInfo.ssetsOfBooksId
    param.param.invoiceCustCode = vendorInfo.scustCode
    param.param.vendorCode = vendorInfo.bvendorCode
    App.getHttp()._post('/api/psi/myx/purOrder/getPurchaseItemPage', param).then(res => {
      if (res && res.length > 0) {
        let goodsList = this.data.pageIndex === 1 ? [] : [...this.data.goodsList]
        this.setData({
          // showHis: false,
          goodsList: goodsList.concat(res)
        })
        this.initPage()
      } else {
        if(this.data.pageIndex === 1) {
          this.setData({
            goodsList: []
          })
        }
      }
    })
    this.setData({
      showHis: false
    })
  },
  onClearAll(){
    App.getHttp()._post('/api/mms/searchHis/myx/clear', { busCode: 'purchase_item_search', busModule: 'psi' }).then(res => {
      this.setData({
        hisList:[],
      })
    })
  },
  // 数量设置关闭回调
  onCloseAddCarts(e) {
    if (e.detail) {
      this.addShops(e.detail)
    } else {
      this.setData({
        showAddCartsPop: false
      })
    }
  },
  onchange(v) {
    if (!v.detail) return
    const reault = v.detail
    this.setData({
      addItemInfo: reault,
      // spuSpecSel:reault.spuSpecSel
    })
  },
  // 添加购物车/理及购买
  addShops(item) {
    if (item.qty === 0) {
      wx.showToast({
        title: '购买数量不能为0',
        icon: 'error'
      })
      return
    }
    const reault = item
    const params = {
      channelCode: reault.channelCode, //渠道
      productCode: reault.productCode, //产品组
      orderType: 0, // 订单类型
      qtyModel: 2, // 1扣减购物车 2增加购物车 3直接填充购买数量
      invoiceCustId: reault.invoiceCustId, // 客户ID
      invoiceCustCode: reault.invoiceCustCode, // 客户ID
      invoiceSetsOfBooksId: reault.invoiceSetsOfBooksId, // 客户ID
      vendorSetsOfBooksId: reault.vendorSetsOfBooksId, // 供应方账套ID
      vendorCode: reault.vendorCode, // 供应商ID
      vendorId: reault.vendorId, // 供应商ID
      itemList: [{
        itemId: reault.itemId, // 商品ID
        itemCode: reault.itemCode, // 商品ID
        purchaseQty: reault.qty // 购买数量
      }]
    }
    App.getHttp()._post('/api/psi/shoppingCart/addToCarts', params).then(res => {
      this.selectComponent('#listHead').getCartsCount()
      this.setData({
        showAddCartsPop: false
      })
      // 延迟0.5毫秒后显示是因为全局的loading会使这个toast消失不显示
      setTimeout(() => {
        wx.showToast({
          title: '添加成功',
          icon: 'success',
          image: "/asset/imgs/purchase/add-success.png",
        })
      }, 500)
    })
  },
  // 输入框内容改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 点击价格排序
  onClickOrderByPrice() {
    let key = `filterParams.orderByPrice`
    if(this.data.orderByPrice === 0) {
      this.setData({
        orderByPriceActive: true,
        orderByPrice: 2,
        [key]: 2,
        pageIndex: 1
      })
    } else {
      this.setData({
        orderByPrice: this.data.orderByPrice === 1 ? 2 : 1,
        [key]: this.data.orderByPrice === 1 ? 2 : 1,
        pageIndex: 1
      })
    }
    this.getList()
  },
  // 点击销量排序
  onClickOrderBySales() {
    let key = `filterParams.orderBySales`
    if(this.data.orderBySales === 0) {
      this.setData({
        orderBySalesActive: true,
        orderBySales: 2,
        [key]: 2,
        pageIndex: 1
      })
    } else {
      this.setData({
        orderBySales: this.data.orderBySales === 1 ? 2 : 1,
        [key]: this.data.orderBySales === 1 ? 2 : 1,
        pageIndex: 1
      })
    }
    this.getList()
  },
  // 打开筛选框
  onClickFilter() {
    this.setData({
      showFilter: true
    })
  },
  // 关闭筛选框
  onCloseFilter() {
    this.setData({
      showFilter: false
    })
  },
  // 最小价改变
  startChange(event) {
    let value = event.detail.value || undefined
    this.setData({
      priceStart: value
    })
  },
  // 最大价改变
  endChange(event) {
    let value = event.detail.value || undefined
    this.setData({
      priceEnd: value
    })
  },
  // 重置
  onClickReset() {
    this.setData({
      priceStart: '',
      priceEnd: '',
      activeFilterType: ''
    })
  },
  // 确认筛选条件
  onClickConfirmFilter() {
    const key1 = `filterParams.lowerPrice`
    const key2 = `filterParams.maxPrice`
    this.setData({
      [key1]: this.data.priceStart,
      [key2]: this.data.priceEnd,
      showFilter: false,
      pageIndex: 1,
    })
    this.getList()
  },
  // 加入购物车
  onClickToShopCar(e) {
    let addItemInfo = e.currentTarget.dataset.item
    addItemInfo.qty = addItemInfo.boxMatchNumber || 1
    if(this.getTabBar()) {
      this.getTabBar().setData({
        isShow:false
      })
    }
    this.setData({
      addItemInfo: addItemInfo,
      showAddCartsPop: true,
    })
  },
  // 跳转详情
  onClickGood(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/skuDetail/index?itemId=${item.itemId}&vendorCode=${item.vendorCode}&custCode=${item.custCode}&channelCode=${item.channelCode}&productCode=${item.productCode}`
    })
  },
  // 获取分类数据
  async getClassList() {
    const res = await App.getHttp()._post('/api/mmd/itemClass/myx/getTree', {})
    if(res && res.length > 0) {
      this.setData({
        typeList: res
      })
    }
  },
  // 点击品类
  onClickFilterType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      activeFilterType: type
    })
  },
  // 点击展开或收起
  onClickExpand() {
    this.setData({
      isExpand: !this.data.isExpand
    })
  }
})