/* pages_sub/pages/ccs/down/searchHis/index.wxss */
.search-layout {
  width: 100%;
  padding: 0 24rpx 12rpx;
  box-sizing: border-box;
}
.search-layout .backImg {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.history-layout {
  padding: 0 24rpx;
  /* margin-top: 16rpx; */
}
.history-layout .title {
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
}
.history-layout .clean {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.history-layout .icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}
.history-layout .listbox {
  margin-top: 32rpx;
}
.history-layout .listbox .label {
  height: 64rpx;
  padding: 0 40rpx;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 64rpx;
  font-weight: 400;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.new-product {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

.new-product .none {
  padding: 60rpx 0;
  margin-top: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
}

.new-product .goods-card {
  position: relative;
}

.new-product .goods-card:after {
  position: absolute;
  bottom: 1rpx;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  content: "";
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: rgba(0, 0, 0, 0.1);
}

.new-product .goods-card:last-child:after {
  height: 0;
}
.search-right {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
  margin: 0 24rpx;
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}
.good-filter {
  justify-content: space-between;
}
.filter-item {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #616161;
  margin-right: 48rpx;
  line-height: 44rpx;
}
.acitveFilter {
  color: #00b9c3;
}
.filter-down {
  transform: rotate(180deg);
}
.filter-img {
  width: 20rpx;
  height: 20rpx;
}
.goodsList {
  font-family: PingFangSC-Regular, PingFang SC;
  margin: 24rpx 0;
}
.good-img-layout {
  position: relative;
}
.good-img {
  width: 224rpx;
  height: 224rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}
.good-noQty {
  width: 128rpx;
  height: 128rpx;
  position: absolute;
  left: 44rpx;
  top: 44rpx;
}
.good-info {
  height: 224rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}
.good-name {
  font-size: 32rpx;
  font-weight: 400;
  color: #242424;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-adjust {
  font-weight: 400;
  color: #9e9e9e;
  font-size: 24rpx;
  align-items: flex-end;
  justify-content: space-between;
}
.good-price {
  color: #FF4A4D;
}
.price-symbol, .price-rem {
  font-size: 20rpx;
}
.price-text {
  font-size: 32rpx;
}
.onhand-type {
  font-weight: 400;
  font-size: 24rpx;
  margin-left: 48rpx;
}
.type-1 {
  color: #FF4A4D;
}
.type-2 {
  color: #52C718;
}
.filter-content {
  padding: 0 32rpx 0 24rpx;
}
.filter-title {
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
  margin: 24rpx 0;
}
.price-interval {
  line-height: 70rpx;
  margin-bottom: 32rpx;
  justify-content: space-around;
}
.price-interval .price-input {
  vertical-align: top;
  text-align: center;
  display: inline-block;
  width: 244rpx;
  /* padding: 0 20rpx;
  margin: 0 20rpx; */
  height: 56rpx;
  border-radius: 28rpx;
  background-color: #F0F0F0;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #8A8A8A;
  line-height: 40rpx;
}
.filter-footer {
  position: fixed;
  bottom: 0;
  padding: 8rpx 32rpx;
  box-sizing: border-box;
  justify-content: space-around;
  width: 100%;
}
.filter-cancel, .filter-confirm {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 48rpx;
  border-radius: 8rpx;
  padding: 16rpx 98rpx;
}
.filter-cancel {
  color: #242424;
  background: rgba(0,0,0,0);
  border: 1px solid #DBDBDB;
}
.filter-confirm {
  background: #00b9c3;
  color: #FFFFFF;
}
.grid-content {
  padding: 0!important;
  margin-bottom: 32rpx;
}
.grid-item {
  background: #F0F0F0;
  border-radius: 28rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #616161;
  line-height: 40rpx;
  /* width: 172rpx; */
  padding: 8rpx 16rpx;
  text-align: center;
}
.active-filter-type {
  color: #00b9c3;
  background:  #E7F2FF;
}
.filter-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expend-block {
  display: flex;
  align-items: center;
}
.expand-text {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 40rpx;
}
/* 重写vant样式 */
.van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}
