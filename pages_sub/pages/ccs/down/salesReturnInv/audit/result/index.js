// pages_sub/pages/ccs/down/salesReturnInv/audit/result/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    billNo: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.billNo) {
      this.setData({
        billNo: options.billNo
      })
    }
  },
  onClickCheckDetail(){
   wx.redirectTo({
     url: '/pages_sub/pages/ccs/down/salesReturnInv/detail/index?billNo=' + this.data.billNo,
   })
  },
  onClickBack(){
    wx.redirectTo({
      url: '/pages_sub/pages/ccs/down/salesReturnInv/index',
    })
  }
 
})