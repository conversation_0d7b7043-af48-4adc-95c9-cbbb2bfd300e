var getTotalCount = function (value) {
  var totalCount = 0;
  if(value.length > 0) {
    for(var i = 0; i< value.length; i++) {
        totalCount += Number(value[i].confirmReqQty)
    }
  }
  return totalCount;
};
var getTotalPrice = function (value) {
    var totalPrice = 0;
    if(value.length > 0) {
      for(var i = 0; i< value.length; i++) {
        var count = value[i].confirmReqQty
        var price = value[i].price
        totalPrice += (price * count)
      }
    }
    return totalPrice.toFixed(2);
  };

module.exports = {
  getTotalCount: getTotalCount,
  getTotalPrice: getTotalPrice
};
