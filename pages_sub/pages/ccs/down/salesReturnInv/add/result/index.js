// pages_sub/pages/ccs/down/order/return/returnResult/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id){
      this.setData({
        id:options.id
      })
    }
  },
  onClickCheckDetail(){
    // 跳转到退入库单详情
    wx.redirectTo({
      url: '/pages_sub/pages/ccs/down/salesReturnInv/detail/index?id=' + this.data.id,
    })
  },
  onClickBack(){
    // 跳转到采购退货 关闭所有页面
    wx.redirectTo({
      url: '/pages_sub/pages/ccs/down/salesReturnInv/index',
    })
  }
 
})