// pages_sub/pages/ccs/down/salesReturnInv/add/index.js
const App = getApp()
import Dialog from "@vant/weapp/dialog/dialog";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    collapseNames: [],
    supplierOptions: [], // 采购方数据源
    supplierValue: undefined, // 选择的采购方
    supplierName: '请选择',
    warehouseOptions: [], // 仓库数据源
    warehouseId: undefined, // 选择的仓库
    warehouseName: '请选择',
    show: false,
    pickerColumns: [],
    selectedKey: '',
    selectedName: '',
    shipModeValue: '',
    shipMode: [],
    addrInfo: {}, // 地址信息
    goodsList: [], // 选择的商品数据
    listViewH: 300,
    windowPaddingTop: 120,
    rectTop: 96,
    rectHeight: 64
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.removeStorageSync('saleItemInfo')
    wx.removeStorageSync('returnGoods')
    wx.removeStorageSync('address')
    let shipMode = wx.getStorageSync('dictMap').shipMode // 发货方式数据
    this.setData({
      shipMode,
      shipModeValue: '5',//默认自提
    })
    this.getSupplierList() // 加载采购方
    this.getWarehouseList() // 加载仓库数据源
    this.getDefaultAddress() // 获取默认地址
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.setPageSize()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const selectAddr = wx.getStorageSync('address')
    const returnGoods = wx.getStorageSync('returnGoods')
    if (selectAddr) {
      this.setData({
        addrInfo: selectAddr
      })
    }
    if (returnGoods && returnGoods.length > 0) {
      returnGoods.map(item => {
        item.applyQty = 1
        const findIndex = this.data.goodsList.findIndex(find => find.itemId === item.itemId)
        if (findIndex > -1) {
          this.data.goodsList[findIndex].applyQty = this.data.goodsList[findIndex].applyQty + 1
        } else {
          this.data.goodsList.push(item)
        }
      })
      this.setData({
        goodsList: this.data.goodsList
      })
      wx.removeStorageSync('returnGoods')
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },
  setPageSize() {
    const query = wx.createSelectorQuery()
    query.select('#page-detail').boundingClientRect()
    query.select('#footer-layout').boundingClientRect()
    const rect = wx.getMenuButtonBoundingClientRect()
    query.exec((res) => {
      let windowHeight = ''
      wx.getSystemInfo({
        success(sysInfo) {
          windowHeight = sysInfo.screenHeight
        }
      })
      console.log();
      this.setData({
        rectTop: rect.top * App.globalData.pxToRpxRatio,
        rectHeight: rect.height * App.globalData.pxToRpxRatio,
        windowPaddingTop: (rect.top + rect.height + 6) * App.globalData.pxToRpxRatio,
        listViewH: windowHeight - res[0].bottom - res[1].height -rect.top - App.globalData.deviceBottomOccPx
      })
    })
  },
  // 获取采购方数据
  async getSupplierList() {
    App.getHttp()._post('/api/mmd/common/bsRelate/queryCust', {
      pageIndex: 1,
      pageSize: 200,
      param: { isUsable: 2 }
    }).then(recordList => {
      if (recordList && recordList.length > 0) {
        const supplierOptions = recordList.map(item => {
          return { text: item.name, id: item.id, code: item.code }
        })
        this.setData({
          supplierOptions,
          supplierValue: supplierOptions[0].id, // 选择的采购方
          supplierName: supplierOptions[0].text,
        })
        wx.setStorageSync('saleItemInfo', supplierOptions[0])
      } else {
        wx.removeStorageSync('saleItemInfo')
      }
    })
  },
  async getWarehouseList() {
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          text: res[r].name,
          id: res[r].id
        })
        if (res[r].isDefault === 2) {
          this.setData({
            warehouseId: res[r].id,
            warehouseName: res[r].name,
          })
        }
      }
      this.setData({
        warehouseOptions: warehouseColumns
      })
    })
  },
  onClickTodo(e) {
    let type = e.currentTarget.dataset.type
    let value = e.currentTarget.dataset.value
    let name = e.currentTarget.dataset.name
    this.setData({
      show: true,
      pickerColumns: this.data[type],
      selectedKey: value,
      selectedName: name
    })
  },
  onCancel() {
    this.setData({
      show: false,
      pickerColumns: []
    })
  },
  onConfirm(event) {
    let detail = event.detail.value
    let value = this.data.selectedKey
    let name = this.data.selectedName
    this.setData({
      show: false,
      [name]: detail.text,
      [value]: detail.id
    })
    if (value === 'supplierValue') {
      wx.setStorageSync('saleItemInfo', detail)
    }
  },
  // 改变运送方式
  onChangeShip(e) {
    this.setData({
      shipModeValue: e.detail
    })
  },
  getDefaultAddress: function () {
    App.getHttp()._post('/api/mmd/baseCustAddr/myx/page', {
      pageIndex: 1,
      pageSize: 1,
      param: { isDefault: 2, isUsable: 2, sourceId: wx.getStorageSync('custInfo').custId }
    }).then(res => {
      if (res && res.length > 0) {
        this.setData({
          addrInfo: res[0]
        })
      }
    })
  },
  // 跳转选择地址
  onClickAddress() {
    wx.navigateTo({
      url: `/pages/ccs/moreFeatures/addr/index?action=order&title=发货地址`,
    })
  },
  onClickBack() {
    wx.navigateBack({
      delta: 1
    })
  },
  // 跳转选择商品
  onClickToGood() {
    let data = this.data
    if (!data.supplierValue || !data.warehouseId) {
      wx.showToast({
        title: '请先选择' + (!data.supplierValue ? '采购方' : '仓库'),
        icon: 'error'
      })
      return
    }
    // 跳转商品选中页面
    let url = `/pages_sub/pages/ccs/down/order/return/selectGoods/index?warehouseId=${data.warehouseId}&warehouseName=${data.warehouseName}&modeUrl=currentInv`
    wx.navigateTo({
      url: url,
    })
  },
  // 改变退货数量
  onChangeReturnQty(e) {
    const index = e.currentTarget.dataset.index
    let arr = [...this.data.goodsList]
    arr[index].applyQty = e.detail
    this.setData({
      goodsList: arr
    })
  },
  // 输入框失焦 格式化两位小数
  onFormatPrice(e) {
    const index = e.currentTarget.dataset.index
    let arr = [...this.data.goodsList]
    let formatNum = e.detail.value ? Number(e.detail.value).toFixed(2) : ''
    arr[index].applyPrice = formatNum
    this.setData({
      goodsList: arr
    })
  },
  onChangePrice(e) {
    const index = e.currentTarget.dataset.index
    let arr = [...this.data.goodsList]
    arr[index].applyPrice = e.detail
    this.setData({
      goodsList: arr
    })
  },
  // 提交退货申请
  onClickSubmit() {
    if (!this.data.shipModeValue) {
      wx.showToast({
        icon: 'none',
        title: '请选择运送方式!'
      })
      return false
    }
    if (this.data.goodsList.length === 0) {
      wx.showToast({
        icon: 'none',
        title: '请选择退货商品!'
      })
      return false
    }
    if (this.data.goodsList.find(item => item.applyQty === 0)) {

      wx.showToast({
        icon: 'none',
        title: '请填写入库数量!'
      })
      return false
    }
    if (this.data.goodsList.find(item => !item.applyPrice)) {
      wx.showToast({
        icon: 'none',
        title: '请填写退货单价!'
      })
      return false
    }
    Dialog.confirm({
      title: "温馨提示",
      message: "确定要退货入库吗?",
      context: this,
    }).then(() => {
      const vendorInfo = wx.getStorageSync('custInfo')
      const saleItemInfo = wx.getStorageSync('saleItemInfo')
      let exitDiff = false
      const params = {
        // 发运方式
        shipMode: this.data.shipModeValue,
        // 仓库
        warehouseId: this.data.warehouseId,
        // 供应商
        vendorId: vendorInfo.custId,
        vendorCode: vendorInfo.custCode,
        vendorName: vendorInfo.custName,
        // 采购方
        custId: saleItemInfo.id,
        custCode: saleItemInfo.code,
        custName: saleItemInfo.text,
        // 详细地址
        contactAddr: this.data.addrInfo.contactAddr,
        defineAddr: this.data.addrInfo.defineAddr,
        // 联系人 
        contactPerson: this.data.addrInfo.name,
        // 联系电话 
        telPhone: this.data.addrInfo.phone,
        // 发货地址
        provinceId: this.data.shipModeValue === 1 ? this.data.addrInfo.provinceId : '',
        cityId: this.data.shipModeValue === 1 ? this.data.addrInfo.cityId : '',
        districtId: this.data.shipModeValue === 1 ? this.data.addrInfo.districtId : '',
        townId: this.data.shipModeValue === 1 ? this.data.addrInfo.townId : '',
        addLineList: this.data.goodsList.map(item => {
          if (!exitDiff && item.barCodeFlowResponseDTOList) exitDiff = item.applyQty > item.barCodeFlowResponseDTOList.length
          return {
            itemId: item.itemId,
            itemCode: item.itemCode,
            itemName: item.itemName,
            priceBill: item.applyPrice,
            pricecBillF: item.applyPrice,
            billQty: item.applyQty,
            warehouseId: this.data.warehouseId,//后端强制要的
            barCodeFlowResponseDTOList: item.barCodeFlowResponseDTOList//条码信息后端要
          }
        })
      }
      if (exitDiff) {
        wx.showModal({
          title: '是否出库',
          content: '当前商品入库数量大于条码数量，请确认是否继续入库?',
          success: (resM) => {
            App.getHttp()._post('/api/psi/saleReturnOutBill/myx/createConfirm', params, true).then(res => {
              if (res && res.code === 200) {
                // 退货成功，
                wx.redirectTo({
                  url: `/pages_sub/pages/ccs/down/salesReturnInv/add/result/index?id=${res.content}`,
                })
              } else {
                wx.showToast({
                  icon: 'error',
                  title: res.detail,
                })
              }
            })
          }
        })
      } else {
        App.getHttp()._post('/api/psi/saleReturnOutBill/myx/createConfirm', params, true).then(res => {
          if (res && res.code === 200) {
            // 退货成功，
            wx.redirectTo({
              url: `/pages_sub/pages/ccs/down/salesReturnInv/add/result/index?id=${res.content}`,
            })
          } else {
            wx.showToast({
              icon: 'error',
              title: res.detail,
            })
          }
        })
      }
    })
  },
  // 折叠监听
  onChangeCollape(event) {
    this.setData({
      collapseNames: event.detail,
    });
  },
  // 扫码扣减商品
  onClickReduceScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/scanPDA/saleReturnInv/add/index?mode=reduce&id=${this.data.id}`,
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.setData({
                goodsList: data.dataList
              })
            }
          },
        },
        success: (res)=> {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('acceptDataFromOpenerPage', { dataList: this.data.goodsList })
        }
      })
    } else {
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const goodsInfo = await App.getHttp()._post('/api/psi/barCodeFlow/scanShimgeGroupBarCode', { barCode: res.result, businessType: 6 })
            const httpResult = goodsInfo.map(info => {
              return {
                itemCode: info.itemCode,
                itemId: info.itemId,
                itemName: info.itemName,
                itemUrl: info.itemUrl,
                specs: info.specs,
                price: 0,
                applyQty: info.barCodeFlowResponseDTOList.length,
                qtyAvi: info.qtyAvi,
                barCodeFlowResponseDTOList: info.barCodeFlowResponseDTOList
              }
            })
            for (let index = 0; index < httpResult.length; index++) {
              const element = httpResult[index]
              const findIndex = this.data.goodsList.findIndex(find => find.itemId === element.itemId)
              if (findIndex > -1) {
                const findGood = this.data.goodsList[findIndex]
                if (findGood.barCodeFlowResponseDTOList && findGood.barCodeFlowResponseDTOList.length > 0) {
                  element.barCodeFlowResponseDTOList.forEach(eleForItem => {
                    const findBarCodeIndex = findGood.barCodeFlowResponseDTOList.findIndex(findItem => findItem.barCode === eleForItem.barCode)
                    if (findBarCodeIndex > -1) {
                      this.data.goodsList[findIndex].applyQty = this.data.goodsList[findIndex].applyQty - 1
                      this.data.goodsList[findIndex].barCodeFlowResponseDTOList.splice(findBarCodeIndex, 1)
                    }
                  })
                }
                this.setData({
                  goodsList: this.data.goodsList
                })
              } else {
                wx.showToast({
                  title: '条码不存在退货商品中!',
                  icon: 'none'
                })
              }
            }
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail: (err) => {
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 扫码增加商品
  onClickAddScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/scanPDA/saleReturnInv/add/index?mode=add&id=${this.data.id}`,
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.setData({
                goodsList: data.dataList
              })
            }
          },
        },
        success: (res)=> {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('acceptDataFromOpenerPage', { dataList: this.data.goodsList })
        }
      })
    } else {
    // 允许从相机和相册扫码
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const goodsInfo = await App.getHttp()._post('/api/psi/barCodeFlow/scanShimgeGroupBarCode', { barCode: res.result, businessType: 6 })
            const httpResult = goodsInfo.map(info => {
              return {
                itemCode: info.itemCode,
                itemId: info.itemId,
                itemName: info.itemName,
                itemUrl: info.itemUrl,
                specs: info.specs,
                price: 0,
                applyQty: info.barCodeFlowResponseDTOList.length,
                qtyAvi: info.qtyAvi,
                barCodeFlowResponseDTOList: info.barCodeFlowResponseDTOList
              }
            })
            if (this.data.goodsList.length === 0) {
              this.setData({
                goodsList: httpResult
              })
            } else {
              for (let index = 0; index < httpResult.length; index++) {
                const element = httpResult[index]
                const findIndex = this.data.goodsList.findIndex(find => find.itemId === element.itemId)
                // 存在一样的商品编码
                if (findIndex > -1) {
                  const findItem = this.data.goodsList[findIndex]
                  if (findItem.barCodeFlowResponseDTOList && findItem.barCodeFlowResponseDTOList.length > 0) {
                    let exitDiff = false
                    //过滤原来条码与新扫码相同的扫码
                    element.barCodeFlowResponseDTOList.forEach(barForEach => {
                      if (findItem.barCodeFlowResponseDTOList.findIndex(barCodeFind => barCodeFind.barCode === barForEach.barCode) === -1) {
                        exitDiff = true
                        this.data.goodsList[findIndex].applyQty = this.data.goodsList[findIndex].applyQty + 1
                        this.data.goodsList[findIndex].barCodeFlowResponseDTOList.push(barForEach)
                      }
                    })
                    if (!exitDiff) {
                      wx.showToast({
                        title: '条码重复!',
                        icon: 'none'
                      })
                    }
                  } else {
                    this.data.goodsList[findIndex].applyQty = this.data.goodsList[findIndex].applyQty + 1
                    this.data.goodsList[findIndex].barCodeFlowResponseDTOList = element.barCodeFlowResponseDTOList
                  }
                  this.setData({
                    goodsList: this.data.goodsList
                  })
                } else {
                  this.data.goodsList.push(element)
                  this.setData({
                    goodsList: this.data.goodsList
                  })
                }
              }
            }
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail: (err) => {
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 侧滑删除
  onClickDelItem(e) {
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: () => {
        const itemId = e.currentTarget.dataset.itemId
        this.setData({
          goodsList: this.data.goodsList.filter(res => res.itemId != itemId)
        })
      }
    })
  },
})