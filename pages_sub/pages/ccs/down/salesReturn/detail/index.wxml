<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<view class="root-layout">
  <view class="head-layout flex">
    <view class="bill-state">{{ backNameList[detailData.state] }}</view>
    <view class="bill-no flex align-items-center">
      <view>订单号:</view>
      <view class="no-text">{{detailData.billNo}}</view>
      <van-icon size="12" bindtap="onClickCopy" data-no="{{detailData.billNo}}" name="/asset/imgs/purchase/copy.png"></van-icon>
    </view>
  </view>
  <view class="address-layout">
    <van-icon size="20" name="/asset/imgs/purchase/location2.png"></van-icon>
    <view class="address-info">
      <view class="address-text">{{detailData.toContactAddress}}</view>
      <view class="contact">{{detailData.fromContactName || ''}} {{detailData.fromContactNumber || ''}}</view>
    </view>
  </view>
  <block wx:if="{{detailData.addLineList && detailData.addLineList.length > 0}}">
    <view class="cust-layout">
      <view class="cust">{{detailData.invoiceCustName}}</view>
      <view class="warehouse">{{detailData.backWarehouseName || ''}}</view>
    </view>
    <view class="line"></view>
    <view class="good-layout" wx:for="{{detailData.addLineList}}" wx:key="index">
      <view class="good-item">
        <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load></image>
        <view class="good-info">
          <view class="good-name">{{item.itemName}}</view>
          <view class="good-price">
            <view class="price-text">￥{{item.applyPrice}}</view>
            <view class="price-qty">共{{item.applyQty}}件</view>
          </view>
        </view>
      </view>
      <view class="total-block">
        <text class="total-label">退货数量{{item.auditQtyBill || item.applyQty}}件</text>
      </view>
    </view>
  </block>
  <van-field input-align="right" readonly label="创建时间" value="{{detailData.createTime}}"></van-field>
  <van-field input-align="right" readonly label="退货金额" value="{{'¥' + detailData.totalAmount}}"></van-field>
  <van-field input-align="right" readonly label="配送方式" value="{{detailData.backTypeName}}"></van-field>
  <van-field input-align="right" readonly label="审核时间" value="{{detailData.auditedDate}}"></van-field>
  <view class="footer-block">
    <view class="footer-amount-qty">退货数量共{{detailData.totalQty}}件</view>
    <view class="footer-amount-price">
      <text class="amount-price-label">退货合计:</text>
      <text class="amount-price-text">￥{{detailData.totalAmount}}</text>
    </view>
  </view>
</view>