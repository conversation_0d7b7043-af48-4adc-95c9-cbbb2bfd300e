// pages_sub/pages/ccs/down/salesReturn/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    queryState: '[2,5,99]',
    typeList:[
      {title:'全部',name:'[2,5,99]'},
      {title: '待审核', name: '[2]'},
      {title: '已审核', name: '[5]'},
      {title: '已关闭', name: '[99]'},
    ],
    keyword: '',
    pageIndex: 1,
    pageSize: 10,
    dataList: [],
    listViewH: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getOrderList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#tabsView').boundingClientRect()
        query.select('#filterSearch').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].bottom
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getOrderList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 加载更多事件
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getOrderList()
  },
  // 搜索框确认事件
  onSearch() {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
   // 搜索框内容修改事件
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 切换导航栏事件
  tapNavi: function (e) {
    this.setData({
      queryState: e.detail.name,
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  // 请求列表事件
  getOrderList() {
    const supInfo = wx.getStorageSync('supInfo')
    const url = '/api/psi/orderItembackAudit/myx/page'
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        billNo: this.data.keyword,
        vendorSetsOfBooksId: supInfo.setsOfBooksId,
        stateList: JSON.parse(this.data.queryState)
      }
    }
    let arr = [...this.data.dataList]
    App.getHttp()._post(url, params).then(res => {
      if(res && res.length > 0) {
        this.setData({
          dataList: arr.concat(res)
        })
      }
    })
  },
  // 审核跳转
  onClickAudit(e) {
    let billNo = e.currentTarget.dataset.no
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/salesReturnAudit/index?billNo=' + billNo
    })
  },
  onClickReject(e) {
    let item = e.currentTarget.dataset.item
    const url = '/api/psi/orderItembackAudit/myx/rejectByMyx'
    const params = { id: item.id, version: item.version }
    App.getHttp()._post(url, params).then(() => {
      wx.showToast({
        title: '关闭成功！',
        icon: 'success',
        duration: 2000,
        mask: true,
        complete: () => {
          this.setData({
            pageIndex: 1,
            dataList: []
          })
          this.getOrderList()
        }
      })
    })
  },
  // 点击块跳转
  onClickItem(e) {
    let billNo = e.currentTarget.dataset.no
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/salesReturn/detail/index?billNo=' + billNo
    })
  }
})