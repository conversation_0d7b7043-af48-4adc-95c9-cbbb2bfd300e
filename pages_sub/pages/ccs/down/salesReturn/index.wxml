<view class="root-layout bg-04">
  <view class="filter-search" id="filterSearch">
    <van-search value="{{keyword}}" bind:search="onSearch" use-action-slot bind:change="onChangeKeyword" bind:clear="onSearch" placeholder="搜索订单">\
      <view slot="action" class="search-right" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>
  <van-tabs id="tabsView" class="tab-layout" active="{{queryState}}" bind:change="tapNavi" title-active-color="#0278ff">
    <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
      <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
    </block>
  </van-tabs>
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <block wx:if="{{dataList.length > 0}}">
      <view class="listItemLayout" wx:for="{{dataList}}" wx:key="id">
        <salesReturnItem itemObj="{{item}}" isAudit="{{false}}" data-no="{{item.billNo}}" bindtap="onClickItem">
          <view class="audit-btn" slot="footer" wx:if="{{item.state == 2}}">
            <van-button size="small" data-item="{{item}}" catchtap="onClickReject">关闭</van-button>
            <van-button class="audit" type="info" plain size="small" data-no="{{item.billNo}}" catchtap="onClickAudit">审核</van-button>
          </view>
        </salesReturnItem>
      </view>
    </block>
    <view class="m-t-25p" wx:else>
      <noneView ></noneView>
    </view>
  </listView>
</view>