<!--pages/ccs/detailsG/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" id="page-layout">
<!-- 头部导航 -->
<list-head id="listHead" showSupplier="{{false}}" customTitle="商品详情"></list-head>
<!-- 热卖-新品 -->
<van-tabs active="{{ scrollTab }}" nav-class="navStyle" bind:click="onTopChangeTab">
  <van-tab title="商品" name="swiper_layout"></van-tab>
  <van-tab title="详情" name="more_layout"></van-tab>
</van-tabs>
<scroll-view  scroll-into-view="{{scrollIntoView}}" id="scroll-layout" scroll-y style="height: {{scrollViewHeightPx}}px;">
  <view class="swiper-layout" id="swiper_layout">
    <adSwiper adList="{{mainPicUrl}}" heightRpx="{{windowWidthRpx}}" indicatorActive="#00b9c3" autoHttp="{{false}}" indicatorDots="{{false}}"
      autoplay="{{true}}"  indicatorCount="{{true}}" radiusRpx="0" bind:onClickAd="onClickAdListener" bind:clickSwiperItem="onClickSwiperItem"/>
  </view>
  <view class="item-layout">
    <view class="price-box flex align-items-center justify-content-between">
      <view class="itemprice">
        <text class="uom">¥</text>
        <text class="current">{{wxsUtil.moneyFormatInt(dInfo.standardPrice,'int')}}</text>  
        <text class="rem">{{wxsUtil.moneyFormatInt(dInfo.standardPrice)}}</text>
        <text class="over" wx:if="{{dInfo.retailPrice}}">建议零售价¥{{dInfo.retailPrice || '暂无报价'}}</text>
      </view>
      <view class="stock-count">库存 {{dInfo.qtyOnhand}}</view>
    </view>
    <view class="name-box">
      <view class="itemname">{{dInfo.itemName}}</view>
      <view class="vendorname">{{dInfo.vendorName}}</view>
    </view>
  </view>
  <view class="more-layout" id="more_layout">
    <van-tabs active="{{ activeMoreValue }}"  bind:click="onMoreTabChange">
      <van-tab title="图文详情" name="2">
        <image class="pic-card" mode="widthFix" wx:for="{{desPicUrl}}" wx:key="index" src="{{item}}" lazy-load></image>
      </van-tab>
    </van-tabs>
  </view>
  <view class="placeholder-layout"></view>
</scroll-view>

  <view wx:if="{{dInfo.qtyOnhand=='无货'}}" class="none-stock-layout">该商品已无货，您可以看看别的～</view>
  <view class="foot-layout flex" hidden="{{!footlayoutAble}}">
    <view class="flexbox flex align-items-center">
      <view class="flexbox flex column align-items-center" bindtap="onClickFocus">
        <image class="img" src="{{isFocused?'/asset/imgs/focus.png':'/asset/imgs/focus-o.png'}}"></image>
        <view class="icontxt">{{isFocused?'取消':'收藏'}}</view>
      </view>
      
    </view>
    <view class="add" catchtap="onClickAdd">加入购物车</view>
    <view class="buy" catchtap="onClickFill">立即下单</view>
  </view>
  <add-carts item="{{dInfo}}" show="{{showAddCartsPop}}" bind:onClose="onCloseAddCarts"></add-carts>
  <pre-view show="{{showPreView}}" list="{{preImageUrl}}"></pre-view>
</view>