// pages/ccs/detailsG/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    scrollTab:2,
    windowWidthRpx: 375,
    scrollViewHeightPx:500,
    scrollIntoView:'swiper_layout',
    dInfo: {},
    mainPicUrl: [],
    preImageUrl: [],
    desPicUrl: [],
    isFocused: false,
    showAddCartsPop: false,
    cartCount: 0,
    footlayoutAble: true,
    showPreView: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.itemId) {
      this.iniData(options)
      this.initCollect(options.itemId)
    }
    this.getCartsCount()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#scroll-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        scrollViewHeightPx: res[0].height - res[1].top
      })
      console.log('=createSelectorQuery==',res);
    })
    wx.getSystemInfo({
      success: (result) => {
        this.setData({
          windowWidthRpx: (result.windowWidth * App.globalData.pxToRpxRatio)
        })
      },
    })
  },
  initCollect: function (itemId) {
    const params = {
      vendorCode: wx.getStorageSync('vendorInfo').bvendorCode,
      vendorSetsOfBooksId: wx.getStorageSync('vendorInfo').ssetsOfBooksId,
      invoiceCustCode: wx.getStorageSync('vendorInfo').scustCode,
      invoiceSetsOfBooksId: wx.getStorageSync('vendorInfo').bsetsOfBooksId,
      itemId: itemId,
      sourceModule:'down'//向下模块的收藏, 收藏那边要区分向上向下价格
    }
    App.getHttp()._post(`/api/mms/itemCollectRelation/myx/isCollect`, params).then(res => {
      this.setData({
        isFocused: res
      })
    })
  },
  iniData: function (param) {
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const params = {
      invoiceSetsOfBooksId: vendorInfo.bsetsOfBooksId,
      invoiceCustCode: vendorInfo.scustCode,
      vendorSetsOfBooksId: vendorInfo.ssetsOfBooksId,
      vendorCode: vendorInfo.bvendorCode,
      ...param,
    }
    App.getHttp()._post('/api/psi/myx/purOrder/getPurchaseItemDetail', params).then(res => {
      if (res) {
        const mainPicUrl = res.carouselImages ? res.carouselImages.map(v => {
          return {
            picUrl: v.url
          }
        }) : []
        const descriptionImages = res.descriptionImages ? res.descriptionImages.map(v => v.url) : []
        this.setData({
          dInfo: res.item,
          mainPicUrl: mainPicUrl,
          preImageUrl: mainPicUrl.map(mainItem => mainItem.picUrl),
          desPicUrl: descriptionImages,
        })
      }
    })
    const vendorList = wx.getStorageSync('vendorList')
    if (vendorList && vendorList.length > 0) {
      const findIndex = vendorList.findIndex(res => res.vendorCode == params.vendorCode && res.channelCode == params.channelCode )
      this.setData({
        footlayoutAble: findIndex > -1
      })
    }
  },
  onClickSwiperItem(event) {
    // wx.previewImage({
    //   current: this.data.preImageUrl[index], // 当前显示图片的http链接
    //   urls: this.data.preImageUrl // 需要预览的图片http链接列表
    // })
    this.setData({
      showPreView: true
    })
  },
  onClickShop() {
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/shop/index',
    })
  },
  onClickFocus() {
    const params = {
      vendorName: wx.getStorageSync('vendorInfo').bvendorName,
      vendorCode: wx.getStorageSync('vendorInfo').bvendorCode,
      vendorSetsOfBooksId: wx.getStorageSync('vendorInfo').ssetsOfBooksId,
      invoiceCustCode: wx.getStorageSync('vendorInfo').scustCode,
      invoiceSetsOfBooksId: wx.getStorageSync('vendorInfo').bsetsOfBooksId,
      itemId: this.data.dInfo.itemId,
      sourceModule:'down'//向下模块的收藏, 收藏那边要区分向上向下价格
    }
    if (this.data.isFocused) {
      App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect/not', params).then(res => {
        this.setData({
          isFocused: false
        })
      })
    } else {
      App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect', params).then(res => {
        wx.showToast({
          title: '已关注',
          icon:'success'
        })
        this.setData({
          isFocused: true
        })
      })
    }
  },
  onClickAdd() {
    this.setData({
      showAddCartsPop: true,
      btnType: 'add',
    })
  },
  onClickFill() {
    this.setData({
      showAddCartsPop: true,
      btnType: 'buy'
    })
  },
  onCloseAddCarts(v) {
    this.setData({
      showAddCartsPop: false
    })
    if (!v.detail) return
    const reault = v.detail
    if (this.data.btnType == 'buy') {
      const fillsInfo = [{
        channelCode: reault.channelCode, //渠道
        productCode: reault.productCode, //产品组
        saleOrgCode: reault.saleOrgCode, //销售组织
        orderTypeList: [0], // 订单类型集合
        invoiceCustId: reault.invoiceCustId, // 客户ID
        invoiceCustCode: reault.invoiceCustCode, // 客户ID
        invoiceCustName: reault.invoiceCustName, // 客户ID
        invoiceSetsOfBooksId: reault.invoiceSetsOfBooksId, // 客户ID
        vendorSetsOfBooksId: reault.vendorSetsOfBooksId, // 供应方账套ID
        vendorCode: reault.vendorCode, // 供应商ID
        vendorName: reault.vendorName, // 供应商ID
        vendorId: reault.vendorId, // 供应商ID
        total: (reault.qty * reault.standardPrice).toFixed(2),
        cartTypes: [{
          orderType: 0, //常规订单
          total: (reault.qty * reault.standardPrice).toFixed(2),
          items: [{
            standardPrice: reault.standardPrice,
            itemCode: reault.itemCode,
            itemId: reault.itemId,
            itemName: reault.itemName,
            itemUrl: reault.itemUrl,
            purchaseQty: reault.qty,
            uomId: reault.uomId,
            uomName: reault.uomName,
            qtyOnhand:reault.qtyOnhand
          }]
        }],
      }]
      wx.setStorageSync('fillsList', fillsInfo)
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/down/order/fill/index',
      })
    } else {
      const params = {
        channelCode: reault.channelCode, //渠道
        productCode: reault.productCode, //产品组
        saleOrgCode: reault.saleOrgCode, //销售组织
        orderType: 0, // 订单类型
        qtyModel: 2, // 1扣减购物车 2增加购物车 3直接填充购买数量
        invoiceCustId: reault.invoiceCustId, // 客户ID
        invoiceCustCode: reault.invoiceCustCode, // 客户ID
        invoiceSetsOfBooksId: reault.invoiceSetsOfBooksId, // 客户ID
        vendorSetsOfBooksId: reault.vendorSetsOfBooksId, // 供应方账套ID
        vendorCode: reault.vendorCode, // 供应商ID
        vendorId: reault.vendorId, // 供应商ID
        itemList: [{
          itemId: reault.itemId, // 商品ID
          itemCode: reault.itemCode, // 商品ID
          purchaseQty: reault.qty // 购买数量
        }]
      }
      App.getHttp()._post('/api/psi/shoppingCart/addToCarts', params).then(res => {
        wx.showToast({
          title: '加入成功',
          icon: 'success'
        })
        this.setData({
          cartCount: (this.data.cartCount + reault.qty)
        })
      })
    }
  },
  getCartsCount() {
    App.getCartsCount().then(res => {
      this.setData({
        cartCount: res
      })
    })
  },
  onTopChangeTab(e){
    this.setData({
      scrollIntoView:e.detail.name
    })
    console.log('=onTopChangeTab=',this.data.scrollIntoView);
  }
})