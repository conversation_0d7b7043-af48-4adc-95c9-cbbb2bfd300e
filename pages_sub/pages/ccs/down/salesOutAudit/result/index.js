// pages_sub/pages/ccs/down/order/return/returnResult/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    billNo: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.billNo) {
      this.setData({
        billNo: options.billNo
      })
    }
  },
  onClickCheckDetail(){
    // 跳转到销售出库详情 关闭所有页面
   wx.redirectTo({
     url: '/pages_sub/pages/ccs/down/salesOut/detail/index?billNo=' + this.data.billNo,
   })
  },
  onClickBack(){
    // 跳转到销售出库 关闭所有页面
    wx.redirectTo({
      url: '/pages_sub/pages/ccs/down/salesOut/index',
    })
  }
 
})