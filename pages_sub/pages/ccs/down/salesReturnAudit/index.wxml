<wxs src="./index.wxs" module="wxsUtil"></wxs>
<view class="root-layout">
  <salesReturnItem isAudit="{{true}}" showAmount="{{false}}" bind:changeAudit="changeAudit" id="confirmList" itemObj="{{detailData}}" />
  <view class="field-content">
    <van-field input-align="right" right-icon="arrow" placeholder="请选择仓库" label="入库仓库" value="{{warehouseName}}" data-item-id="{{detailData.id}}" border="{{false}}" bind:click-icon="checkkWarehouse" bind:click-input="checkkWarehouse" />
  </view>
</view>
<view class="footer">
  <view class="footer-left">
    <view class="totalCount">共{{wxsUtil.getTotalCount(confirmLineList)}}件</view>
    <view class="totalPrice row-layout">
      <view class="price-label">合计:</view>
      <view class="row-layout price-content">
        <view class="price-symbol">￥</view>
        <view class="price-text">{{wxsUtil.getTotalPrice(confirmLineList)}}</view>
      </view>
    </view>
  </view>
  <view class="footer-btn" wx:if="{{detailData.state == 2}}" bind:tap="onClickConfirm">确认退货</view>
</view>
<van-popup show="{{ show }}" round position="bottom" custom-style="height: 60%" bind:close="onClose">
  <van-picker columns="{{ warehouseColumns }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
</van-popup>