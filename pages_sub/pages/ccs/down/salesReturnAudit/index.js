// pages_sub/pages/ccs/down/salesReturnAudit/index.js
const App = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    billNo: "",
    show: false,
    warehouseColumns: [],
    detailData: {},
    confirmLineList: [],
    warehouseName: '',
    warehouseId: '',
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      billNo: options.billNo,
    });
    this.getOderDetail();
    this.initWarehouseColumns()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() { },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() { },
  // 获取单个单据号信息
  getOderDetail() {
    const url = "/api/psi/orderItembackAudit/myx/page";
    const params = {
      pageIndex: 1,
      pageSize: 1,
      param: {
        billNo: this.data.billNo,
      },
    };
    App.getHttp()
      ._post(url, params)
      .then((res) => {
        if (res && res.length > 0) {
          this.setData({
            detailData: res[0],
            confirmLineList: res[0].addLineList.map((item) => {
              return {
                id: item.id,
                auditPrice: item.applyPrice,
                auditQtyBill: item.applyQty,
                auditAmount: ''
              };
            }),
          });
        }
      });
  },
  // 获取仓库列表
  initWarehouseColumns() {
    // 选择仓库
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          text: res[r].name,
          id: res[r].id
        })
        if (res[r].isDefault === 2) {
          this.setData({
            warehouseId: res[r].id,
            warehouseName: res[r].name,
          })
        }
      }
      this.setData({
        warehouseColumns: warehouseColumns
      })
    })
  },

  checkkWarehouse(e) {
    // 选择仓库
    this.setData({
      show: true,
    })
  },
  // 确认选择仓库
  onConfirmWarehouse(event) {
    let detail = event.detail.value
    if (detail.id) {
      this.setData({
        show: false,
        warehouseName: detail.text,
        warehouseId: detail.id
      })
    }
  },
  // 关闭选择仓库
  onCancelWarehouse() {
    this.setData({
      show: false
    })
  },
  // 修改审批数量回调
  changeAudit(e) {
    let confirmList = [...e.detail]
    this.setData({
      confirmLineList: confirmList
    })
  },
  // 确认审核
  onClickConfirm() {
    let bool = this.data.confirmLineList.some(item => item.auditQtyBill === 0)
    if (bool) {
      wx.showToast({
        title: '审批数量不能为0',
        duration: 1000,
        icon: 'none'
      })
      return
    }
    const url = "/api/psi/orderItembackAudit/myx/auditByMyx";
    const params = {
      id: this.data.detailData.id,
      backWarehouseId: this.data.warehouseId,
      version: this.data.detailData.version,
      addLineList: this.data.confirmLineList.map((item) => {
        return {
          id: item.id,
          auditPrice: item.auditPrice,
          auditQtyBill: item.auditQtyBill,
          auditAmount: item.auditPrice * item.auditQtyBill,
        };
      }),
    };
    App.getHttp()
      ._post(url, params)
      .then((res) => {
        wx.redirectTo({
          url: '/pages_sub/pages/ccs/down/salesReturnAudit/result/index?billNo=' + this.data.detailData.billNo
        })
      });
  },
});
