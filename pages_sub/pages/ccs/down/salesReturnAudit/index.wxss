page {
  background-color: rgba(0,0,0,0.04);
}
.root-layout {
  padding-bottom: 132rpx;
}
.field-content {
  margin: 0 24rpx;
}
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 32rpx 32rpx 68rpx 32rpx;
  position: fixed;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
}
.footer-btn {
  background: #00b9c3;
  border-radius: 8rpx;
  padding: 14rpx 32rpx;
  text-align: center;
  color: #fff;
  line-height: 36rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
}
.footer-left {
  display: flex;
  flex-direction: column;
}
.totalCount {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 24rpx;
  margin-bottom: 8rpx;
}
.row-layout {
  display: flex;
  align-items: center;
}
.totalPrice {
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #3D3D3D;
  line-height: 24rpx;
}
.price-label {
  font-size: 24rpx;
}
.price-content {
  font-size: 32rpx;
}
