var getTotalCount = function (value) {
  var totalCount = 0;
  if(value.length > 0) {
    for(var i = 0; i< value.length; i++) {
        totalCount += Number(value[i].auditQtyBill || 0)
    }
  }
  return totalCount;
};
var getTotalPrice = function (value) {
    var totalPrice = 0;
    if(value.length > 0) {
      for(var i = 0; i< value.length; i++) {
        var count = value[i].auditQtyBill || 0
        var price = value[i].auditPrice
        totalPrice += (price * count)
      }
    }
    return totalPrice.toFixed(2);
  };

module.exports = {
  getTotalCount: getTotalCount,
  getTotalPrice: getTotalPrice
};
