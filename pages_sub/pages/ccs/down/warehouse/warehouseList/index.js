// pages_sub/pages/ccs/down/warehouse/warehouseList/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listData: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      listData:[{
        warehouseName:'仓库1',
        propName: '仓库1a',
        warehouseCode: 'sdfas',
        warehouseId: '546565',
        manageImage: '/asset/imgs/backhome.png'
      },{
        warehouseName:'仓库2',
        propName: '仓库1b',
        warehouseCode: 'sdfah',
        warehouseId: '546566',
        manageImage: '/asset/imgs/backhome.png'
      },{
        warehouseName:'仓库3sdfasdfasdfsdfasd',
        propName: '仓库1c',
        warehouseCode: 'sdfat',
        warehouseId: '546567',
        manageImage: '/asset/imgs/backhome.png'
      }]
    })
  },

  /* 仓库编辑的事件 */
  jumpEdit(){
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/warehouse/warehouseEdit/index',
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})