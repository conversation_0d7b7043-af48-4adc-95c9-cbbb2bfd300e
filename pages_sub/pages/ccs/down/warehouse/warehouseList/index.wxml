<!--pages_sub/pages/ccs/down/warehouse/warehouseList/index.wxml-->

<view class="page warehouse-manage">
  <view
    wx:for="{{listData}}"
    wx:key="warehouseId"
    data-item="{{item}}"
    class="item-layout"
  >
    <view class="item-layout_top flex">
      <image
        class="image"
        src="{{item.manageImage}}"
        mode='scaleToFill'
      />
      <view class="txtbox">
        <view class="flex">
          <view class="title">
            {{ item.warehouseName }}
          </view>
          <view class="type">
            {{ item.propName }}
          </view>
        </view>
        <view class="des">
          仓库编码：{{ item.warehouseCode }}
        </view>
      </view>
    </view>
    <view class="item-layout_bottom flex justify-content-end">
      <view
        class="flex align-items-center"
        bindtap="jumpEdit"
      >
        <image
          class="image"
          src="/asset/svgs/editor.svg"
          mode='scaleToFill'
        />
        <span class="hint">编辑</span>
      </view>
    </view>
  </view>
</view>
