/* pages_sub/pages/ccs/down/warehouse/warehouseList/index.wxss */

.warehouse-manage{
  background: rgba(0, 0, 0, 0.04);
}
.item-layout {
  margin: 0;
  background: #fff;
}
.item-layout_top .image {
  width: 64rpx;
  height: 64rpx;
  border: 2rpx;
  position: relative;
}
.item-layout_top .image::after {
  position: absolute;
  top: 12rpx;
  right: -24rpx;
  display: block;
  width: 2rpx;
  height: 48rpx;
  content: ' ';
  background-color: #EBEBF5;
}
.item-layout_top .txtbox {
  flex: 1;
  margin-left: 48rpx;
}
.item-layout_top .title {
  font-size: 28rpx;
  line-height: 44rpx;
  color: rgba(0,0,0,0.85);
}

.item-layout_top .type {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 40rpx;
  padding: 0 8rpx;
  margin-left: 24rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: #f97d4e;
  border: 2rpx solid #f97d4e;
  border-radius: 4rpx;
}

.item-layout_top .des {
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: rgba(0,0,0,0.45);
}

.item-layout_top {
  position: relative;
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
}
.item-layout_top::after {
  position: absolute;
  bottom: 0;
  left: 0;
  display: block;
  width: 100%;
  content: ' ';
  border-bottom: 2rpx solid #EBEBF5;
  transform: scaleY(0.5);
}
.item-layout_bottom {
  padding: 16rpx 24rpx;
}
.item-layout_bottom .image {
  width: 48rpx;
  height: 48rpx;
}
.item-layout_bottom .hint {
  margin-left: 4px;
  font-size: 12px;
  line-height: 20px;
  color: rgba(0,0,0,0.45);
}
.item-layout_bottom .ml24 {
  margin-left: 28rpx;
}

.item-layout:nth-of-type(n+2) {
  margin-top: 16rpx;
}



.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.justify-content-end {
  justify-content: flex-end;
}
/* 垂直 */
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}

