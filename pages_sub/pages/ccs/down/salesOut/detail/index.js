// pages_sub/pages/ccs/down/salesOut/detail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    billNo: '',
    detailData: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if(options.billNo) {
      this.setData({
        billNo: options.billNo
      })
      this.getOderDetail()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取单个单据号信息
  getOderDetail() {
    const url = "/api/psi/saleOutBill/myx/page";
    const params = {
      pageIndex: 1,
      pageSize: 1,
      param: {
        billNo: this.data.billNo,
        isConfirm: "",
      },
    };
    App.getHttp()
      ._post(url, params)
      .then((res) => {
        if (res && res.length > 0) {
          this.setData({
            detailData: res[0]
          });
        }
      });
  },
  // 复制单号
  onClickCopy(e) {
    const no = e.currentTarget.dataset.no
    wx.setClipboardData({
      data: no,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制单号成功'
        })
      }
    })
  }
})