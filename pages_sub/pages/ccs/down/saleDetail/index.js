// pages/ccs/orderDetail/index.js
const App=getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    stat:'',
    id:'',
    detailInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id){
      let stat = '待审核'
      if(options.stat === 2) {
        stat = '待审核'
      } else if(options.stat === 3) {
        stat = '已审核'
      } else if(options.stat === 5) {
        stat = '已关闭'
      } else {
        stat = ''
      }
      this.setData({
        stat
      })
      this.data.id = options.id
      this.getOrderDetail()
    }
  },
  getOrderDetail(){
    App.getHttp()._post('/api/psi/myx/purOrder/state/getStateDetail',{id:this.data.id}).then(res=>{
      console.log(res, 'resdetail')
      this.setData({
        detailInfo:res
      })
    })
  },
  onClickTodo(e){
    if(e.currentTarget.dataset.type==='delivered') {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/down/order/outStock/index?id='+this.data.id+'&type='+e.currentTarget.dataset.type+'&sourceSystem='+this.data.detailInfo.sourceSystem,
      })
    } else {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/down/order/question/index?id='+this.data.id+'&type='+e.currentTarget.dataset.type+'&sourceSystem='+this.data.detailInfo.sourceSystem,
      })
    }
  },
  onClickOnceAgain(e){
    App.getHttp()._get(`/myx/ccs-mobile-web/order/copyOrderItemsToCart/${this.data.detailInfo.mergeNo}`).then(res=>{
      if(res){
        wx.showToast({
          title: '添加成功',
          icon:'success'
        })
      }
    })
  },
  // 复制单号
  onClickCopy() {
    if(!this.data.detailInfo.orderHead.poNo) {
      return
    }
    wx.setClipboardData({
      data: this.data.detailInfo.orderHead.poNo,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制单号成功'
        })
      }
    })
  },
  // 关闭订单
  onClickClose() {
    const url = '/api/psi/myx/purOrder/order/close'
    const params = {
      id: this.data.detailInfo.orderHead.id
    }
    wx.showModal({
      title: '确定要关闭订单吗?',
      content: '',
      success: (resM) => {
        if (resM.confirm) {
          App.getHttp()._post(url, params).then(res => {
            wx.navigateBack({
              delta: 1
            })
          })
        }
      }
    })
  }
})