<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<view class="root-layout">
    <view class="head-layout flex">
        <view class="bill-state">{{stat ? stat : detailInfo.orderHead.queryStatName}}</view>
        <view class="bill-no flex align-items-center">
            <view>订单号:</view>
            <view class="no-text">{{detailInfo.orderHead.poNo}}</view>
            <van-icon size="12" bindtap="onClickCopy" data-no="{{detailData.billNo}}" name="/asset/imgs/purchase/copy.png"></van-icon>
        </view>
    </view>
    <detail-address orderAddr="{{detailInfo.orderAddr}}"/>
    <block wx:if="{{detailInfo.orderLines && detailInfo.orderLines.length > 0}}">
        <view class="good-layout" wx:for="{{detailInfo.orderLines}}" wx:key="index">
            <view class="good-item">
                <image class="good-img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load></image>
                <view class="good-info">
                    <view class="info-middle">
                        <view class="good-name-box">
                          <view class="name">{{item.itemName}}</view>
                          <view class="specs">{{item.specs}}</view>
                        </view>
                        <!-- 1满减 2满折 3一口价 4满赠 5众筹 -->
                        <!-- 满减 满折 满赠才显示配比数 -->
                        <view class="price-qty" wx:if="{{item.proportioningQty && (detailInfo.orderHead.orderType == 1 || detailInfo.orderHead.orderType == 2 || detailInfo.orderHead.orderType == 4)}}">配比数 {{item.proportioningQty}}</view>
                    </view>
                    <view class="good-price">
                        <view class="price-text">￥{{item.applyPrice}}</view>
                        <view class="price-qty">共{{item.applyQty}}件</view>
                    </view>
                </view>
            </view>
            <view class="qty-layout price-qty">
                <view class="qty-item">取消数量{{item.cancelQty}}件</view>
                <view class="qty-item">待审核{{item.toAuditQty}}件</view>
                <view class="qty-item">已审核{{item.auditQty}}件</view>
            </view>
            <view class="total-block">
                <text class="total-label">合计:</text>
                <text class="total-text">￥{{stat == '待审核' || (detailInfo.orderHead.state == 2 && !stat) ? item.applyPrice * item.toAuditQty : item.applyPrice * item.auditQty}}</text>
            </view>
        </view>
    </block>
    <van-field input-align="right" readonly label="创建时间" value="{{detailInfo.orderHead.createTime}}"></van-field>
    <van-field input-align="right" readonly label="配送方式" value="{{detailInfo.orderHead.shipModeName}}"></van-field>
    <van-field input-align="right" readonly label="审核时间" value="{{detailInfo.orderHead.auditedDate}}"></van-field>
    <view class="footer-block">
        <!-- 待审核的情况 -->
        <view class="footer-right">
            <block wx:if="{{stat ? stat == '待审核' : detailInfo.orderHead.state == 2}}">
            <view class="footer-amount-qty">待审核共{{detailInfo.orderHead.totalToAuditQty}}件</view>
            <view class="footer-amount-price">
                <text class="amount-price-label">待审核合计:</text>
                <text class="amount-price-text">￥{{detailInfo.orderHead.totalToAuditAmount}}</text>
            </view>
        </block>
        <block wx:else>
            <block wx:if="{{detailInfo.orderHead.isCompleted == 2}}">
                <view class="footer-amount-qty">已审核共{{detailInfo.orderHead.totalSignedQty}}件</view>
                <view class="footer-amount-price">
                    <text class="amount-price-label">审核合计:</text>
                    <text class="amount-price-text">￥{{detailInfo.orderHead.totalSignedAmount}}</text>
                </view>
            </block>
            <block wx:else>
                <view class="footer-amount-qty">已审核共{{detailInfo.orderHead.totalToDeliveryQty == 0 ? detailInfo.orderHead.totalToSignedQty : detailInfo.orderHead.totalToDeliveryQty}}件</view>
                <view class="footer-amount-price">
                    <text class="amount-price-label">审核合计:</text>
                    <text class="amount-price-text">￥{{detailInfo.orderHead.totalToDeliveryAmount  || detailInfo.orderHead.totalToSignedAmount}}</text>
                </view>
            </block>
        </block>
        </view>
          
    </view>
</view>