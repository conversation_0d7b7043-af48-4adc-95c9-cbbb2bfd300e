<view class="root-layout bg-04">
    <view class="filter-search" id="filterSearch">
        <van-search value="{{keyword}}" bind:search="onSearch" bind:change="onChangeKeyword" use-action-slot bind:clear="onSearch" placeholder="搜索订单" >
            <view slot="action" class="search-right" bind:tap="onSearch">搜索</view>
        </van-search>
    </view>
    <van-tabs id="tabsView" class="tab-layout" active="{{queryState}}" bind:change="tapNavi" title-active-color="#0278ff">
        <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
            <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
        </block>
    </van-tabs>
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
        <block wx:if="{{dataList.length > 0}}">
            <goodsCardNew wx:for="{{dataList}}" bind:clickOrder="onClickItem" data-id="{{item.id}}" showAllInfo="{{false}}" headTitle="申请单号" goodData="{{item}}"  showExpandBlock="{{false}}"   dataType="{{2}}" wx:key="index" />
        </block>
        <view class="m-t-25p" wx:else>
          <noneView ></noneView>
        </view>
    </listView>
</view>