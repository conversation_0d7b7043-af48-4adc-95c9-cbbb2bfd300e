/* pages/ccs/orderDetail/index.wxss */
.root-layout {
  background-color: rgba(0, 0, 0, 0.04);
  padding-bottom: 100rpx;
}
.item-layout {
  margin: 24rpx;
}
.address {
  padding: 24rpx 32rpx;
  background-color: #fff;
}
.address-content {
  flex-direction: column;
  align-items: flex-end;
  margin: 0 16rpx;
}
.address-label {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 44rpx;
}
.return-price-class {
  color: #FF4A4D!important;
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}
.foot-layout {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  padding: 0 24rpx;
  background-color: #ffffff;
  z-index: 99;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}
.cancel {
  background: #00b9c3;
  border-radius: 8rpx;
  padding: 16rpx 0;
  text-align: center;
  color: #fff;
  width: 100%;
}
