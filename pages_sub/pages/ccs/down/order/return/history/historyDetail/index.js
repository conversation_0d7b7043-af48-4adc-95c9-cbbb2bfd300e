// pages_sub/pages/ccs/down/order/return/history/history-detail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    stat:'',
    id:'',
    detailInfo: {
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if(options.id) {
      this.setData({
        id: options.id
      })
    }
    this.getOrderDetail()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 查询订单详情
  getOrderDetail() {
    const url = '/api/psi/orderItembackAudit/' + this.data.id
    App.getHttp()._get(url).then(res=>{
      res.orderLines = res.addLineList
      this.setData({
        detailInfo:res
      })
    })
  },
  // 退货关闭
  onClickClose() {
    // const url = "/api/psi/orderItembackAudit/myx/rejectByMyx"
    const url = '/api/psi/orderItemback/myx/cancalByMyx'
    const params = {
      id: this.data.id,
      // version: this.data.detailInfo.version
      flowCallBack: true
    }
    App.getHttp()._post(url, params).then(res => {
      wx.showToast({
        title: "操作成功",
        duration: 2000,
        icon: "success",
      });
      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
        });
      }, 2000);
    })
  },
  // 复制单号
  onClickCopy(e) {
    const no = e.currentTarget.dataset.no
    wx.setClipboardData({
      data: no,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制成功',
          image: '/asset/imgs/purchase/add-success.png'
        })
      }
    })
  }
})