<!-- pages/ccs/orderDetail/index.wxml -->
<view class="page-full root-layout">
  <view class="stat-layout">
    <van-field label="销售方" value="{{detailInfo.vendorName}}" input-align="right" readonly></van-field>
    <van-field label="退货仓库" value="{{detailInfo.warehouseName}}" input-align="right" readonly></van-field>
    <van-field label="运送方式" value="{{detailInfo.backTypeName}}" input-align="right" readonly></van-field>
    <view class="address flex align-items-center" bindtap="onClickAddress">
      <van-icon size="20" style="align-self: flex-start" name="/asset/imgs/purchase/location.png"></van-icon>
      <view class="address-content flex flexbox">
        <view class="address-text">{{detailInfo.toContactAddress}}</view>
        <view class="address-label">
          {{detailInfo.fromContactName}}  {{detailInfo.fromContactNumber}}
        </view>
      </view>
    </view>
  </view>
  <goodsCardNew showAllInfo="{{false}}" headTitle="申请单号" showState="{{false}}" showExpandBlock="{{false}}" showReturnQty="{{true}}" goodData="{{detailInfo}}" dataType="{{2}}"></goodsCardNew>
  <van-cell-group class="item-layout m-t-16" border="{{false}}">
    <van-cell title="申请退款总额" value="{{'￥'+detailInfo.totalAmount}}" border="{{false}}" value-class="return-price-class" />
  </van-cell-group>
  <van-cell-group class="item-layout m-t-16" border="{{false}}">
    <van-field value="{{detailInfo.billNo}}" label="订单号" use-button-slot readonly input-align="right" >
      <view slot="button" class="copy" data-no="{{detailInfo.billNo}}" bindtap="onClickCopy">
        |  复制
      </view>
    </van-field>
    <van-cell title="申请时间" value="{{detailInfo.createTime}}" border="{{false}}" custom-class="cell-value-class" />
    <van-cell title="审核时间" value="{{detailInfo.auditedDate}}" border="{{false}}" custom-class="cell-value-class" wx:if="{{detailInfo.state == 5}}" />
    <van-cell title="取消时间" value="{{detailInfo.updateTime}}" border="{{false}}" custom-class="cell-value-class" wx:if="{{detailInfo.state == 99}}" />
  </van-cell-group>
  <view class="foot-layout flex align-items-center justify-content-end" wx:if="{{detailInfo.state == 2}}">
    <view class="cancel" bindtap="onClickClose">取消退货单</view>
  </view>
</view>