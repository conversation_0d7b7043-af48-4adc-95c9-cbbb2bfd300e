// pages_sub/pages/ccs/down/order/return/history/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    queryState: '1',
    typeList: [
      { title:'全部',name:'1' },
      { title: '待审核', name: '2' },
      { title: '已审核', name: '5' },
      { title: '已关闭', name: '99' }
    ],
    pageIndex: 1,
    pageSize: 10,
    dataList: [],
    keyword: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#tabsView').boundingClientRect()
        query.select('#filterSearch').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].bottom
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 初始化数据
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getHistoryList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getHistoryList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  // 加载更多事件
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getHistoryList()
  },
  // 获取退货申请记录列表
  getHistoryList() {
    const url = '/api/psi/orderItembackAudit/myx/page'
    const supInfo = wx.getStorageSync('supInfo')
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        invoiceSetsOfBooksId: supInfo.setsOfBooksId,
        stateList: this.data.queryState === '1' ? [2, 5, 99] : [this.data.queryState],
        billNo: this.data.keyword
      }
    }
    App.getHttp()._post(url, params).then(res => {
      let dataList = this.data.pageIndex === 1 ? [] : this.data.dataList
      if(res && res.length > 0) {
        this.setData({
          dataList: dataList.concat(res)
        })
      } else {
        this.setData({
          dataList
        })
      }
    })
  },
  // 切换导航栏
  tapNavi(e) {
    this.setData({
      queryState: e.detail.name,
      pageIndex: 1,
      dataList: []
    })
    this.getHistoryList()
  },
  // 跳转详情
  onClickItem(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/order/return/history/historyDetail/index?id=' + id
    })
  },
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 确认搜索事件
  onSearch() {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getHistoryList()
  },
})