/* pages_sub/pages/ccs/down/order/return/selectGoods/index.wxss */
.page-helper{
  /* padding-bottom: calc(constant(safe-area-inset-bottom) + 48px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 48px); */
  background-color: rgba(0,0,0,0.04);
}
.top-layout{
  padding: 0 24rpx;
  /* height: 160rpx; */
  background-color: #FFFFFF;
}
.top-layout-search {
  width: 100%;
}
.padbott{
  padding-bottom: 32rpx;
}
.adapter-layout {
  background-color: #FFFFFF;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
  border-bottom: 1px solid #f7f8fa;
}
.adapter-layout:after{
  border-bottom-width: 16rpx;
  border-bottom-color:rgb(245, 245, 245)
}
.good-layout{
  /* height: 240rpx; */
  box-sizing: border-box;
  padding: 24rpx;
}
.good-layout .img{
  width: 176rpx;
  height: 176rpx;
  margin: 0 16rpx 10rpx;
  border-radius: 8rpx;
}
.good-layout .item-name-box{
 
  flex: 1;
}
.good-layout .item-name-box .name{
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 400;
}
.good-layout .item-name-box .specs{
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #8a8a8a;
  line-height: 44rpx;
  font-weight: 400;
}
.good-layout .itemprice-box{
  height: 52rpx;
  margin-top: 10rpx;
}
.good-layout .itemprice{
  font-size: 36rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FF4A4D;
  font-size: 32rpx;
}
.good-layout .uom{
  margin-right: 4rpx;
  font-family: SanFranciscoText-Semibold;
  font-size: 20rpx;
}
.radio-img{
  width: 40rpx;
  height: 40rpx;
  position: relative;
}
.foot-layout{
  position: fixed;
  right: 0;
  left: 0;
  /* bottom: calc(constant(safe-area-inset-bottom) + 48px);
  bottom: calc(env(safe-area-inset-bottom) + 48px); */
  bottom: 0;
  padding: 0 24rpx;
  height: 100rpx;
  background-color: #FFFFFF;
}
.bottom-button-container {
  /* display: flex; */
  padding: 0px 32rpx;
  background-color: #fff;
  border-top: 1px solid #f3f4f6;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.bottom-button {
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  padding: 16rpx 130rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 48rpx;
}
.footer-confirm {
  background: #00b9c3;
  border: none;
  color: #fff;
}
.search-right {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
  margin: 0 24rpx;
}
.list-layout {
  margin: 24rpx;
  border-radius: 8rpx;
}
.good-right {
  height: 176rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
/* 重写vant样式 */
.van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}