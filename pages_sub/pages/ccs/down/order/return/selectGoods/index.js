// pages_sub/pages/ccs/down/order/return/selectGoods/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH: 300,
    warehouseId: "",
    warehouseName: "",
    ssetsOfBooksId: '',
    list: [],
    pageIndex: 1,
    pageSize: 10,
    keyword: '',
    modeUrl:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // wx.removeStorageSync('returnGoods')
    wx.setNavigationBarTitle({
      title: options.warehouseName
    })
    this.setData({
      warehouseId: options.warehouseId,
      warehouseName: options.warehouseName,
      modeUrl:options.modeUrl
    })
    this.getList()
  },
  // ------------------------------methods------------------------------
  setPageSize() {
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.select('#foot-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH: res[1] ? res[0].height - res[1].height - res[2].height : res[0].height
      })
    })
  },
  // 查询商品列表
  getList() {
    const url = this.data.modeUrl&&this.data.modeUrl==='currentInv'?'/api/psi/currentInv/selectItemPrice':'/api/psi/currentInv/myx/warehouse/itemInvPrice'
    let saleItemInfo = wx.getStorageSync('saleItemInfo')
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        warehouseId: this.data.warehouseId,
        vendorSetsOfBooksId: saleItemInfo.ssetsOfBooksId,
        // vendorSetsOfBooksId: 46,
        keyword: this.data.keyword
      }
    }
    App.getHttp()._post(url,params).then(res=>{
      if(res&&res.length>0){
        let list = this.data.pageIndex === 1 ? [] : this.data.list
        this.setData({
          list: list.concat(res)
        })
        this.setPageSize()
      }
    })
  },
  // 上拉刷新
  onPullRefresh() {
    this.setData({
      pageIndex: 1,
      list:[]
    })
    this.getList()
  },
  // 滚动懒加载
  loadmore() {
    if (this.data.list.length%this.data.pageSize > 0) {
      // 处理已加载全部数据
      wx.showToast({
        title: '已加载全部商品',
      })
      return
    }
    this.setData({
      pageIndex: this.data.pageIndex + 1
    })
    this.getList()
  },
  // 选中商品事件
  onClickChecked(e) {
    // gIndex商品角标
    const dataset = e.currentTarget.dataset
    const gIndex = dataset.gIndex
    let checked = false
    const groupInfo = this.data.list[gIndex]
    checked = groupInfo.checked
    this.data.list[gIndex].checked = !checked
    this.setData({
      list: this.data.list
    })
  },
  submitNext() {
    let returnGoods = []
    let list = this.data.list
    let isHasFlag = false
    for (let l = 0; l < list.length; l++) {
      if (list[l].checked) {
        returnGoods.push(list[l])
      }
    }
    const curReturnGoods = wx.getStorageSync('returnGoods')
    for (let l = 0; l < curReturnGoods.length; l++) {
      if (returnGoods.find(item => item.itemId === curReturnGoods[l].itemId)) {
        isHasFlag = true
        wx.showToast({
          title: `请勿重复添加商品${curReturnGoods[l].itemName}`,
          icon: 'none'
        })
        break
      }
    }
    if(isHasFlag) {
      return false
    }
    if (!(returnGoods.length > 0)) {
      wx.showToast({
        title: '请先选择商品',
        icon: 'error'
      })
      return
    }
    const confirmGoods = curReturnGoods ? curReturnGoods.concat(returnGoods) : [].concat(returnGoods)
    wx.setStorageSync('returnGoods', confirmGoods)
    wx.navigateBack({
      delta: 1
    })
  },
  // 输入框改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 确认搜索事件
  onClickSearch() {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    let keyword = this.data.keyword
    this.getList()
  },
  onClickCancel() {
    wx.navigateBack({
      delta: 1
    })
  }
})