<!--pages_sub/pages/ccs/down/order/return/selectGoods/index.wxml-->
<!--pages_sub/pages/ccs/down/shop/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil" />
<view class="page page-helper" id="page-layout">
  <view class="top-layout" id="top-layout">
    <van-search value="{{ keyword }}" placeholder="请输入搜索关键词" use-action-slot bind:change="onChangeKeyword" bind:search="onClickSearch">
      <view slot="action" class="search-right" bind:tap="onClickSearch">搜索</view>
    </van-search>
  </view>
  <listView class="list-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullRefresh" bind:loadmore="loadmore">
    <view class="item-layout padbott" wx:if="{{list.length>0}}">
      <view class="adapter-layout" wx:for="{{list}}" wx:for-item="gItem" wx:key="gIndex" wx:for-index="gIndex">
        <view>
          <view class="good-layout flex align-items-center" id="{{(gItem.isOnSale == 1 || typeItem.usable == 1) ? 'disabled':''}}">
            <view class="radio-img">
              <image class="radio-img" src="{{gItem.checked?'/asset/imgs/purchase/circle-success.png':'/asset/imgs/purchase/circle-uncheck.png'}}" catchtap="onClickChecked" data-g-index="{{gIndex}}" />
            </view>
            <image data-gItem="{{gItem}}" data-typeItem="{{typeItem}}" catchtap="{{typeItem.orderType != 0 ? 'goPolicyPage':'previewImage'}}" class="img" mode="aspectFit" src="{{gItem.itemUrl|| '/asset/imgs/default.jpg'}}" />
            <view class="flexbox good-right">
              <view class="item-name-box">
                <view class="name two-line-ellipsis">{{gItem.itemName}}</view>
                <view class="specs single-line-ellipsis">{{gItem.specs}}</view>
              </view>
              <view class="itemprice-box flex align-items-center justify-content-between">
                <block>
                  <view class="itemprice" wx:if="{{gItem.price || gItem.standardPrice}}">
                    <text  class="uom">¥</text>
                    <text>{{gItem.price||gItem.standardPrice}}</text>
                  </view>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="m-t-25p" wx:else>
      <no-product noneTxt="暂无数据" />
    </view>
  </listView>
  <view wx:if="{{list.length>0}}" class="foot-layout bottom-button-container" id="foot-layout">
    <view class="bottom-button footer-cancel" bind:tap="onClickCancel">取消</view>
    <view class="bottom-button footer-confirm" bind:tap="submitNext">确定</view>
  </view>
</view>