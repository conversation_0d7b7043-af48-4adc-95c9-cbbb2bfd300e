// pages_sub/pages/ccs/down/order/return/index.js
const App = getApp()
import Dialog from "@vant/weapp/dialog/dialog";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    supplierOptions: [], // 销售方数据源
    supplierValue: undefined, // 选择的销售方
    supplierName: '请选择',
    warehouseOptions: [], // 仓库数据源
    warehouseId: undefined, // 选择的仓库
    warehouseName: '请选择',
    show: false,
    pickerColumns: [],
    selectedKey: '',
    selectedName: '',
    shipModeValue: '',
    shipMode: [],
    addrInfo: {}, // 地址信息
    goodsList: [], // 选择的商品数据
    listViewH: 300,
    windowPaddingTop: 120,
    rectTop: 96,
    rectHeight: 64
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.removeStorageSync('saleItemInfo')
    wx.removeStorageSync('returnGoods')
    wx.removeStorageSync('address')
    let shipMode = wx.getStorageSync('dictMap').shipMode // 发货方式数据
    this.setData({
      shipMode,
      shipModeValue:'5',//默认自提
    })
    this.setPageSize()
    this.getSupplierList() // 加销售方，与供应方相同数据源
    this.getWarehouseList() // 加载仓库数据源
    this.getDefaultAddress() // 获取默认地址
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const selectAddr = wx.getStorageSync('address')
    const returnGoods = wx.getStorageSync('returnGoods')
    if (selectAddr) {
      this.setData({
        addrInfo: selectAddr
      })
    }
    if(returnGoods) {
      this.setData({
        goodsList: returnGoods.map(item => {
          return {
            ...item,
            applyPrice: item.price || 0,
            applyQty: 0
          }
        })
      })
    }
    this.setPageSize()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },
  setPageSize() {
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#head-layout').boundingClientRect()
    query.select('#page-detail').boundingClientRect()
    query.select('#bottom-button-container').boundingClientRect()
    const rect = wx.getMenuButtonBoundingClientRect()
    query.exec((res) => {
      let windowHeight = ''
      wx.getSystemInfo({
        success(res) {
          windowHeight = res.screenHeight
        }
      })
      this.setData({
        rectTop: rect.top * App.globalData.pxToRpxRatio,
        rectHeight: rect.height * App.globalData.pxToRpxRatio,
        windowPaddingTop: (rect.top + rect.height  + 6) * App.globalData.pxToRpxRatio,
        listViewH: res[2] ? windowHeight - res[1].height - res[2].height - rect.top - 12 : windowHeight - res[1].height - rect.top
      })
    })
  },
  // 获取供应商数据
  async getSupplierList() {
    const res = wx.getStorageSync('custInfo').bsRelates
    let supplierOptions = this.data.supplierOptions
    if (res && res.length > 0) {
      supplierOptions = res
      for (let s = 0; s < supplierOptions.length; s++) {
        supplierOptions[s].text = supplierOptions[s].bvendorName
        supplierOptions[s].id = supplierOptions[s].bvendorId
      }
      this.setData({
        supplierOptions,
        supplierValue: supplierOptions[0].bvendorId, // 选择的销售方
        supplierName: supplierOptions[0].bvendorName,
      })
      wx.setStorageSync('saleItemInfo', supplierOptions[0])
    } else {
      // 未获取到供应商
      this.setData({
        supplierOptions: []
      })
    }
  },
  async getWarehouseList() {
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex:1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          text: res[r].name,
          id: res[r].id
        })
        if(res[r].isDefault ===2){
          this.setData({
            warehouseId: res[r].id,
            warehouseName: res[r].name,
          })
        }
      }
      this.setData({
        warehouseOptions: warehouseColumns
      })
    })
  },
  onClickTodo(e) {
    let type = e.currentTarget.dataset.type
    let value = e.currentTarget.dataset.value
    let name = e.currentTarget.dataset.name
    this.setData({
      show: true,
      pickerColumns: this.data[type],
      selectedKey: value,
      selectedName: name
    })
  },
  onCancel() {
    this.setData({
      show: false,
      pickerColumns: []
    })
  },
  onConfirm(event) {
    let detail = event.detail.value
    let value = this.data.selectedKey
    let name = this.data.selectedName
    this.setData({
      show: false,
      [name]: detail.text,
      [value]: detail.id
    })
    if (value === 'supplierValue') {
      wx.setStorageSync('saleItemInfo', detail)
    }
  },
  checkHistory() {
    // 跳转查看退货记录
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/order/return/history/index',
    })
  },
  // 改变运送方式
  onChangeShip(e) {
    this.setData({
      shipModeValue: e.detail
    })
  },
  getDefaultAddress: function () {
    App.getHttp()._post('/api/mmd/baseCustAddr/myx/page', {
      pageIndex: 1,
      pageSize: 1,
      param: { isDefault: 2, isUsable: 2 ,sourceId: wx.getStorageSync('custInfo').custId}
    }).then(res => {
      if (res && res.length > 0) {
        this.setData({
          addrInfo: res[0]
        })
      }
    })
  },
  // 跳转选择地址
  onClickAddress() {
    wx.navigateTo({
      url: `/pages/ccs/moreFeatures/addr/index?action=order&title=发货地址`,
    })
  },
  onClickBack() {
    wx.navigateBack({
      delta: 1
    })
  },
  // 跳转选择商品
  onClickToGood() {
    let data = this.data
    if (!data.supplierValue || !data.warehouseId) {
      wx.showToast({
        title: '请先选择' + (!data.supplierValue ? '销售方' : '仓库'),
        icon: 'error'
      })
      return
    }
    // 跳转商品选中页面
    let url = `/pages_sub/pages/ccs/down/order/return/selectGoods/index?warehouseId=${data.warehouseId}&warehouseName=${data.warehouseName}`
    wx.navigateTo({
      url: url,
    })
  },
  // 改变退货数量
  onChangeReturnQty(e) {
    const index = e.currentTarget.dataset.index
    let arr = [...this.data.goodsList]
    arr[index].applyQty = e.detail
    this.setData({
      goodsList: arr
    })
  },
  // 输入框失焦 格式化两位小数
  onFormatPrice(e) {
    const index = e.currentTarget.dataset.index
    let arr = [...this.data.goodsList]
    let formatNum = e.detail.value ? Number(e.detail.value).toFixed(2) : ''
    arr[index].applyPrice = formatNum
    this.setData({
      goodsList: arr
    })
  },
  onChangePrice(e) {
    const index = e.currentTarget.dataset.index
    let arr = [...this.data.goodsList]
    arr[index].applyPrice = e.detail
    this.setData({
      goodsList: arr
    })
  },
  // 提交退货申请
  onClickSubmit() {
    if(!this.data.shipModeValue) {
      wx.showToast({
        icon: 'none',
        title: '请选择运送方式!'
      })
      return false
    }
    if(this.data.goodsList.length === 0) {
      wx.showToast({
        icon: 'none',
        title: '请选择退货商品!'
      })
      return false
    }
    if(this.data.goodsList.find(item => item.applyQty === 0)) {
      
      wx.showToast({
        icon: 'none',
        title: '请填写退货数量!'
      })
      return false
    }
    if(this.data.goodsList.find(item => !item.applyPrice)) {
      wx.showToast({
        icon: 'none',
        title: '请填写退货单价!'
      })
      return false
    }
    Dialog.confirm({
      title: "温馨提示",
      message: "确定要提交退货吗?",
      context: this,
    }).then(() => {
      const saleItemInfo = wx.getStorageSync('saleItemInfo')
      const params = {
        // 发运方式
        backType: this.data.shipModeValue,
        // 仓库
        warehouseId: this.data.warehouseId,
        // 销售方
        vendorSetsOfBooksId: saleItemInfo.ssetsOfBooksId,
        vendorId: saleItemInfo.bvendorId,
        vendorCode: saleItemInfo.bvendorName,
        vendorName: saleItemInfo.ssetsOfBooksId,
        invoiceCustId: saleItemInfo.scustId,
        invoiceCustCode: saleItemInfo.scustCode,
        invoiceCustName: saleItemInfo.scustName,
        // 详细地址
        toContactAddress: this.data.addrInfo.contactAddr,
        defineAddr: this.data.addrInfo.defineAddr,
        // 联系人 字段名 before: contactPerson
        fromContactName: this.data.addrInfo.name,
        // 联系电话 字段名 before: telPhone
        fromContactNumber: this.data.addrInfo.phone,
        // 发货地址
        toProvinceId: this.data.shipModeValue === 1 ? this.data.addrInfo.provinceId : '',
        toCityId: this.data.shipModeValue === 1 ? this.data.addrInfo.cityId : '',
        toDistrictId: this.data.shipModeValue === 1 ? this.data.addrInfo.districtId : '',
        toTownId: this.data.shipModeValue === 1 ? this.data.addrInfo.townId : '',
        addLineList: this.data.goodsList.map(item => {
          return {
            itemId: item.itemId,
            itemCode: item.itemCode,
            itemName: item.itemName,
            applyPrice: item.applyPrice,
            applyQty: item.applyQty
          }
        })
      }
      App.getHttp()._post('/api/psi/orderItemback/myx/submitByMyx', params, true).then(res => {
        if (res && res.code === 200) {
          // 退货成功，
          wx.redirectTo({
            url: `/pages_sub/pages/ccs/down/order/return/returnResult/index?id=${res.content}`,
          })
          wx.removeStorageSync('returnGoods')
        } else {
          wx.showToast({
            icon: 'error',
            title: res.detail,
          })
        }
      })
    }).catch(() => {

    })
  },
  // 侧滑删除
  onClickDelItem(e) {
    wx.showActionSheet({
      alertText: '确定要删除吗?',
      itemList: ['是'],
      itemColor: '#FF4A4D',
      success: () => {
        const itemId = e.currentTarget.dataset.itemId
        this.setData({
          goodsList: this.data.goodsList.filter(res => res.itemId != itemId)
        })
      }
    })
  },
})