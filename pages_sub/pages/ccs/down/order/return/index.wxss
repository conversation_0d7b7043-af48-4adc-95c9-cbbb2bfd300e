/* pages_sub/pages/ccs/down/order/return/index.wxss */
.root-layout {
  position: relative;
  background-color: rgba(0, 0, 0, 0.04);
}
.page-detail {
  flex: 1;
}
.page-scroll-button {
  flex-shrink: 0;
}
.bottom-button-container {
  padding: 8rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #f3f4f6;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  justify-content: flex-end;
  position: fixed;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
}

.bottom-button-container van-button {
  flex: 1;
  margin: 16rpx 0rpx;
}
.bottom-button {
  width: 100%;
  margin: 8px 0px;
}
/* 重写样式 */
.head-layout {
  background-color: #fff;
  position: fixed;
  top: 0;
  width: 100%;
  padding-bottom: 12rpx;
  padding-left: 24rpx;
  z-index: 99;
}
.head-layout .image-return{
  width: 36rpx;
  height: 36rpx;
  margin-right: 4rpx ;
}
.page-title {
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 52rpx;
  margin-left: 16rpx;
  margin-right: 196rpx;
}
.cell-title {
  margin-left: 10rpx;
}
.radioGroup {
  display: flex;
  align-items: center;
}
.address {
  padding: 24rpx 32rpx;
  background-color: #fff;
}
.address-content {
  flex-direction: column;
  align-items: flex-end;
  margin: 0 16rpx;
}
.address-label {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 44rpx;
}
.add-btn {
  justify-content: center;
  background: #f3f9ff;
  padding: 24rpx 0;
}
.btn-text {
  color: #00b9c3;
  margin-left: 18rpx;
}
.good-block-title {
  padding: 0 24rpx;
  margin-top: 48rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
}
.sub-btn {
  background: #00b9c3;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 48rpx;
  padding: 16rpx 68rpx;
  margin-left: 32rpx;
}
.return-price {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff4a4d;
  font-size: 24rpx;
}
.return-title {
  color: #242424;
  margin-right: 8rpx;
}
.price-text {
  font-size: 40rpx;
  line-height: 40rpx;
}
.good-content {
  background-color: #fff;
  margin: 24rpx;
  border-radius: 8rpx;
}
.content-top {
  display: flex;
  padding: 24rpx;
}
.good-image {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}
.good-info {
  height: 176rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.good-supplier {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
}
.good-name {
  width: 284rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.info-bottom {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.good-pirce {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FF4A4D;
  line-height: 40rpx;
}
.price-symbol, .price-rem {
  font-size: 20rpx;
}
.good-price-text {
  font-size: 32rpx;
}
.onHand {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
}
.van-radio-group--horizontal {
  justify-content: flex-end;
}
.van-swipe-cell__right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65px;
  height: 100% !important;
  font-size: 15px;
  color: #fff;
  text-align: center;
  background-color: #FF4A4D;
}
