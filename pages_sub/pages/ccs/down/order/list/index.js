// pages/myOrder/myOrder.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    queryState: '4',
    typeList:[
      {title:'全部',name:'1'},
      {title:'待审核',name:'2'},
      {title:'待发货',name:'3'},
      {title:'待签收',name:'4'},
      {title:'已完成',name:'5'}
    ],
    dataList: [],
    sourceBilltype: '',
    pageIndex: 1,
    pageSize: 10,
    listViewH: '',
    keyword: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.queryState){
      this.setData({
        queryState:String(options.queryState)
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#tabsView').boundingClientRect()
        query.select('#filterSearch').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].bottom - App.globalData.deviceBottomOccPx
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 初始化数据
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList() // tapNavi会自动触发一次
  },

   /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getOrderList()
  },
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getOrderList()
  },

  // ------------------------------methods------------------------------
  // 搜索条件触发查询
  onConfirm(e) {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList(e.detail)
  },
  tapNavi: function (e) {
    this.setData({
      queryState: e.detail.name,
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  getOrderList(){
    const supInfo = wx.getStorageSync('supInfo')
    let url = '/api/psi/myx/purOrder/state/page'
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        invoiceSetsOfBooksId: supInfo.setsOfBooksId,
        queryState: this.data.queryState,
        keyWord: this.data.keyword
      }
    }
    if (this.data.queryState === '4') {
      params.param.queryState = undefined
      url = '/api/psi/myx/purOrder/state/unsigned'
    }
    App.getHttp()._post(url,params).then(res=>{
      let list = this.data.pageIndex === 1 ? [] : this.data.dataList
      if(res&&res.length>0){
        this.setData({
          dataList: list.concat(res)
        })
      } else {
        this.setData({
          dataList: list
        })
      }
    })
  },
  onCheckOrder(e){
    const dataset= e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/order/detail/index?stat=${dataset.stat}&id=${dataset.headId}`,
    })
  },
  // 输入框改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 确认搜索事件
  onClickSearch() {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    let keyword = this.data.keyword
    this.getOrderList(keyword)
  },
})