<!-- pages/myOrder/myOrder.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
  <view class="filter-search" id="filterSearch">
    <!-- <search showMsgImg="{{false}}" disabled="{{false}}" bind:onConfirm="onConfirm"></search> -->
    <van-search value="{{ keyword }}" placeholder="请输入搜索关键词" use-action-slot bind:change="onChangeKeyword" bind:search="onClickSearch">
      <view slot="action" class="search-right" bind:tap="onClickSearch">搜索</view>
    </van-search>
  </view>
  <van-tabs id="tabsView" class="tab-layout" active="{{queryState}}" bind:change="tapNavi" title-active-color="#00b9c3" line-height="4" line-width="24">
    <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
      <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
    </block>
  </van-tabs>
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <block wx:if="{{dataList.length > 0}}">
      <goodsCardOld wx:for="{{dataList}}" data-stat="{{item.orderHead.state}}" data-head-id="{{item.orderHead.id}}" bind:clickOrder="onCheckOrder" headTitle="订单号" orderHead="{{item.orderHead}}" orderLines="{{item.orderLines || item.outDetail}}" showExpandBlock="{{true}}"  dataType="{{queryState == '4'? 3 : 1}}" wx:key="index" />
    </block>
    <view class="m-t-25p" wx:else>
      <noneView ></noneView>
    </view>
  </listView>
</view>