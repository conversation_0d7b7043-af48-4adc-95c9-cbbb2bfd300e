// pages_sub/pages/ccs/down/order/outStock/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    outStockData : [],
    sourceSystem:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getOutStock(options.id)
    this.setData({
      sourceSystem:options.sourceSystem
    })
  },
  getOutStock(id) {
    App.getHttp()._post(`/myx/ccs-mobile-web/order/getOutBillSimpleRespList/${id}`,{}).then(res=>{
      this.setData({
        outStockData : res || []
      })
    })
  },
  onClickTodo(e) {
    console.log(e.currentTarget.dataset)
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/order/outStockDetail/index?id='+e.currentTarget.dataset.id+'&type=delivered'+'&sourceSystem='+this.data.sourceSystem,
    })
  }
})