// pages_sub/pages/ccs/down/order/fillResult/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    payAmount:0,
    paymentAmount:0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.payAmount){
      this.setData({
        payAmount:options.payAmount,
        paymentAmount:options.paymentAmount
      })
    }
  },
  onClickCheck(){
    // pages_sub/pages/ccs/sup/order/list/index
   wx.redirectTo({
     url: '/pages_sub/pages/ccs/sup/order/list/index?deliveryStat=1',
   })
  },
  onClickBack(){
    wx.switchTab({
      url: '/pages/ccs/purchase/index',
    })
  },
  onClickBackShop(){
    wx.navigateBack({
      delta: 1,
    })
  }
 
})