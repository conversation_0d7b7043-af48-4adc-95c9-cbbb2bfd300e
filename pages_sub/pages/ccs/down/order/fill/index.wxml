<!--pages_sub/pages/ccs/down/order/fill/index.wxml-->
<view class=" root-layout">
  <view class="ship-addr-layout">
    <!-- 发运方式 -->
    <view class="flex justify-content-between radio-group-layout" bind:tap="onShipRadioChange">
      <view class="radio-group-label">{{settlementData.groupObj.invoiceCustName}}
        <image class="more" src="/asset/svgs/left.svg"></image></view>
    </view>

    <!-- 收货地址 -->
    <view class="addr-layout " catchtap="onClickAddress">
      <view class="flex align-items-center">
        <view class="flexbox">
          <view class="flex justify-content-end">
            <image class="address-icon" src="/asset/imgs/purchase/location.png"></image>
            <view class="flexbox text-right font-s32-lh48 two-line-ellipsis">{{addrInfo.contactAddr || ''}}</view>
          </view>
          <view class="tel">{{addrInfo.name}} {{addrInfo.phone}}</view>
        </view>
        <image class="more" src="/asset/svgs/left.svg"></image>
      </view>
    </view>
    <!-- 合计金额 备注 -->
    <van-cell-group border="{{false}}" >
      <van-cell title="备注" border="{{false}}" custom-class="cell-value-bottom-class" value-class="cell-hint-class">
        <input placeholder="请输入" placeholder-class="placeholder-clazz" class="txt" bindinput="onBindinput"></input>
      </van-cell>
    </van-cell-group>
  </view>
  <!-- 黄色警告提示 -->
  <!-- <view class="warining-layout flex align-items-center">
    <image class="warining-icon" src="/asset/imgs/purchase/warning.png"></image>
    <text class="warining-txt font-s24-lh44">您选择的地址可能会造成商品库存变化，请提前确认再付款</text>
  </view> -->
  <!-- 商品明细 -->
  <view class="item-layout">
    <view class="adapter-layout">
      <view class="top-layout">
          <view class=" font-s32-lh48">{{settlementData.groupObj.saleOrgName}}</view>
      </view>
      <view class="line"></view>
      <!-- 商品区域 -->
      <view class="card-layout" wx:for="{{shoppingCartList}}" wx:for-item="cartItem" wx:key="cartItemIndex" wx:for-index="cartItemIndex">
        <block>
          <!-- 单品 -->
          <block>
            <!-- 商品区域 -->
            <view class="good-layout flex align-items-center">
              <view class="img-layout">
                <image mode="aspectFit" class="itemurl" wx:if="{{cartItem.itemUrl}}" src="{{cartItem.itemUrl}}" lazy-load="{{true}}"></image>
                <image class="itemurl"  wx:else src="/asset/imgs/bg_shop.png" mode="aspectFit"></image>
                <image class="noqty" wx:if="{{cartItem.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
              </view>
              <view class="flexbox">
                <view class="itemname two-line-ellipsis">{{cartItem.itemName}}/{{cartItem.itemCode}}</view>
                <view class="itemspecs">{{cartItem.specs || ''}}</view>
                <view class="price-des-layout">
                  <view class="itemprice"><text class="uom">¥</text><text>{{cartItem.finalDiscountPrice}}</text><text class="uom up-price">¥</text><text class="up-price">{{cartItem.applyPrice}}</text></view>
                  <view class="des-box flex align-items-end justify-content-between">
                    <!-- <view class="txt flexbox">库存 {{cartItem.qtyOnhand}}</view> -->
                    <view class="txt flexbox text-right">x<text class="price">{{cartItem.purchaseQty}}</text></view>
                  </view>
                </view>
              </view>
            </view>
            <view class="serial-number" wx:if="{{cartItem.shoppingCartItemType === 2 && cartItem.serialNumber}}">序列号：{{cartItem.serialNumber}}</view>
            <!-- <view class="serial-number">序列号：1254669878</view> -->
          </block>
        </block>
      </view>
    </view>
  </view>

  <view style="padding: 0 24rpx">
    <!-- 订单附加参数区域 -->
    <van-cell-group class="cell-group-layout m-t-24" border="{{false}}" >
      <van-cell title="商品总额" value="{{'￥'+totalPayAmount.payAmount}}" border="{{false}}" custom-class="cell-value-top-class" value-class="cell-price-class" />
      <van-cell title="商品总数" value="{{totalPayAmount.totalNum}}" border="{{false}}" custom-class="cell-value-top-class" value-class="cell-price-class" />
      <van-cell title="优惠金额" value="{{'￥'+totalPayAmount.finalAmount}}" border="{{false}}" custom-class="cell-value-class" value-class="cell-price-class" />
      <!-- <van-cell title="实际支付金额" value="{{'￥'+totalPayAmount.paymentAmount}}" border="{{false}}" custom-class="cell-value-bottom-class" value-class="cell-price-class" /> -->
    </van-cell-group>
    <van-dialog
      use-slot
      title="提醒"
      show="{{ stockLackShow }}"
      show-cancel-button
      bind:cancel="closeStockLack"
      bind:confirm="confirmStockLack"
    >
      <view class="stock-lack-message">
        <view class="stock-lack-message-item" wx:for="{{validateData.stockLack}}" wx:key="index">规格：{{item.specs || ''}}，库存量：{{item.stockQty}}，下单量：{{item.purchaseQty}}</view>
        <view>以上商品库存不足订单量，预计一周后发货，是否继续下单？</view>
      </view>
    </van-dialog>

  </view>
  <!-- 底部按钮 -->
  <!-- <footer-button btnWord="提交订单" bind:mainClick="doSubmit" /> -->
  <view class="btn-layout">
    <view class="btn-submit">
      <view class="flex justify-content-end align-items-center">
        <view class="font-s24-lh44">实际支付金额：<text class="uom">￥</text><text class="price">{{totalPayAmount.paymentAmount}}</text></view>
        <van-button wx:if="{{!isConfirm}}" custom-style="width:200rpx;height:80rpx" block type="primary" loading="{{submitLoading}}" loading-text="提交中,请稍后..." bind:click="doSubmit" color="#00b9c3" custom-class="custom-clazz">
          提交订单
        </van-button>
      </view>
    </view>
    <view class="box" />
  </view>
</view>