// pages_sub/pages/ccs/down/order/fill/index.js
const App = getApp()
const dayjs = require('dayjs')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showDatePicker: false,
    submitLoading: false,
    shipMode: '1',
    shipModeName: '汽运',
    planDeliveryDate: '',
    creditAmount: '',
    isConfirm: false,
    totalPayAmount: {
      totalNum: 0, // 商品数量
      payAmount: 0, // 总金额
      finalAmount: 0, // 实际支付金额
      paymentAmount: 0, // 优惠金额
    },
    settlementData: {},
    shoppingCartList: [],
    addrInfo: {},
    note: '',
    validateData: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      showPicker: true,
      settlementData: wx.getStorageSync('settlementData') //目前只能一个供应商 产品组 渠道下单,不能合并下单 ,先这样写了
    })
    this.getShopCartList()
    this.getDefaultAddress()
    this.getDefaultCust()
  },

  onShow: function () {
    const selectAddr = wx.getStorageSync('address')
    const groupObj = wx.getStorageSync('groupObj')
    wx.removeStorageSync('groupObj')
    wx.removeStorageSync('address')
    if (selectAddr) {
      this.setData({
        addrInfo: selectAddr,
      })
    }
    if (groupObj) {
      this.data.settlementData.groupObj = {...this.data.settlementData.groupObj, ...groupObj}
      this.setData({
        settlementData: this.data.settlementData
      }, () => {
        this.getShopCartList()
      })
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    wx.removeStorageSync('fillsList')
    wx.removeStorageSync('address')
  },
  // 跳转到地址选中页面
  onClickAddress() {
    wx.navigateTo({
      url: '/pages/ccs/moreFeatures/addr/index?action=order&sourceId=' + this.data.settlementData.groupObj.invoiceCustId,
    })
  },
  // 获取商品数据
  getShopCartList() {
    const url = '/api/vcs/shoppingCart/getPaymentPageItemList'
    let idList = this.data.settlementData.shopCartIds
    let groupObj = this.data.settlementData.groupObj
    const params = {
      idList: idList,
      saleOrgCode: groupObj.saleOrgCode,
      invoiceCustId: groupObj.invoiceCustId,
      invoiceCustCode: groupObj.invoiceCustCode,
      vendorSetsOfBooksId: groupObj.vendorSetsOfBooksId,
      supId: groupObj.supId,
      agencyLevel: groupObj.agencyLevel,
    }
    App.getHttp()._post(url, params).then(res => {
      if (res && res.length > 0) {
        this.setData({
          shoppingCartList: res,
          isConfirm: false
        }, () => {
          this.totalPayAmount()
        })
      }
    })
  },
  // 汇总金额计算相关
  totalPayAmount() {
    let shoppingCartList = this.data.shoppingCartList
    let totalNum = 0
    let payAmount = 0
    let finalAmount = 0
    let paymentAmount = 0
    shoppingCartList.forEach(item => {
      if (item.itemId === '-1') {} else {
        totalNum += item.purchaseQty - 0;
      }
      payAmount += (item.purchaseQty * item.applyPrice);
      paymentAmount += (item.purchaseQty * item.finalDiscountPrice);
    })
    finalAmount = (payAmount - paymentAmount)
    this.setData({
      totalPayAmount: {
        totalNum: totalNum.toFixed(2) - 0,
        payAmount: payAmount.toFixed(2) - 0,
        finalAmount: finalAmount.toFixed(2) - 0,
        paymentAmount: paymentAmount.toFixed(2) - 0,
      }
    })
  },
  // 获取默认地址
  getDefaultAddress: function () {
    App.getHttp()._post('/api/mmd/common/addr/page', {
      pageIndex: 1,
      pageSize: 50,
      param: { isUsable: 2, sourceId: this.data.settlementData.groupObj.invoiceCustId }
    }).then(res => {
      if (res && res.length > 0) {
        let haveDefault = false
        res.forEach((item) => {
          if (item.isDefault === 2) {
            haveDefault = true
            // 设置默认地址
            this.setData({
              addrInfo: item
            })
          }
        });
        if (!haveDefault) {
          this.setData({
            addrInfo: res[0]
          })
        }
      }
    })
  },
  // 获取默认客户数据
  getDefaultCust() {
    App.getHttp()._post('/api/mmd/common/bsSup/page', {
      pageIndex: 1,
      pageSize: 5,
      param: {
        isUsable: 2,
        saleOrgCode: this.data.settlementData.groupObj.saleOrgCode
      }
    }).then(res => {
      const findDefaultIndex = res.findIndex((findSup) => !findSup.mainAccountId);
      const item = res[findDefaultIndex > -1 ? findDefaultIndex : 0];
      const channelIdArr = item.channelIds.split(',');
      let groupObj = {
        agge: item.scustCode,
        invoiceCustCode: item.scustCode,
        invoiceCustId: item.scustId,
        invoiceCustName: item.scustName,
        invoiceSetsOfBooksId: item.bsetsOfBooksId,
        vendorCode: item.bvendorCode,
        vendorId: item.bvendorId,
        vendorName: item.bvendorName,
        vendorSetsOfBooksId: item.ssetsOfBooksId,
        supId: item.id,
        channelId: channelIdArr.length > 0 ? channelIdArr[channelIdArr.length - 1] : '',
        saleRegionCode: item.regionCode,
        agencyLevel: item.agencyLevel,
      }
      this.data.settlementData.groupObj = {...this.data.settlementData.groupObj, ...groupObj}
      this.setData({
        settlementData: this.data.settlementData
      })
    })
  },
  // 获取客户数据
  onShipRadioChange() {
    wx.navigateTo({
      url: `/pages/ccs/down/purchaseNew/searchApiSup/index?scustId=${this.data.settlementData.groupObj.invoiceCustId}&saleOrgCode=${this.data.settlementData.groupObj.saleOrgCode}`
    })
  },
  // 备注回调
  onBindinput(e) {
    this.data.note = e.detail.value
  },
  // 提交订单
  doSubmit(isConfirm) {
    const { addrInfo, settlementData, shoppingCartList } = this.data
    if (!this.data.addrInfo.provinceId) {
      wx.showToast({
        title: '必须选地址!',
        icon: 'error'
      })
      return
    }
    this.validateStock(shoppingCartList)
  },
  // 确认提交订单回调
  executeSubmit(lines) {
    if (this.data.submitLoading) return
    const { addrInfo, settlementData } = this.data
    const params = {
      ...settlementData.groupObj,
      remark: this.data.note,
      orderItemType: this.data.shoppingCartList[0].shoppingCartItemType,
      lines,
      sourceSystem: 2, // 来源PC
      addr: {
        contactPerson: addrInfo.name || '',
        telPhone: addrInfo.phone || '',
        defineAddr: addrInfo.addr,
        contactAddr: (addrInfo.provinceName || '') + (addrInfo.cityName || '') + (addrInfo.districtName || '') + (addrInfo.townName || '') + (addrInfo.addr || ''),
        provinceId: addrInfo.provinceId || '',
        provinceName: addrInfo.provinceName || '',
        cityId: addrInfo.cityId || '',
        cityName: addrInfo.cityName || '',
        districtId: addrInfo.districtId || '',
        districtName: addrInfo.districtName || '',
        townId: addrInfo.townId || '',
        townName: addrInfo.townName || '',
      },
    }
    this.setData({
      isConfirm: true,
      submitLoading: true
    })
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/createMultipleOrder', params, true).then(res => {
      this.setData({
        submitLoading: false
      }) 
      if (res.code && res.code == 62256) {
        wx.showModal({
          title: res.chnDesc,
          content: '操作提示',
          complete: (res) => {
            if (res.confirm) {
              this.getShopCartList()
            } else {
              this.setData({
                isConfirm: false
              }) 
            }
          }
        })
        return
      }
      wx.redirectTo({
        url: '/pages_sub/pages/ccs/down/order/fillResult/index?paymentAmount=' + (this.data.totalPayAmount.paymentAmount)+'&payAmount='+(this.data.totalPayAmount.payAmount)
      })
    }).catch(error => {
      this.setData({
        isConfirm: false,
        submitLoading: false
      }) 
    })
  },
  // 提交订单后，用于校验的数据处理
  validateStock(shoppingCartList) {
    const queryParam = [];
    const stockLack = [];
    const beSellFalse = [];
    let lines = []
    let orderLineList = []
    for (let index = 0; index < shoppingCartList.length; index++) {
      let shopCartItem = shoppingCartList[index]
      const tempLine = {
        ...shopCartItem,
        shoppingCartId: shopCartItem.id,
        applyQty: shopCartItem.purchaseQty,
        originalPrice: shopCartItem.applyPrice,
        applyPrice: shopCartItem.finalDiscountPrice,
        shoppingCartItemType: shopCartItem.shoppingCartItemType,
      };
      delete tempLine.id;
      orderLineList.push(tempLine);
      let itemType = shopCartItem.itemTypeName;
      if (this.data.settlementData.groupObj.shoppingCartItemType === 3) {
        itemType = '促销';
      } else if (this.data.settlementData.groupObj.shoppingCartItemType === 2) {
        itemType = '配件';
      }
      queryParam.push({
        itemCode: shopCartItem.itemCode,
        orgCode: this.data.settlementData.groupObj.saleOrgCode,
        beSell: shopCartItem.beSell,
        itemType: itemType,
      });
    }
    lines.push({
      orderType: 0,
      orderLineList,
    });
    App.getHttp()._post('/api/interface/erp/stock/query', {query: [...queryParam]}).then(res => {
      for (let f = 0; f < shoppingCartList.length; f++) {
        const fItem = shoppingCartList[f];
        const findTrue = res.find((item) => item.itemCode === fItem.itemCode);
        if (fItem.beSell === 1 && fItem.purchaseQty > findTrue.stockQty) {
          // 非促销商品，申请数量不能大于库存提示
          stockLack.push({
            ...fItem,
            stockQty: findTrue.stockQty,
          });
        }
        if (fItem.beSell === 2 && fItem.purchaseQty > findTrue.stockQty) {
          // 促销商品，申请数量不能大于库存
          beSellFalse.push({
            ...fItem,
            stockQty: findTrue.stockQty,
          });
        }
      }
      const validateData =  {
        lines,
        stockLack,
        beSellFalse,
      };
      this.messageBack(validateData)
    })
  },
  // 提交订单时，校验后提醒的回调
  messageBack(validateData) {
    if (validateData.beSellFalse && validateData.beSellFalse.length > 0) {
      wx.showToast({
        title: `促销商品${validateData.beSellFalse[0].itemName}库存不足，不能下单!`,
        icon: 'none'
      })
      return;
    }
    if (validateData.stockLack && validateData.stockLack.length > 0) {
      this.setData({
        validateData: validateData,
        stockLackShow: true
      })
    } else {
      this.executeSubmit(validateData.lines);
    }
  },
  closeStockLack() {
    this.setData({
      validateData: {}
    })
  },
  confirmStockLack() {
    this.executeSubmit(this.data.validateData.lines);
  }
})