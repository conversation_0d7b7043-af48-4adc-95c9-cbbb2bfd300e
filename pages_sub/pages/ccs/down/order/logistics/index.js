// pages_sub/pages/ccs/down/order/logistics/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active:0,
    steps: [
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getLogistics(options.billNo)
  },
  getLogistics(invOutBillHeadNo){
    App.getHttp()._post(`myx/ccs-mobile-web/order/getRoutesfromSf/${invOutBillHeadNo}`,invOutBillHeadNo).then(res=>{
      if(res&&res.routes.length>0){
        this.setData({
          steps:res.routes.map(res=>{
            return {
              text:res.remark,
              desc: res.acceptAddress+' '+res.acceptTime,
              inactiveIcon: '',
              activeIcon: 'success',
            }
          })
        })
      }
    })
  }
})