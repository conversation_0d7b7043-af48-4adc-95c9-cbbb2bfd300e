/* pages_sub/pages/ccs/down/salesReturnInv/add/index.wxss */
.root-layout {
  position: relative;
  background-color: rgba(0, 0, 0, 0.04);
}
.page-detail {
  flex: 1;
}
.page-scroll-button {
  flex-shrink: 0;
}
.footer-layout{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  padding-bottom: env(safe-area-inset-bottom);
}
.scan-btn-layout{
  padding: 24rpx;
}
.scan-btn-layout .scan-holder{
  width: 48rpx;
  height: 1rpx;
}
.scan-btn-layout .scan-btn{
  padding: 12rpx 0;
  border-radius: 8rpx;
  background: linear-gradient(to right, #00b9c3,#22d3dc);
  cursor: pointer;
  color: white;
  text-align: center;
}
.bottom-button-container {
  padding: 8rpx 32rpx;
  justify-content: flex-end;
  background-color: #fff;
}

.bottom-button-container van-button {
  flex: 1;
  margin: 16rpx 0rpx;
}
.bottom-button {
  width: 100%;
  margin: 8px 0px;
}
/* 重写样式 */
.head-layout {
  background-color: #fff;
  position: fixed;
  top: 0;
  width: 100%;
  padding-bottom: 12rpx;
  padding-left: 24rpx;
  z-index: 99;
}
.head-layout .image-return{
  width: 36rpx;
  height: 36rpx;
  margin-right: 4rpx ;
}
.page-title {
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 52rpx;
  margin-left: 16rpx;
  margin-right: 196rpx;
}
.cell-title {
  margin-left: 10rpx;
}
.radioGroup {
  display: flex;
  align-items: center;
}
.address {
  padding: 24rpx 32rpx;
  background-color: #fff;
}
.address-content {
  flex-direction: column;
  align-items: flex-end;
  margin: 0 16rpx;
}
.address-label {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 44rpx;
}
.add-btn {
  justify-content: center;
  background: #f3f9ff;
  padding: 24rpx 0;
}
.btn-text {
  color: #00b9c3;
  margin-left: 18rpx;
}
.good-block-title {
  padding: 0 24rpx;
  margin-top: 12rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
}
.sub-btn {
  background: #00b9c3;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 48rpx;
  padding: 16rpx 68rpx;
  margin-left: 32rpx;
}
.return-price {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff4a4d;
  font-size: 24rpx;
}
.return-title {
  color: #242424;
  margin-right: 8rpx;
}
.price-text {
  font-size: 40rpx;
  line-height: 40rpx;
}
.good-content {
  background-color: #fff;
  margin: 24rpx;
  border-radius: 8rpx;
}
.content-top {
  display: flex;
  padding: 24rpx;
}
.good-image {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}
.good-info {
  height: 176rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.good-supplier {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
}

.good-name-box{
  flex:1
}
.good-name-box .name {
  font-size: 28rpx;
  color: #242424;
  line-height: 28rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-name-box .specs {
  margin-top: 8rpx;
  font-size: 26rpx;
  color: #8a8a8a;
  line-height: 28rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.info-bottom {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.good-pirce {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FF4A4D;
  line-height: 40rpx;
}
.price-symbol, .price-rem {
  font-size: 20rpx;
}
.good-price-text {
  font-size: 32rpx;
}
.onHand {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
}
.van-radio-group--horizontal {
  justify-content: flex-end;
}
.van-swipe-cell__right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65px;
  height: 100% !important;
  font-size: 15px;
  color: #fff;
  text-align: center;
  background-color: #FF4A4D;
}
.bar-code{
  word-break: break-all;
}