<!-- pages_sub/pages/ccs/down/order/returnOut/add/index.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" style="padding-top: {{windowPaddingTop}}rpx;" id="page-layout">
  <view class="head-layout flex align-items-center" id="head-layout" style="padding-top: {{rectTop}}rpx;height: {{rectHeight}}rpx; line-height: {{rectHeight}}rpx">
    <van-icon bindtap="onClickBack" size="20" name="arrow-left" />
    <view class="page-title">采购退货出库新增</view>
  </view>
  <view class="page-detail" id="page-detail">
    <van-cell-group class="cell-group-layout m-t-16" border="{{false}}">
      <van-notice-bar color="#242424" background="#FFFBE6" left-icon="/asset/imgs/purchase/warning.png" text="请提前与销售方协商好退货商品的价格" />
      <van-cell title="销售方" title-class="cell-title" required value="{{supplierName}}" is-link data-type="supplierOptions" data-value="supplierValue" data-name="supplierName" bind:click="onClickTodo" />
      <van-cell title="退货仓库" title-class="cell-title" required value="{{warehouseName}}" is-link data-type="warehouseOptions" data-value="warehouseId" data-name="warehouseName" bind:click="onClickTodo" />
      <van-cell title-width="198rpx" input-align="right" title="运送方式">
        <van-radio-group direction="horizontal" value="{{ shipModeValue }}" bind:change="onChangeShip">
          <van-radio wx:for="{{shipMode}}" wx:key="index" name="{{item.value}}">
            {{item.name}}
          </van-radio>
        </van-radio-group>
      </van-cell>
      <view class="address flex align-items-center" bindtap="onClickAddress">
        <van-icon size="20" style="align-self: flex-start" name="/asset/imgs/purchase/location.png"></van-icon>
        <view class="address-content flex flexbox">
          <view class="address-text">{{addrInfo.contactAddr}}</view>
          <view class="address-label">{{addrInfo.name}} {{addrInfo.phone}}</view>
        </view>
        <van-icon size="20" color="#bdbdbd" name="arrow" />
      </view>
    </van-cell-group>
    <view class="add-btn flex align-items-center" bindtap="onClickToGood">
      <van-icon size="17" color="#00b9c3" name="plus" />
      <view class="btn-text">添加商品</view>
    </view>
    <view class="good-block-title" wx:if="{{goodsList.length > 0}}">退货商品</view>
  </view>
  <listView class="list-layout" viewHeightPx="{{listViewH}}">
    <van-collapse value="{{ collapseNames }}" bind:change="onChangeCollape">
      <view class="good-content align-items-center" wx:for="{{goodsList}}" wx:key="index">
        <van-swipe-cell right-width="{{ 65 }}" wx:key="index">
          <view class="content-top">
            <image class="good-image" mode="aspectFit" src="{{item.itemUrl}}"></image>
            <view class="good-info">
              <view class="good-supplier">{{supplierName}}</view>
              <view class="info-bottom">
                <view class="good-name-box">
                  <view class="name">{{item.itemName}}</view>
                  <view class="specs">{{item.specs}}</view>
                </view>
                <!-- <view class="good-price-block">
                <view class="good-pirce">
                  <text class="price-symbol">¥</text>
                  <text class="good-price-text">{{item.price ? wxsUtil.moneyFormatInt(item.price, 'int') : 0}}</text>
                  <text class="price-rem">{{wxsUtil.moneyFormatInt(item.price)}}</text>
                </view>
                <view class="onHand">库存: {{item.qtyAvi}}</view>
              </view> -->
              </view>
            </view>
          </view>
          <view slot="right" class="swipe-right-del" bindtap="onClickDelItem" data-item-id="{{item.itemId}}">
            删除
          </view>
        </van-swipe-cell>
        <van-cell border="{{false}}" title="退货数量">
          <van-stepper value="{{item.applyQty}}" min="0" data-index="{{index}}" bind:change="onChangeReturnQty" />
        </van-cell>
        <van-field type="digit" label="退货单价" left-icon="edit" value="{{item.applyPrice}}" placeholder="0.00" use-button-slot data-index="{{index}}" bind:blur="onFormatPrice" bind:change="onChangePrice" input-align="right">
          <view slot="button">元</view>
        </van-field>
        <van-collapse-item name="{{'collapse'+index}}" wx:if="{{item.barCodeFlowResponseDTOList}}">
          <view slot="title">已扫码信息({{item.barCodeFlowResponseDTOList.length}})</view>
          <view slot="value">时间</view>
          <view class="flex font-sub-hint m-b-12" wx:for="{{item.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex">
            <view class="bar-code">条码：{{barCodeItem.barCode}} 扫码时间：{{barCodeItem.createTime}}</view>
          </view>
        </van-collapse-item>
      </view>
    </van-collapse>
  </listView>
  <view class="footer-layout" id="footer-layout">
    <scan-btns wx:if="{{detailData.isConfirm != 2}}" routeName="DownOrderReturnOutAdd" catch:clickReduceScan="onClickReduceScan" catch:clickAddScan="onClickAddScan"/>
    <!-- <view class="flex scan-btn-layout">
      <view class="flexbox scan-btn" catchtap="onClickReduceScan">扫码扣减商品</view>
      <view class="scan-holder"></view>
      <view class="flexbox scan-btn" catchtap="onClickAddScan">扫码添加商品</view>
    </view> -->
    <view class="bottom-button-container flex align-items-center">
      <view class="return-price">
        <text class="return-title">退货金额:</text>
        <text>¥</text>
        <text class="price-text">{{wxsUtil.totalAmount(goodsList)}}</text>
      </view>
      <view class="sub-btn" bindtap="onClickSubmit">提交</view>
    </view>
  </view>
  <van-popup show="{{ show }}" round position="bottom" custom-style="height: 60%" bind:close="onClose">
    <van-picker columns="{{ pickerColumns }}" show-toolbar title="{{selectedName == 'warehouseName' ? '选择仓库' : '选择销售方'}}" bind:cancel="onCancel" bind:confirm="onConfirm" />
  </van-popup>
  <van-dialog confirm-button-color="#00b9c3" id="van-dialog" />
</view>