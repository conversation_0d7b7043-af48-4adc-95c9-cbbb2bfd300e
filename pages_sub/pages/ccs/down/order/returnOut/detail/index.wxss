/* pages_sub/pages/ccs/down/order/returnOut/detail/index.wxss */
.bg-img {
  width: 100%;
  height: 332rpx;
}
page {
  background-color: rgba(0,0,0,0.04);
}
.root-layout {
  position: absolute;
  top: 48rpx;
  left: 0rpx;
  right: 0rpx;
  bottom: 148rpx;
  overflow: scroll;
  padding: 0 24rpx;
  margin-bottom: 24rpx;
}
.head-layout {
  flex-direction: column;
  align-items: center;
}
.bill-state {
  font-size: 32rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 36rpx;
}
.bill-no {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 36rpx;
  margin-top: 16rpx;
  margin-bottom: 40rpx;
}
.no-text {
  margin:  0 8rpx;
}
.good-layout {
  background: #FFFFFF;
  border-radius: 0 0 8rpx 8rpx;
  padding: 24rpx 32rpx;
  margin: 24rpx 0;
}
.good-item {
  display: flex;
  align-items: flex-start;
}
.good-img {
  width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
}
.good-info {
  flex: 1;
  height: 160rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 16rpx;
}
.good-name-box{

}
.good-name-box .name {
  font-size: 28rpx;
  color: #242424;
  line-height: 28rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-name-box .specs {
  margin-top: 8rpx;
  font-size: 26rpx;
  color: #8a8a8a;
  line-height: 28rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 100%;
}
.price-text {
  font-size: 28rpx;
  color: #242424;
  line-height: 28rpx;
}
.price-qty {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #707070;
}
.total-block {
  text-align: right;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  line-height: 24rpx;
  margin-top: 8rpx;
}
.total-label {
  font-size: 24rpx;
  color: #242424;
}
.total-text {
  font-size: 32rpx;
}
.footer-block {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-family: PingFang SC-Medium, PingFang SC;
  background-color: #fff;
  padding: 32rpx 32rpx 54rpx 32rpx;
  width: 100%;
  box-sizing: border-box;
}
.footer-amount-qty {
  font-size: 24rpx;
  font-weight: 400;
  color: #707070;
  line-height: 24rpx;
}
.footer-amount-price {
  font-weight: 500;
  color: #3D3D3D;
  line-height: 24rpx;
  margin-top: 16rpx;
}
.amount-price-label {
  font-size: 24rpx;
}
.amount-price-text {
  font-size: 32rpx;
}