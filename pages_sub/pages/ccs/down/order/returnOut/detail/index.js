// pages_sub/pages/ccs/down/order/returnOut/detail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    billNo: '',
    detailData: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if(options.id) {
      this.setData({
        id: options.id
      })
      this.getOderDetail()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 获取单个单据号信息
  getOderDetail() {
    App.getHttp()
      ._get(`/api/psi/purInvOutBackBill/myx/${this.data.id}`)
      .then((res) => {
        if (res) {
          this.setData({
            detailData: res
          });
        }
      });
  },
  // 复制单号
  onClickCopy(e) {
    const no = e.currentTarget.dataset.no
    wx.setClipboardData({
      data: no,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制单号成功'
        })
      }
    })
  }
})