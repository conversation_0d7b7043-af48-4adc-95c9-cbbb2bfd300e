<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<view class="root-layout">
    <view class="head-layout flex">
        <view class="bill-state">{{detailData.isConfirm == 2 ? '已出库': '待出库'}}</view>
        <view class="bill-no flex align-items-center">
            <view>订单号:</view>
            <view class="no-text">{{detailData.billNo}}</view>
            <van-icon size="12" bindtap="onClickCopy" data-no="{{detailData.billNo}}" name="/asset/imgs/purchase/copy.png"></van-icon>
        </view>
    </view>
    <detail-address orderAddr="{{detailData}}"/>
    <block wx:if="{{detailData.addLineList && detailData.addLineList.length > 0}}">
        <view class="good-layout" wx:for="{{detailData.addLineList}}" wx:key="index">
            <view class="good-item">
                <image class="good-img" mode="aspectFit"  src="{{item.itemUrl}}"></image>
                <view class="good-info">
                    <view class="good-name-box">
                      <view class="name">{{item.itemName}}</view>
                      <view class="specs">{{item.specs}}</view>
                      <view class="price-qty">共{{item.billQty}}件，出库{{item.confirmQty}}件</view>
                    </view>
                    <view class="good-price">
                        <view class="price-text">￥{{item.pricecBillF}}</view>
                    </view>
                </view>
            </view>
            <view class="total-block">
                <text class="total-label">合计:</text>
                <text class="total-text">￥{{item.amountBillF}}</text>
            </view>
        </view>
    </block>
    <van-field input-align="right" readonly label="创建时间" value="{{detailData.createTime}}"></van-field>
    <!-- <van-field input-align="right" readonly label="支付金额" value="{{'¥' + detailData.amountTotal}}"></van-field> -->
    <van-field input-align="right" readonly label="配送方式" value="{{detailData.shipModeName}}"></van-field>
    <van-field input-align="right" readonly label="审核时间" value="{{detailData.auditedDate}}"></van-field>
    <view class="footer-block">
        <view class="footer-amount-qty">出库数量共{{detailData.totalQty}}件</view>
        <view class="footer-amount-price">
            <text class="amount-price-label">出库合计:</text>
            <text class="amount-price-text">￥{{detailData.amountTotal}}</text>
        </view>
    </view>
</view>