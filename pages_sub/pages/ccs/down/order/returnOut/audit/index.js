// pages_sub/pages/ccs/down/order/returnOut/audit/index.js
const App = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: "",
    detailData: {},
    confirmLineList: [],
    paddingBottomRpx:132,
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.id,
    });
    this.getOderDetail();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    const query = wx.createSelectorQuery()
        query.select('#footer-layout').boundingClientRect()
        query.exec((exceRes)=>{
          this.setData({
            paddingBottomRpx: App.globalData.pxToRpxRatio*exceRes[0].height
          })
        })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  // 获取单个单据号信息
  getOderDetail() {
    App.getHttp()
      ._get(`/api/psi/purInvOutBackBill//myx/${this.data.id}`)
      .then((res) => {
        if (res ) {
          this.setData({
            detailData: res,
            confirmLineList: res.addLineList.map((item) => {
              return {
                id: item.id,
                confirmReqQty:
                  res.isConfirm === 2 ? item.confirmQty : item.billQty,
                price: item.pricecBillF,
                scanSize:item.barCodeFlowResponseDTOList.length
              };
            }),
          });
        }
      });
  },
  // 组件内修改审核数量回调
  changeCount(e) {
    const confirmData = e.detail;
    this.setData({
      confirmLineList: confirmData,
    });
  },
  // 确认审核
  onClickConfirm() {
    let exitDiff = false
    const url = "/api/psi/purInvOutBackBill/myx/confirm";
    const params = {
      id: this.data.detailData.id,
      billNo: this.data.detailData.billNo,
      version: this.data.detailData.version,
      custId:this.data.detailData.custId,
      custCode:this.data.detailData.custCode,
      custName:this.data.detailData.custName,
      confirmLineList: this.data.confirmLineList.map((item) => {
        if(!exitDiff)exitDiff = item.confirmReqQty>item.scanSize
        return {
          id: item.id,
          confirmReqQty: item.confirmReqQty,
        };
      }),
    };
    if(exitDiff){
      wx.showModal({
        title: '是否出库',
        content: '当前商品数量大于条码数量，请确认是否继续出库?',
        success: (resM) => {
          if (resM.confirm) {
            App.getHttp()
            ._post(url, params)
            .then((res) => {
              wx.redirectTo({
                url: '/pages_sub/pages/ccs/down/order/returnOut/audit/result/index?id=' + this.data.detailData.id
              })
            });
          }
        }
      })
    }else{
      App.getHttp()
      ._post(url, params)
      .then((res) => {
        wx.redirectTo({
          url: '/pages_sub/pages/ccs/down/order/returnOut/audit/result/index?id=' + this.data.detailData.id
        })
      });
    }
  },
   // 扫码扣减商品
   onClickReduceScan(e){
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/scanPDA/returnOut/index?mode=reduce&id=${this.data.id}`,
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.getOderDetail();
            }
          },
        }
      })
    } else {
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const addParams = {
              headId: this.data.detailData.id,
              billNo: this.data.detailData.billNo,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/purInvOutBackBill/myx/cancelScanBarCode', addParams)
            this.getOderDetail();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail:(err)=>{
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
   // 扫码增加商品
   onClickAddScan(e){
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/scanPDA/returnOut/index?mode=add&&id=${this.data.id}`,
        events: {
          acceptDataFromScanPDAPage: (data)=> {
            if(data.refresh){
              this.getOderDetail();
            }
          },
        }
      })
    } else {
    // 允许从相机和相册扫码
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const addParams = {
              headId: this.data.detailData.id,
              billNo: this.data.detailData.billNo,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/purInvOutBackBill/myx/scanBarCode', addParams)
            this.getOderDetail();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail:(err)=>{
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
});
