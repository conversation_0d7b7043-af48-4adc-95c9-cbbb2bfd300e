<wxs src="./index.wxs" module="wxsUtil"></wxs>
<view class="root-layout" style="padding-bottom: {{paddingBottomRpx}}rpx;">
  <salesOutItem isAudit="{{true}}" showCollapse="{{true}}" showAmount="{{false}}" bind:changeCount="changeCount" id="confirmList" itemObj="{{detailData}}" />
</view>
<view class="footer-layout" id="footer-layout">
  <scan-btns wx:if="{{detailData.isConfirm != 2}}" routeName="DownOrderReturnOutAudit" catch:clickReduceScan="onClickReduceScan" catch:clickAddScan="onClickAddScan"/>
  <!-- <view class="flex scan-btn-layout"  wx:if="{{detailData.isConfirm != 2}}">
    <view class="flexbox scan-btn" catchtap="onClickReduceScan">扫码扣减商品</view>
    <view class="scan-holder"></view>
    <view class="flexbox scan-btn" catchtap="onClickAddScan">扫码添加商品</view>
  </view> -->
  <view class="footer flex align-items-center justify-content-between">
    <view class="footer-left">
      <view class="totalCount">共{{wxsUtil.getTotalCount(confirmLineList)}}件</view>
      <view class="totalPrice row-layout">
        <view class="price-label">合计:</view>
        <view class="row-layout price-content">
          <view class="price-symbol">￥</view>
          <view class="price-text">{{wxsUtil.getTotalPrice(confirmLineList)}}</view>
        </view>
      </view>
    </view>
    <view class="footer-btn" wx:if="{{detailData.isConfirm != 2}}" bind:tap="onClickConfirm">
      确认出库
    </view>
  </view>
</view>