// pages_sub/pages/ccs/down/order/returnOut/audit/result/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id) {
      this.setData({
        id: options.id
      })
    }
  },
  onClickCheckDetail(){
   wx.redirectTo({
     url: '/pages_sub/pages/ccs/down/order/returnOut/detail/index?id=' + this.data.id,
   })
  },
  onClickBack(){
    wx.redirectTo({
      url: '/pages_sub/pages/ccs/down/order/returnOut/index',
    })
  }
 
})