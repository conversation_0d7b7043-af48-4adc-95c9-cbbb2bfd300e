.root-layout {
  width: 100vw;
  height: 100vh;
}
.tab-layout {
  background-color: white;
}
.listItemLayout {
  margin: 24rpx 0;
}
.search-right {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
  margin: 0 24rpx;
}
.audit-btn {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.audit-btn .scan-rate{
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
}
.float-btn{
  position: fixed;
  bottom: 60rpx;
  right: 40rpx;
  background: linear-gradient(to right, #00b9c3a1,#22d3dc3a);
  width: 80rpx;
  height: 80rpx;
  text-align: center;
  border-radius: 50%;
  color: white;
  font-size: 60rpx;
  font-weight: 600;
  line-height: 80rpx;
}
/* 重写页面组件的样式 */
.van-tabs__line {
  background: #02a7af!important;
}
.van-cell {
  padding: 10rpx 20rpx 10rpx 0!important; 
}