<!-- pages/ccs/orderDetail/index.wxml -->
<view class="root-layout">
  <scroll-view style="height: {{scrollHeight}}rpx;margin-bottom: 24rpx" scroll-y>
    <van-field value="{{ detailInfo.orderHead.shipModeName }}" label="运送方式" readonly input-align="right" />
  <van-field value="{{ detailInfo.orderAddr.contactAddr }}" readonly left-icon="/asset/imgs/purchase/location.png" autosize border="{{false}}" type="textarea" input-align="right" />
  <van-cell border="{{false}}" value="{{detailInfo.orderAddr.contactPerson + '  ' + detailInfo.orderAddr.telPhone}}"></van-cell>
  <view class="item-layout block-layout flex align-items-center">
    <view class="state-item flex">
      <view class="state-qty {{detailInfo.orderHead.state == 2 && detailInfo.orderHead.queryStatName == '待审'  ? 'activeState' : ''}}">{{detailInfo.orderHead.totalToAuditQty}}</view>
      <view class="state-text {{detailInfo.orderHead.state == 2 && detailInfo.orderHead.queryStatName == '待审' ? 'activeState' : ''}}">待审核</view>
    </view>
    <view class="state-item flex">
      <view class="state-qty {{detailInfo.orderHead.isUndelivered == 2 ? 'activeState' : ''}}">{{detailInfo.orderHead.totalToDeliveryQty}}</view>
      <view class="state-text {{detailInfo.orderHead.isUndelivered == 2 ? 'activeState' : ''}}">待发货</view>
    </view>
    <view class="state-item flex">
      <view class="state-qty {{detailInfo.orderHead.isUnsigned == 2 ? 'activeState' : ''}}">{{detailInfo.orderHead.totalToSignedQty}}</view>
      <view class="state-text {{detailInfo.orderHead.isUnsigned == 2 ? 'activeState' : ''}}">待签收</view>
    </view>
    <view class="state-item flex">
      <view class="state-qty {{detailInfo.orderHead.isCompleted == 2 ? 'activeState' : ''}}">{{detailInfo.orderHead.totalSignedQty}}</view>
      <view class="state-text {{detailInfo.orderHead.isCompleted == 2 ? 'activeState' : ''}}">已完成</view>
    </view>
  </view>
  <goodsCardNew showState="{{false}}" headTitle="订单号" showQty="{{true}}" orderHead="{{detailInfo.orderHead}}" orderLines="{{detailInfo.orderLines}}"  dataType="{{1}}" />
  <view class="block-layout">
    <van-cell-group class="cell-group-layout" border="{{false}}">
      <van-field value="{{'￥'+detailInfo.orderHead.totalAmount}}" label="商品总价" readonly input-align="right" />
      <van-field value="{{'￥'+amountInfo.availableAmount}}" label="可用金额" readonly input-align="right" />
      <!-- <van-field value="{{'￥'+detailInfo.orderHead.totalToDeliveryAmount}}" error label="支付金额" readonly input-align="right" /> -->
    </van-cell-group>
  </view>
  <view class="block-layout">
    <van-cell-group class="cell-group-layout" border="{{false}}">
      <van-field value="{{detailInfo.orderHead.planDeliveryDate}}" label="预计发货时间" readonly input-align="right" />
      <van-field label="备注" value="{{ detailInfo.orderHead.remark }}" readonly autosize border="{{false}}" type="textarea" input-align="right" />
    </van-cell-group>
  </view>
  <view class="placeholder-layout block-layout">
    <van-field value="{{detailInfo.orderHead.poNo}}" label="订单号" use-button-slot readonly input-align="right" >
      <view slot="button" class="copy" data-no="{{detailInfo.orderHead.poNo}}" bindtap="onClickCopy">
        |  复制
      </view>
    </van-field>
    <van-field value="{{detailInfo.orderHead.createTime}}" label="下单时间" readonly input-align="right" />
    <van-field value="{{detailInfo.orderHead.auditedDate}}" label="审核时间" readonly input-align="right" />
  </view>
  </scroll-view>
  <view id="foot-layout" class="foot-layout flex align-items-center justify-content-end" wx:if="{{detailInfo.orderHead.state == 2 && detailInfo.orderHead.queryStatName == '待审'}}">
    <view class="cancel" bindtap="onClickClose">取消订单</view>
  </view>
</view>