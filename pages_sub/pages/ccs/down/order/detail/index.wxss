/* pages/ccs/orderDetail/index.wxss */
.root-layout {
  background-color: rgb(245, 245, 245);
}

.placeholder-layout {
  height: 116rpx;
}

.stat-layout {
  padding: 0 24rpx;
  height: 168rpx;
  box-sizing: border-box;
  background-image: linear-gradient(-55deg, #00b9c3 0%, #D0005A 100%);
}

.stat-layout .stat {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 48rpx;
  font-weight: 500;
}

.stat-layout .no {
  margin-top: 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
  font-weight: 400;
}

.stat-layout .stationc {
  width: 64rpx;
  height: 64rpx;
}
.item-layout{
  margin-top: 16rpx;
  background-color: #fff;
  padding: 24rpx;
  border-radius: 8rpx;
}
.top-layout {
  padding: 24rpx 0;
  background-color: #FFFFFF;
}

.take-goods-detail {
  padding: 0 24rpx 20rpx;
  font-size: 24rpx;
  line-height: 30rpx;
  background: #ddd;
  padding-top: 20rpx;
  margin-bottom: 20rpx;
}
.take-goods-detail .contacts-detail {
  padding: 20rpx 0 0;
  font-size: 20rpx;
  color: #999;
}

.serial-box {
  height: 44rpx;
  padding: 0 24rpx 0 16rpx;
  border-left: 8rpx solid #00b9c3;
}

.serial-box .serial {
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 500;
}
.total-layout {
  padding: 26rpx 24rpx;
  background-color: #FFFFFF;
  text-align: right;
}
.total-layout .totaltxt {
  margin-left: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}
.total-layout .totalprice {
  font-family: SanFranciscoText-Medium;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: right;
  line-height: 48rpx;
  font-weight: 500;
}
.cell-group-layout .cell-value-class{
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 500;
  margin: 24rpx;
}
.cell-group-layout .cell-price-class{
  font-family: SanFranciscoText-Medium;
font-size: 28rpx;
color: #F97D4E;
line-height: 44rpx;
font-weight: 500
}
.txt {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
  font-weight: 400;
}
.placeholder-layout {
  height: 116rpx;
}
.foot-layout {
  height: 100rpx;
  padding: 0 24rpx;
  background-color: #ffffff;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}
.cancel {
  background: #00b9c3;
  border-radius: 8rpx;
  padding: 16rpx 0;
  text-align: center;
  color: #fff;
  width: 100%;
}
.block-layout {
  margin: 24rpx 24rpx 0 24rpx;
  border-radius: 8rpx;
}
.item-layout {
  justify-content: space-around;
}
.state-item {
  flex-direction: column;
  align-items: center;
}
.state-qty {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  font-size: 44rpx;
  line-height: 60rpx;
}
.state-text {
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #9E9E9E;
  font-size: 28rpx;
  line-height: 44rpx;
}
.activeState {
  color: #00b9c3;
}
.copy {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #707070;
  line-height: 44rpx;
}