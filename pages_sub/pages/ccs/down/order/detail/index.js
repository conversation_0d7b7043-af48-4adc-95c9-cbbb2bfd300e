// pages/ccs/orderDetail/index.js
const App=getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    stat:'',
    id:'',
    detailInfo: {},
    amountInfo: {},
    scrollHeight: 1006
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id){
      this.data.stat = options.stat
      this.data.id = options.id
      this.getOrderDetail()
    }
  },
  getOrderDetail(){
    App.getHttp()._post('/api/psi/myx/purOrder/state/getStateDetail',{id:this.data.id}).then(res=>{
      this.setData({
        detailInfo:res
      })
      const self = this
      const query = wx.createSelectorQuery()
      query.select('#foot-layout').boundingClientRect()
      wx.getSystemInfo({
        success(sysRes) {
          query.exec(function(exceRes){
            let scrollH = sysRes.windowHeight - (exceRes[0] ? exceRes[0].height : 0) - App.globalData.deviceBottomOccPx
            self.setData({
              scrollHeight: scrollH * App.globalData.pxToRpxRatio
            })
          })
        }
      })
      this.getAvailableAmount()
    })
  },
  onClickTodo(e){
    if(e.currentTarget.dataset.type==='delivered') {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/down/order/outStock/index?id='+this.data.id+'&type='+e.currentTarget.dataset.type+'&sourceSystem='+this.data.detailInfo.sourceSystem,
      })
    } else {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/down/order/question/index?id='+this.data.id+'&type='+e.currentTarget.dataset.type+'&sourceSystem='+this.data.detailInfo.sourceSystem,
      })
    }
  },
  onClickOnceAgain(e){
    App.getHttp()._get(`/myx/ccs-mobile-web/order/copyOrderItemsToCart/${this.data.detailInfo.mergeNo}`).then(res=>{
      if(res){
        wx.showToast({
          title: '添加成功',
          icon:'success'
        })
      }
    })
  },
  // 复制单号
  onClickCopy(e) {
    const no = e.currentTarget.dataset.no
    wx.setClipboardData({
      data: no,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制成功',
          image: '/asset/imgs/purchase/add-success.png'
        })
      }
    })
  },
  // 获取可用金额
  getAvailableAmount() {
    const url = '/api/psi/report/myx/cust/deferpay'
    const params = {
      vendorSetsOfBooksId: this.data.detailInfo.orderHead.vendorSetsOfBooksId,
      vendorCode: this.data.detailInfo.orderHead.vendorCode,
      invoiceSetsOfBooksId: this.data.detailInfo.orderHead.invoiceSetsOfBooksId,
      invoiceCustCode: this.data.detailInfo.orderHead.invoiceCustCode,
    }
    App.getHttp()._post(url, params).then(res => {
      if(res && res.length > 0) {
        this.setData({
          amountInfo: res[0]
        })
      }
    })
  },
  // 取消订单
  onClickClose() {
    const url = '/api/psi/myx/purOrder/order/close'
    const params = {
      id: this.data.detailInfo.orderHead.id
    }
    wx.showModal({
      title: '确定要取消订单吗?',
      content: '',
      success: (resM) => {
        if (resM.confirm) {
          App.getHttp()._post(url, params).then(res => {
            wx.navigateBack({
              delta: 1
            })
          })
        }
      }
    })
  }
})