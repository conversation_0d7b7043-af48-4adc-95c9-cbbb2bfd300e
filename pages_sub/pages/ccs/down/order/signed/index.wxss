/* pages_sub/pages/ccs/down/order/signed/index.wxss */
.root-layout{
  background: rgba(0,0,0,0.04);
}
.card-layout {
  background-color: #fff;
  margin: 24rpx;
  border-radius: 8rpx;
}
.card-bill {
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 48rpx;
  padding: 24rpx;
}
.card-supplier {
  padding: 0 24rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 44rpx;
}
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.line{
  width: 100%;
  height: 1rpx;
  background-color: rgba(0,0,0,0.1);
  -webkit-transform-origin: 0 0; 
  transform-origin: 0 0; 
  -webkit-transform: scaleY(0.5); 
  transform: scaleY(0.5)
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.m-t-16{
  margin-top: 16rpx;
}
.m-l-24{
  margin-left: 24rpx;
}
.card-layout{
  background-color: #FFFFFF;
}
.card-layout .good-layout {
  padding: 24rpx;
}
.card-layout .good-layout .itemname {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
}
.card-layout .good-layout .specs {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
}
.card-layout .good-layout .img {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}
.good-layout .cart-title {
  height: 72rpx;
  margin-bottom: 10rpx;
}

.justify-content-between {
  justify-content: space-between;
}
.card-layout .good-layout .count-box .itemprice {
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 40rpx;
}
.price-symbol {
  font-size:20rpx;
}
.content-layout {
  flex-direction: column;
  justify-content: space-between;
}
.card-layout .good-layout .count-box .itemqty {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
}
.card-layout .good-layout .signed-label {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 44rpx;
}
.total-layout {
  margin: 24rpx;
  border-radius: 8rpx;
}
.bottom-button {
  float: right;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 20rpx;
  color: #fff;
  background-color: #00b9c3;
  text-align: center;
  padding: 0 20rpx;
}
.footer-layout{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  padding-bottom: env(safe-area-inset-bottom);
}
.scan-btn-layout{
  padding: 24rpx;
  background: rgba(0,0,0,0.04);
}
.scan-btn-layout .scan-holder{
  width: 48rpx;
  height: 1rpx;
}
.scan-btn-layout .scan-btn{
  padding: 12rpx 0;
  border-radius: 8rpx;
  background: linear-gradient(to right, #00b9c3,#22d3dc);
  cursor: pointer;
  color: white;
  text-align: center;
}
.footer {
  background-color: #fff;
  padding-top: 8rpx;
  padding-left: 34rpx;
  padding-right: 34rpx;
  padding-bottom: 16rpx;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.footer-btn {
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 48rpx;
  padding: 16rpx 130rpx;
}
.confirm-btn {
  background: #00b9c3;
  color: #FFFFFF;
}
.edit-layout {
  align-items: flex-end;
}
.bar-code{
  word-break: break-all;
}
.m-b-12{
  margin-bottom: 12rpx;
}

