// pages_sub/pages/ccs/down/order/signed/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    outHeadId: '',
    totalToSignQty: 0,
    totalSignedQty: 0,
    warehouseName: '请选择',
    warehouseId: undefined,
    show: false,
    warehouseColumns: [],
    infoData: {},
    signType: '',
    collapseNames: [],
    scrollHeightPx: 675
  },
  onLoad: function (options) {
    if (options.outHeadId) {
      this.setData({
        signType: Number(options.signType)
      })
      this.data.id = options.id
      this.data.outHeadId = options.outHeadId
      this.getWarehouseColumns()
      this.getQuestItems()
    }
  },
  onReady() {
    const query = wx.createSelectorQuery()

    query.select('#footer-layout').boundingClientRect()
    query.exec((exceRes) => {
      this.setData({
        scrollHeightPx: exceRes[0].top
      })
    })
  },
  getQuestItems() {
    App.getHttp()._post('/api/psi/myx/purOrder/state/getOutboundOrderList', {
      id: this.data.id,
      outHeadId: this.data.outHeadId
    }).then(res => {
      // 差异签收时隐藏为赠品的商品
      if (this.data.signType === 1) {
        let outLines = []
        res.outLines.forEach(item => {
          if (item.itemLineType != 3) {
            outLines.push(item)
          }
        })
        res.outLines = outLines
      }
      let totalQty = 0
      for (let r = 0; r < res.outLines.length; r++) {
        totalQty += parseFloat(res.outLines[r].billQty)
      }
      this.setData({
        infoData: res,
        totalToSignQty: res.outHead.totalToSignQty,
        totalSignedQty: totalQty - res.outHead.totalToSignQty
      })
    })
  },
  getWarehouseColumns() {
    // 选择仓库
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      let defultWarehouse = {}
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          text: res[r].name,
          id: res[r].id
        })
        if (res[r].isDefault === 2) defultWarehouse = res[r]
      }
      this.setData({
        warehouseColumns: warehouseColumns,
        warehouseName: defultWarehouse.name,
        warehouseId: defultWarehouse.id
      })
    })
  },
  checkkWarehouse() {
    this.setData({
      show: true,
    })
  },
  onCancel() {
    this.setData({
      show: false
    })
  },
  onConfirm(event) {
    let detail = event.detail.value
    this.setData({
      show: false,
      warehouseName: detail.text,
      warehouseId: detail.id
    })
  },
  onChangeSignQty(e) {
    const qty = e.detail
    const index = e.currentTarget.dataset.index
    const key = `infoData.outLines[${index}].toSignQty`
    this.setData({
      [key]: qty
    })
    let totalQty = this.data.infoData.outLines.reduce((pre, cur) => {
      return pre + parseFloat(cur.toSignQty)
    }, 0)
    this.setData({
      totalToSignQty: totalQty
    })
  },
  comitSign() {
    let _this = this
    if (_this.data.signType === 2 && !_this.data.warehouseId) {
      wx.showToast({
        title: '请先选择签收仓库',
        icon: "none"
      })
      return
    }
    wx.showModal({
      title: '温馨提示',
      content: _this.data.signType === 2 ? `确定签收${_this.data.totalToSignQty}件商品？` : '确定差异签收商品?',
      success(res) {
        if (res.confirm) {
          let infoData = _this.data.infoData
          let outLines = infoData.outLines
          if (_this.data.signType === 1) {
            const params = {
              outHeadId: infoData.outHead.outHeadId,
              outBillNo: infoData.outHead.outBillNo,
              id: infoData.orderHead.id,
            }
            App.getHttp()
              ._post("/api/psi/myx/purOrder/close", params)
              .then((res) => {
                wx.showToast({
                  title: "差异签收成功！",
                  icon: 'success'
                });
                wx.navigateBack({
                  delta: 1
                })
              });
          } else {
            let exitDiff = false
            const params = {
              id: infoData.orderHead.id,
              invoiceSetsOfBooksId: infoData.orderHead.invoiceSetsOfBooksId,
              warehouseId: _this.data.warehouseId,
              outHeadId: infoData.outHead.outHeadId,
              outBillNo: infoData.outHead.outBillNo,
              lines: outLines.map(item => {
                if (!exitDiff) exitDiff = item.toSignQty > item.barCodeFlowResponseDTOList.length
                return {
                  outLineId: item.outLineId,
                  signQty: item.toSignQty
                }
              })
            };
            if (exitDiff) {
              wx.showModal({
                title: '是否出库',
                content: '当前商品签收数量大于条码数量，请确认是否继续签收?',
                success: (resM) => {
                  if (resM.confirm) {
                    App.getHttp()._post('/api/psi/myx/purOrder/sign', params).then(res => {
                      wx.showToast({
                        title: '签收成功',
                        icon: 'success'
                      })
                      wx.navigateBack({
                        delta: 1
                      })
                    })
                  }
                }
              })
            } else {
              App.getHttp()._post('/api/psi/myx/purOrder/sign', params).then(res => {
                wx.showToast({
                  title: '签收成功',
                  icon: 'success'
                })
                wx.navigateBack({
                  delta: 1
                })
              })
            }
          }
        } else if (res.cancel) {
          // console.log('用户点击取消')
        }
      }
    })
  },
  onClickCancel() {
    wx.navigateBack({
      delta: 1
    })
  },
  // 扫码扣减商品
  onClickReduceScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/scanPDA/signed/index?mode=reduce&id=${this.data.id}&outHeadId=${this.data.outHeadId}&signType=${this.data.signType}`,
        events: {
          acceptDataFromScanPDAPage: (data) => {
            if (data.refresh) {
              this.getQuestItems();
            }
          },
        }
      })
    } else {
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const addParams = {
              headId: this.data.infoData.outHead.outHeadId,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/myx/purOrder/cancelScanBarCode', addParams)
            this.getQuestItems();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail: (err) => {
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 扫码增加商品
  onClickAddScan(e) {
    if (e.detail.openPDA) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/scanPDA/signed/index?mode=add&id=${this.data.id}&outHeadId=${this.data.outHeadId}&signType=${this.data.signType}`,
        events: {
          acceptDataFromScanPDAPage: (data) => {
            if (data.refresh) {
              this.getQuestItems();
            }
          },
        }
      })
    } else {
      // 允许从相机和相册扫码
      wx.scanCode({
        success: async (res) => {
          if (res.errMsg === 'scanCode:ok') {
            const addParams = {
              headId: this.data.infoData.outHead.outHeadId,
              barCode: res.result
            };
            await App.getHttp()._post('/api/psi/myx/purOrder/scanBarCode', addParams)
            this.getQuestItems();
          } else {
            wx.showModal({
              title: '扫码失败',
              content: res.errMsg,
              showCancel: false
            })
          }
        },
        fail: (err) => {
          wx.showModal({
            title: '扫码失败',
            content: err.errMsg,
            showCancel: false
          })
        },
      })
    }
  },
  // 折叠监听
  onChangeCollape(event) {
    this.setData({
      collapseNames: event.detail,
    });
  },
})