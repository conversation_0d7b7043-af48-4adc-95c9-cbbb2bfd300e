<!-- pages_sub/pages/ccs/down/order/signed/index.wxml -->
<view class="page root-layout" id="root-layout">
  <scroll-view style="height: {{scrollHeightPx}}px" scroll-y="true">
    <!-- 头部通知 -->
    <view class="notice" id="notice-layout" wx:if="{{signType == 1}}">
      <van-notice-bar color="#faae16" background="#FFFBE6" left-icon="info-o" text="只能进行一次差异签收，请提前与上游沟通！" />
    </view>
    <!-- 商品区域 -->
    <view class="card-layout">
      <view class="card-bill">运单号  {{infoData.outHead.outBillNo}}</view>
      <view class="card-supplier">{{infoData.orderHead.vendorName}}</view>
      <van-collapse value="{{ collapseNames }}" bind:change="onChangeCollape">
        <view class="good-layout" wx:for="{{infoData.outLines}}" wx:key="index" wx:for-item="item">
          <view class="flex ">
            <image class="img" src="{{item.itemUrl}}"></image>
            <view class="flexbox flex content-layout">
              <view class="content-top">
                <view class="itemname two-line-ellipsis">{{item.itemName}}</view>
                <view class="cart-title specs single-line-ellipsis">{{item.specs}}</view>
                <view class="flex count-box justify-content-between">
                  <view class="itemprice">
                    <text class="price-symbol">¥</text>
                    {{item.pricecBillF||item.price}}
                  </view>
                  <view class="itemqty">x{{item.billQty}}</view>
                </view>
              </view>
              <view class="flex justify-content-between" wx:if="{{signType == 1}}">
                <view class="signed-label">已签收数量</view>
                <view class="signed-qty">{{item.billQty - item.toSignQty}}</view>
              </view>
              <view class="flex justify-content-between edit-layout" wx:if="{{signType == 2}}">
                <view class="signed-label">本次签收数量</view>
                <van-stepper value="{{ item.toSignQty }}" min="{{item.barCodeFlowResponseDTOList?item.barCodeFlowResponseDTOList.length:0}}" data-index="{{index}}" max="{{item.billQty}}" catch:change="onChangeSignQty" />
              </view>
            </view>
          </view>
          <van-collapse-item name="{{'collapse'+index}}" wx:if="{{item.barCodeFlowResponseDTOList}}">
            <view slot="title">已扫码信息({{item.barCodeFlowResponseDTOList.length}})</view>
            <view slot="value">时间</view>
            <view class="flex font-sub-hint m-b-12" wx:for="{{item.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex">
              <view class="bar-code">条码：{{barCodeItem.barCode}} 扫码时间：{{barCodeItem.createTime}}</view>
            </view>
          </van-collapse-item>
        </view>
      </van-collapse>
    </view>
    <view class="total-layout" wx:if="{{signType == 1}}">
      <van-field readonly label="已签收总数量" value="{{totalSignedQty}}" input-align="right"></van-field>
      <van-field readonly error label="差异签收数量" value="{{totalToSignQty}}" input-align="right"></van-field>
    </view>
    <view class="total-layout" wx:if="{{signType == 2}}">
      <van-field readonly title-width="204rpx" label="本次签收总数量" value="{{totalToSignQty}}" input-align="right"></van-field>
      <van-cell-group border="{{false}}">
        <van-cell title="仓库" value="{{warehouseName}}" is-link border="{{false}}" bind:click="checkkWarehouse" />
      </van-cell-group>
    </view>
  </scroll-view>
  <view class="footer-layout" id="footer-layout">
    <scan-btns wx:if="{{signType == 2}}" routeName="DownOrderSigned" catch:clickReduceScan="onClickReduceScan" catch:clickAddScan="onClickAddScan"/>
      <!-- <view class="flex scan-btn-layout" wx:if="{{signType == 2}}">
        <view class="flexbox scan-btn" catchtap="onClickReduceScan">扫码扣减商品</view>
        <view class="scan-holder"></view>
        <view class="flexbox scan-btn" catchtap="onClickAddScan">扫码添加商品</view>
      </view> -->
      <view class="footer">
        <view class="footer-btn" bindtap="onClickCancel">取消</view>
        <view class="footer-btn confirm-btn" bindtap="comitSign">确定</view>
      </view>
  </view>
  <van-popup show="{{ show }}" round position="bottom" custom-style="height: 60%" bind:close="onClose">
    <van-picker columns="{{ warehouseColumns }}" show-toolbar title="选择仓库" bind:cancel="onCancel" bind:confirm="onConfirm" />
  </van-popup>
</view>