/* pages/ccs/outStockDetail/index.wxss */
.root-layout{
  background: rgba(0,0,0,0.04);
  margin-bottom: 120rpx;
}
.status-layout{
  display: flex;
  justify-content: space-between;
  color: #fff;
  padding: 24rpx;
  font-size: 24rpx;
  background: rgba(231, 12, 27, 0.8);
}
.status-layout .statu{
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #00b9c3;
  line-height: 44rpx;
  font-weight: 400;
}
.status-layout .des{
  margin-top: 8rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.75);
  line-height: 40rpx;
  font-weight: 400;
}
.address-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
}
.address-item .left {
  padding-right: 24rpx;

}
.address-item .left image {
  width: 48rpx;
  height: 48rpx;
}
.address-item .right {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
}
.address-item .right .right-left .top {
  display: flex;
  font-size: 28rpx;
  line-height: 44rpx;
  color:rgba(0,0,0,0.85);
}
.address-item .right .right-left .top .phone {
  margin: 0 8rpx 0 44rpx;
}
.address-item .right .right-left .top .tag {
  padding: 0 12rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: #fff;
  text-align: center;
  background-color: #00b9c3;
  border-radius: 4rpx;
}
.address-item .right .right-left .bottom {
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: rgba(0, 0, 0, 0.75);
}
.address-item .right .right-right {
  padding-left: 24rpx;
}
.address-item .right .right-right image {
  width: 48rpx;
  height: 48rpx;
}
.infocard {
  background-color: #fff;
  padding: 20rpx;
}
.price {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-content: center;
  background-color: #fff;
  font-size: 28rpx;
}
.time {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-content: center;
  font-size: 28rpx;
  color: #cccccc;
  background-color: #fff;

}
.c-red {
  color: #00b9c3;
}
.foot-layout {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  padding: 0 24rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}
.foot-layout .total {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}

.foot-layout .total .uom {
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: #F97D4E;
  line-height: 44rpx;
  font-weight: 500;
}

.foot-layout .cancel {
  width: 136rpx;
  height: 56rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.15);
  border-radius: 8rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 56rpx;
  font-weight: 400;
}
