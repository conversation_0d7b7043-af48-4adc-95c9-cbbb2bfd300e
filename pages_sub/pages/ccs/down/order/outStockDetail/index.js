// pages/ccs/outStockDetail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    infoData: {}
  },
  onLoad: function (options) {
    if (options.id) {
      this.getQuestItems(`/myx/ccs-mobile-web/order/getMobileDeliveredBillDetailResp/${options.id}`, options.id, options.sourceSystem)
    }
  },
  getQuestItems(url, mergeHeadId, sourceSystem) {
    App.getHttp()._post(url, {
      mergeHeadId,
      sourceSystem
    }).then(res => {
      this.setData({
        infoData: res
      })
    })
  },
  checkLogistics(e) {
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/order/logistics/index?billNo=${e.currentTarget.dataset.id}`,
    })
  }
})