<!--pages/ccs/outStockDetail/index.wxml-->
<view class="page root-layout">
  <view class="status-layout">
    <view>出库单编号：{{infoData.billNo}}</view>
    <view>{{infoData.appQueryStatName}}</view>
  </view>
  <view class="infocard">
    <view class="address-item">
      <view class="right">
        <view class="right-left">
          <view class="top">
            <view class="name">
              {{ infoData.contactPerson }}
            </view>
            <view class="phone">
              {{ infoData.telPhone }}
            </view>
            <!-- <view wx:if="{{item.isDefault === 2}}" class="tag">
                默认
              </view> -->
          </view>
          <view class="bottom">
            {{infoData.contactAddr}}
          </view>
        </view>
      </view>
    </view>
  </view>
  <total-card sourceData="{{infoData.lineList}}" />
  <view class="time">
    <view>发货时间</view>
    <view>{{infoData.creationDate}}</view>
  </view>
  <view class="foot-layout flex align-items-center" >
    <view class="flexbox total">商品总额：<text class="uom">¥ {{infoData.amountTotal}}</text></view>
    <view class="cancel" data-id="{{infoData.billNo}}" bindtap="checkLogistics">查看物流</view>
  </view>
</view>