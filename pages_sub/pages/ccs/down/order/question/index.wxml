<!--pages/ccs/questDetail/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="page root-layout">
  <view class="status-layout">
    <view class="statu">{{typeName}}</view>
    <view class="des">{{typeDes}}</view>
  </view>
  <total-card wx:if="{{typeName!=='商品已发货'}}"  sourceData="{{infoData}}"/>
  <view class="total-layout">
      <text class="txt">共{{infoData.totalQty}}件商品</text>
      <text class="totaltxt">合计：</text>
      <text class="totalprice">¥{{wxsUtil.moneyFormat(infoData.totalAmount)}}</text>
    </view>
</view>
