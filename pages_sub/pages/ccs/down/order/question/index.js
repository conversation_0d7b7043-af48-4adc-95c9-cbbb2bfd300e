// pages/ccs/questDetail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    typeName:'查看详情',
    // typeDes:'请核对,如有疑问及时联系经销商.',
    infoData: {}
  },
  onLoad: function (options) {
    if (options.id) {
      let url = ''
      let typeName = ''
      switch (options.type) {
        case 'unaudited':
          typeName = '待审核'
          url = '/api/psi/myx/purOrder/state/getUnauditedItems'
          break;
        case 'undelivered':
          typeName = '商品待发货'
          url = '/api/psi/myx/purOrder/state/getUndeliveredItems'
          break;
        // case 'notdelivered':
        //   typeName = '商品无法发货'
        //   url = 'myx/ccs-mobile-web/order/getNotDeliverItems'
        //   break;
        case 'unsign':
          typeName = '商品待签收'
          url = '/api/psi/myx/purOrder/state/unsigned'
          break;
        // case 'signed':
        //   typeName = '商品已签收'
        //   url = 'myx/ccs-mobile-web/order/getSignedItems'
        //   break;
        // case 'reject':
        //   typeName = '商品已拒收'
        //   url = 'myx/ccs-mobile-web/order/getRejectedItems'
        //   break;
        // case 'delivered':
        //     typeName = '商品已发货'
        //     url = `/myx/ccs-mobile-web/order/getMobileDeliveredBillDetailResp/${options.id}`
        //     break;  
        default:
          break;
      }
      this.getQuestItems(url, options.id)
      this.setData({
        typeName:typeName,
      })
    }
  },
  getQuestItems(url, id) {
    App.getHttp()._post(url, {
      id
    }).then(res => {
      const data = {
        orderLines: res.orderLines ,
        totalQty: res.totalQty ,
        totalAmount: res.totalAmount 
      }
      this.setData({
        infoData: data
      })
    })
  }
})