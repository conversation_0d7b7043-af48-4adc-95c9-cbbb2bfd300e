<wxs src="./index.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
    <salesOutItem isAudit="{{true}}" showAmount="{{false}}" isFormOrderList="{{true}}" bind:changeCancel="changeCancel" bind:changeAudit="changeAudit" bind:changeWarehouse="changeWarehouse" id="confirmList" orderHead="{{orderData.orderHead}}" orderLineList="{{orderData.orderLines}}" />
    <view class="footer">
        <view class="footer-left">
            <view class="totalCount">
                共{{wxsUtil.getTotalCount(confirmLineList)}}件
            </view>
            <view class="totalPrice row-layout">
                <view class="price-label">合计:</view>
                <view class="row-layout price-content">
                    <view class="price-symbol">￥</view>
                    <view class="price-text">{{wxsUtil.getTotalPrice(confirmLineList)}}</view>
                </view>
            </view>
        </view>
        <view class="footer-btn" bind:tap="onClickConfirm">评审出库</view>
    </view>
</view>