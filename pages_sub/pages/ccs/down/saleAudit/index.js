// pages_sub/pages/ccs/down/saleAudit/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderData: {},
    orderId: '',
    confirmLineList: [],
    warehouseId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      orderId: options.id
    })
    this.getOrderDetail()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  getOrderDetail() {
    const url = '/api/psi/myx/purOrder/state/getStateDetail'
    const params = {
      id: this.data.orderId
    }
    App.getHttp()._post(url, params).then(res => {
      this.setData({
        orderData: res,
        confirmLineList: res.orderLines.map(item => {
          return {
            id: item.id,
            headId: item.headId,
            currAuditQty: item.toAuditQty,
            curAuditPrice: item.applyPrice,
            currCancelQty: 0,
            warehouseId: '',
            toAuditQty: item.toAuditQty
          }
        })
      })
    })
  },
  // 修改仓库
  changeWarehouse(e) {
    let confirmList = [...e.detail]
    this.setData({
      confirmLineList: confirmList
    })
  },
  // 修改取消数量回调
  changeCancel(e) {
    console.log(e.detail)
    let confirmList = [...e.detail]
    this.setData({
      confirmLineList: confirmList
    })
  },
  // 修改审批数量回调
  changeAudit(e) {
    console.log(e.detail)
    let confirmList = [...e.detail]
    this.setData({
      confirmLineList: confirmList
    })
  },
  // 确认审核
  onClickConfirm() {
    // 判断是否没选择仓库
    let flag = false
    let itemObj = this.data.confirmLineList.find(item => item.currCancelQty != item.toAuditQty && item.toAuditQty > 0 && !item.warehouseId)
    if(itemObj) {
      wx.showToast({
        title: '请先选择仓库!',
        duration: 1000,
        icon: 'none'
      })
      return false
    }
    // 取消数量和审批数量不能大于申请数量
    for(let i = 0; i< this.data.confirmLineList.length; i++) {
      let model = this.data.confirmLineList[i]
      let applyQty = this.data.orderData.orderLines.find(item => item.id === model.id).applyQty
      if((Number(model.currCancelQty) + Number(model.currAuditQty)) > applyQty) {
        flag = true
        wx.showToast({
          title: '取消数量和审批数量之和不能大于开单数量!',
          duration: 1000,
          icon: 'none'
        })
        break
      }
    }
    if(flag) {
      return false
    }
    let params = {
      orderHead: {
        id: this.data.orderData.orderHead.id
      },
      orderLines: this.data.confirmLineList
    }
    const url = '/api/psi/saleOrder/myx/audit'
    console.log(params);
    App.getHttp()
      ._post(url, params)
      .then((res) => {
        wx.showToast({
          title: "确认成功",
          duration: 1000,
          icon: "success",
        });
        setTimeout(() => {
          wx.navigateBack({
            delta: 1,
          });
        }, 2000);
      });
  }
})