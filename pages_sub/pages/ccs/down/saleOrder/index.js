// pages_sub/pages/ccs/down/saleOrder/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    queryState: '1',
    typeList: [
      { title:'全部',name:'1' },
      { title: '待审核', name: '2' },
      { title: '待发货', name: '3' },
      { title: '已关闭', name: '5' }
    ],
    keyword: '',
    pageIndex: 1,
    pageSize: 10,
    dataList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#tabsView').boundingClientRect()
        query.select('#filterSearch').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].bottom - App.globalData.deviceBottomOccPx
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getOrderList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getOrderList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 加载更多事件
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getOrderList()
  },
  // 确认搜索事件
  onSearch() {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  // 获取列表方法
  getOrderList() {
    const supInfo = wx.getStorageSync('supInfo')
    const url = '/api/psi/myx/purOrder/state/page'
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        vendorSetsOfBooksId: supInfo.setsOfBooksId,
        queryState: this.data.queryState,
        keyWord: this.data.keyword,
      }
    }
    let arr = [...this.data.dataList]
    App.getHttp()._post(url, params).then(res => {
      let dataList = this.data.pageIndex === 1 ? [] : this.data.dataList
      if(res && res.length > 0) {
        this.setData({
          dataList: dataList.concat(res)
        })
      } else {
        this.setData({
          dataList
        })
      }
    })
  },
  // 切换导航栏
  tapNavi(e) {
    this.setData({
      queryState: e.detail.name,
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  // 点击审核跳转
  onClickAudit(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/saleAudit/index?id=' + id
    })
  },
  // 跳转详情
  onClickItem(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/saleDetail/index?id=' + id + '&stat=' + this.data.queryState
    })
  },
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  }
})