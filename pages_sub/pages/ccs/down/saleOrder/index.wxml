<view class="root-layout bg-04">
    <view class="filter-search" id="filterSearch">
        <van-search value="{{keyword}}" bind:search="onSearch" bind:change="onChangeKeyword" use-action-slot bind:clear="onSearch" placeholder="搜索订单" >
            <view slot="action" class="search-right" bind:tap="onSearch">搜索</view>
        </van-search>
    </view>
    <van-tabs id="tabsView" class="tab-layout" active="{{queryState}}" bind:change="tapNavi" title-active-color="#0278ff">
        <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
            <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
        </block>
    </van-tabs>
    <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
        <block wx:if="{{dataList.length > 0}}">
            <view class="listItemLayout" wx:for="{{dataList}}" wx:key="id">
                <salesOutItem orderHead="{{item.orderHead}}" orderLineList="{{item.orderLines}}" stat="{{queryState}}" isFormOrderList="{{true}}" isAudit="{{false}}" data-id="{{item.orderHead.id}}" bindtap="onClickItem">
                    <view class="audit-btn" slot="footer" wx:if="{{(queryState == 1 || queryState == 2) && item.orderHead.totalToAuditQty > 0}}">
                        <van-button size="small" data-id="{{item.orderHead.id}}" catchtap="onClickAudit">审核</van-button>
                    </view>
                </salesOutItem>
            </view>
        </block>
        <view class="m-t-25p" wx:else>
          <noneView ></noneView>
        </view>
    </listView>
</view>