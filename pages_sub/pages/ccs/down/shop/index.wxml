<!--pages_sub/pages/ccs/down/shop/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" id="page-layout">
  <!-- 顶部标题,返回 -->
  <view class="navigation-layout" style="padding-top: {{navigationTop}}px; height: {{navigationHeight}}px; padding-right: {{navigationPaddingRight}}px">
    <view class="flex  justify-content-between">
      <view class="flexbox flex align-items-center" bindtap="onClickBack">
        <image class="back" src="/asset/imgs/arrow-back.png"></image>
        <!-- <image class="back" src="/asset/svgs/free.svg"></image> -->
        <view class="title">购物车</view>
      </view>
      <view class="edit flex align-items-center" catchtap="onClickOpenEdit">
        <image class="manage" src="/asset/imgs/manage.png" mode="aspectFit" />
        {{shopSup.openEdit?"退出管理":"管理"}}
      </view>
    </view>
  </view>
  <view class="line"></view>
  <!-- 搜索区域 -->
  <!-- <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{false}}" bind:onSearchTxtClick="onSearchClick" placeholder="请输入商品名称/商品编码" /> -->
  <!-- 提示区域 -->
  <!-- <view class="warining-layout flex align-items-center">
    <image class="warining-icon" src="/asset/imgs/purchase/warning.png"></image>
    <text class="warining-txt">只能选择同一供应商下的商品进行下单！</text>
  </view> -->
  <!-- 滑动区域 -->
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullRefresh" id="list-layout">
    <view class="list-layout" wx:if="{{listData.length>0}}">
      <view class="adapter-layout" wx:for="{{listData}}" wx:for-item="rootItem" wx:key="rootIndex" wx:for-index="rootIndex">
        <!-- 供应商 -->
        <view class="vendor-layout font-s32-lh48 flex align-items-center">
          <image class="radio-img m-r-24" src="{{rootItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="1" data-root-index="{{rootIndex}}" />
          <view class="flexbox">
            <text  class="group-content shoppingCartItemType">{{shoppingCartItemTypeName[rootItem.shoppingCartItemType]}}</text>
            <text wx:if="{{rootItem.shoppingCartItemType !== 2}}" class="group-content itemTypeName">{{rootItem.itemTypeName || ''}}</text>
            <text class="group-content taxCombination">税率：{{rootItem.taxCombination}}</text>
            <view class="group-content saleOrgName">{{rootItem.saleOrgName}}</view>
          </view>
          
        </view>
        <!-- 政策,商品内容区域 -->
        <view class="card-layout" wx:for="{{rootItem.policyCartList}}" wx:for-item="cartTypeItem" wx:key="cartTypeIndex" wx:for-index="cartTypeIndex">
          <block  wx:for="{{cartTypeItem.shoppingCartList}}" wx:for-item="cartItem" wx:key='cartId' wx:for-index="cartIndex">
            <!-- 单品 -->
            <block>
              <!-- 商品区域 -->
              <van-swipe-cell right-width="{{ 75 }}" >
                <view class="good-layout flex align-items-center" data-item-id="{{cartItem.itemId}}" data-cust-info="{{rootItem}}">
                    <view class="radio-img">
                      <image wx:if="{{(cartItem.isUsable === 2 && cartItem.applyPrice > 0) || shopSup.openEdit}}" class="radio-img" src="{{cartItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="3" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}"  />
                      <image wx:else class="radio-img radio-d-img" src="/asset/svgs/radioD.svg" data-checked-layer="3" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}"/>
                    </view>
                    <view class="img-layout" data-item-id="{{cartItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickGood">
                      <image class="itemurl" wx:if="{{cartItem.itemUrl}}" src="{{cartItem.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                      <image class="itemurl"  wx:else src="/asset/imgs/bg_shop.png" mode="aspectFit"></image>
                      <view wx:if="{{cartItem.isUsable === 1 || cartItem.applyPrice <= 0}}" class="is-usable-1">失效</view>
                      <image class="noqty" wx:if="{{cartItem.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
                    </view>
                    <view class="flexbox" data-item-id="{{cartItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickGood">
                      <view class="itemname two-line-ellipsis">{{cartItem.itemName}}</view>
                      <view class="itemcode single-line-ellipsis">{{cartItem.specs}}</view>
                      <view class="itemprice-box flex align-items-end justify-content-between" catchtap="onCatchNull">
                        <view class="itemprice">
                          <text class="uom">¥</text>{{cartItem.applyPrice}}
                        </view>
                        <van-stepper plus-class="plusclss" minus-class="plusclss" input-class="inputclss" integer disabled="{{cartItem.isUsable !== 2 || cartItem.applyPrice <= 0}}" 
                        step="{{(cartItem['isBulkOrder'] === 2 && cartItem['shoppingCartItemType'] !== 2) ? cartItem.itemNumber : 1}}"
                         min="{{(cartItem['isBulkOrder'] === 2 && cartItem['shoppingCartItemType'] !== 2) ? cartItem.itemNumber : 1}}" button-size="24" input-width="48"  data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" value="{{ cartItem.purchaseQty }}" catch:change="onStepChange" />
                      </view>
                    </view>
                </view>
                <view class="serial-number" wx:if="{{rootItem.shoppingCartItemType === 2 && cartItem.serialNumber}}">序列号：{{cartItem.serialNumber}}</view>
                <!-- <view class="serial-number">序列号：1254669878</view> -->
                <view slot="right" class="swipe-right flex">
                  <!-- <view class="flexbox collect-button" data-item-id="{{cartItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickCollect">收藏</view> -->
                  <view class="flexbox delete-button" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" bindtap="onClickDelete">删除</view>
                </view>
              </van-swipe-cell>
                <!-- 起订量,单客限量, 已购数量 -->
                <view class="limit-layout flex justify-content-between">
                  <view wx:if="{{cartItem['isBulkOrder'] === 2 && cartItem['shoppingCartItemType'] !== 2}}" class="flexbox">整托数量 {{cartItem.itemNumber}}</view>
                  <!-- <view class="flexbox">库存: {{cartItem.itemNumber}}</view> -->
                </view>
            </block>
          </block>
        </view>
      </view>
    </view>
    <view class="m-t-25p" wx:else>
      <no-product noneTxt="快去添加商品吧" />
    </view>
  </listView>
  <!-- 底部结算按钮区域 -->
  <view class="foot-layout flex align-items-center" id="foot-layout">
    <view class="all flex align-items-center">
      <image class="radio-img" src="{{shopSup.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="0" />全选
    </view>
    <view class="flexbox total">
      <block wx:if="{{!shopSup.openEdit}}">
        <text class="txt">合计：</text><text  class="uom">¥ </text><text class="amount">{{shopSup.totalMoney}}</text>
      </block>
    </view>
    <view wx:if="{{shopSup.openEdit}}" class="end-active-red" catchtap="onClickFillOrDeleteOrder">{{'删除'}}</view>
    <view wx:else class="{{shopSup.totalMoney>0 ? 'end-active':'end'}}" catchtap="onClickFillOrDeleteOrder">{{'去结算('+shopSup.totalCount+')'}}</view>
  </view>
</view>
<van-action-sheet show="{{ showDelActionView }}" actions="{{ delActions }}" cancel-text="取消" bind:select="onCloseDelActionView" bind:cancel="onCloseDelActionView" description="确定要删除商品吗?" />