/* pages_sub/pages/ccs/down/shop/index.wxss */
.root-layout {
  background-color: #F2F2F2;
  width: 100%;
  height: 100vh;
}
::-webkit-scrollbar{
  width: 0;
  height: 0;
  color: transparent;
}
.navigation-layout {
  padding-left: 24rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 52rpx;
  background-color: white;
}

.navigation-layout .back {
  width: 40rpx;
  height: 40rpx;
}

.navigation-layout .title {
  font-size: 36rpx;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #000000;
  margin-left: 12rpx;
}

.navigation-layout .edit {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #000000;
  font-weight: 400;
  margin-right: 32rpx;
}

.navigation-layout .manage {
  width: 36rpx;
  height: 36rpx;
  margin-right: 4rpx;
}

.tab-layout {
  background-color: white;
}

.warining-layout {
  height: 80rpx;
  background: #FFFBE6;
  padding-left: 32rpx;
}

.warining-layout .warining-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.warining-layout .warining-txt {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FAAE16;
}

.foot-layout {
  position: fixed;
  right: 0;
  left: 0;
  bottom: env(safe-area-inset-bottom);
  padding-left: 24rpx;
  height: 100rpx;
  background-color: #FFFFFF;
}

.foot-layout .all {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 400;
}

.foot-layout .total {
  
  text-align: right;
  margin: 0 34rpx;
}

.foot-layout  .total .amount{
  font-family: SanFranciscoText-Medium;
  font-size: 40rpx;
  color: #FF4A4D;
  font-weight: 500;
  margin: 0 4rpx;
}
.foot-layout  .total .uom {
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: #F97D4E;
  font-weight: 500;
}

.foot-layout  .total .txt {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #242424;
  font-weight: 400;
}

.foot-layout .end {
  width: 200rpx;
  background: rgba(0, 0, 0, 0.20);
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 80rpx;
  margin-right: 32rpx;
  border-radius: 8rpx;
}

.foot-layout .end-active {
  width: 200rpx;
  background: #00b9c3;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 80rpx;
  margin-right: 32rpx;
  border-radius: 8rpx;
}
.foot-layout  .end-active-red{
  width: 200rpx;
  background: #FF4A4D;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 400;
  text-align: center;
  line-height: 80rpx;
  margin-right: 32rpx;
  border-radius: 8rpx;
}

.radio-img {
  width: 40rpx;
  height: 40rpx;
}
.radio-d-img {
  width: 30rpx;
  height: 30rpx;
}

.list-layout {
  margin-top: 24rpx;
  padding: 0 24rpx;
}

.list-layout .adapter-layout {
  padding-bottom: 24rpx;
}

/* .list-layout .adapter-layout .vendor-layout .m-r-24 {
  align-items: center;
} */
/* .list-layout .adapter-layout .group-content {

} */
.list-layout .adapter-layout .taxCombination {
  margin-left: 30rpx;
}
.list-layout .adapter-layout .shoppingCartItemType {
  margin-right: 30rpx;
  padding: 0 6px;
  font-size: 28rpx;
  color: #faae16;
  background: #fffbe6;
  border: 1px solid #ffe591;
  border-radius: 4px;
}
.list-layout .adapter-layout .itemTypeName {
  vertical-align: top;
}
.list-layout .adapter-layout .saleOrgName {
  color: rgb(94, 93, 93);
  font-size: 28rpx;
}
.list-layout .adapter-layout .card-layout {
  padding: 24rpx 0 24rpx 24rpx;
  margin-top: 12rpx;
  background-color: white;
  border-radius: 8rpx;
}
.list-layout .adapter-layout .card-layout .serial-number {
  color: rgb(94, 93, 93);
  font-size: 28rpx;
  margin-top: 10rpx;
}
.list-layout .adapter-layout .card-layout .swipe-right {
  position: relative;
  width:140rpx;
  height: 176rpx;
  color: #fff;
  text-align: center;
  line-height:176rpx;
}
.list-layout .adapter-layout .card-layout .swipe-right .collect-button {
  height: 100%;
  background-color: #FAAE16;
}
.list-layout .adapter-layout .card-layout .swipe-right .delete-button {
  height: 100%;
  background-color: #FF4A4D;
}
.list-layout .adapter-layout .card-layout .policy-info-layout .item-com-type {
  padding: 0 8rpx;
  height: 40rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  border-radius: 8rpx;
  line-height: 40rpx;
  margin-right: 8rpx;
}
.list-layout .adapter-layout .card-layout .policy-info-layout{
  padding-right: 24rpx;
}
.list-layout .adapter-layout .card-layout .policy-info-layout .hint {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 32rpx;
}

.list-layout .adapter-layout .card-layout .policy-info-layout .des-red {
  color: #FF4A4D;
}


.adapter-layout .collapse-layout .slotitle {
  font-family: PingFangSC-Medium;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.adapter-layout .collapse-layout:after {
  border-bottom-width: 16rpx;
  border-bottom-color: rgb(245, 245, 245)
}

.adapter-layout .collapse-layout .inner-top {
  height: 88rpx;
}

.adapter-layout .collapse-layout .inner-top .title {
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  margin-left: 16rpx;
  font-weight: 500;
}

.adapter-layout .collapse-layout .inner-top .edit {
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: #00b9c3;
  font-weight: 500;
}

.rule-layout {
  padding: 22rpx 0;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.75);
}

.rule-layout .type {
  margin-right: 24rpx;
  width: 72rpx;
  height: 40rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #F6A52C;
  text-align: center;
  line-height: 40rpx;
  font-weight: 400;
  background: rgba(246, 165, 44, 0.06);
  border: 1rpx solid #F6A52C;
  border-radius: 4rpx;
}

.rule-layout .des {
  margin-top: 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
  font-weight: 400;
}

.rule-layout .des-limit {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
  font-weight: 400;
}

.rule-layout .limit {
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: #00b9c3;
  font-weight: 400;
}

.list-layout .adapter-layout .card-layout .good-layout {
  height: 176rpx;
  box-sizing: border-box;
  margin-top: 32rpx;
  padding-right: 24rpx;
}

.list-layout .adapter-layout .card-layout .good-layout .img-layout {
  position: relative;
  width: 176rpx;
  height: 176rpx;
  margin: 0 24rpx;
}
.list-layout .adapter-layout .card-layout .good-layout .img-layout .itemurl {
  width: 176rpx;
  height: 176rpx;
}
.list-layout .adapter-layout .card-layout .good-layout .img-layout .is-usable-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  line-height: 176rpx;
  text-align: center;
  color: #fff;
  background: rgba(0, 0, 0, 0.2);
}
.list-layout .adapter-layout .card-layout .good-layout .img-layout .noqty {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  width: 128rpx;
  height: 128rpx;
}

.list-layout .adapter-layout .card-layout .good-layout .itemname {
  height: 72rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #242424;
  line-height: 36rpx;
  font-weight: 400;
}
.list-layout .adapter-layout .card-layout .good-layout .itemcode{
  height: 35rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 35rpx;
}
.list-layout .adapter-layout .card-layout .good-layout .itemprice-box {
  height: 69rpx;
}
.list-layout .adapter-layout .card-layout .good-layout .itemprice-box .des{
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 32rpx;
}
.list-layout .adapter-layout .card-layout .good-layout .itemprice-box .count{
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 32rpx;
}
.list-layout .adapter-layout .card-layout .good-layout .itemprice-box .itemprice {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #FF4A4D;
  font-weight: 600;
}
.list-layout .adapter-layout .card-layout .good-layout .itemprice-box .itemprice .standprice{
  text-decoration: line-through;
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #707070;
  font-weight: 400;
}

.list-layout .adapter-layout .card-layout .good-layout .uom {
  font-family: PingFangSC-Medium;
  font-size: 20rpx;
  color: #F97D4E;
  font-weight: 500;
}
.list-layout .adapter-layout .card-layout  .limit-layout{
  margin-top: 24rpx;
  margin-left: 64rpx;
  padding-right: 24rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 40rpx;
}
.list-layout .adapter-layout .card-layout .group-layout{
  margin-top: 24rpx;
  padding-right: 24rpx;
}
.list-layout .adapter-layout .card-layout .group-layout  .hint {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 40rpx;
  margin-left: 64rpx;
  margin-right: 16rpx;
}

.list-layout .adapter-layout .card-layout .group-layout  .itemprice {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #FF4A4D;
  font-weight: 600;
}

.list-layout .adapter-layout .card-layout .group-layout  .uom {
  font-family: PingFangSC-Medium;
  font-size: 20rpx;
  color: #F97D4E;
  font-weight: 500;
}
.good-fee-total {
  width: 100%;
  text-align: right;
  font-family: SanFranciscoText-Semibold;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 48rpx;
  font-weight: 600;
}

.pkg-layout {
  height: 88rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
}

.plusclss {
  width: 56rpx !important;
  height: 56rpx !important;
  background-color: #F0F0F0 !important;
  font-family: PingFangSC-Regular, PingFang SC;
  color: #242424 !important;
  font-weight: 400 !important;
  border-radius: 8rpx !important;
}

.inputclss {
  width: 80rpx !important;
  height: 56rpx !important;
  background: #F0F0F0 !important;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 28rpx;
  font-weight: 400 !important;
  color: #242424 !important;
  border-radius: 8rpx !important;
}