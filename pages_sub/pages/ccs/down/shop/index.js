// pages_sub/pages/ccs/down/shop/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   * 整个购物车模型,  分组(供应方+产品组+渠道)-->类型(满赠,特价,套餐,常规等)-->商品
   * rootIndex分组角标 , tIndex:订单类型角标   cIndex商品角标
   */
  data: {
    listViewH: 300,
    tabs: [
      { title: '全部', name: "-1" },
      { title: '常规', name: "0" },
      { title: '满减', name: "1" },
      { title: '满折', name: "2" },
      { title: '一口价', name: "3" },
      { title: '满赠', name: "4" },
      { title: '众筹', name: "5" }
    ],
    shoppingCartItemTypeName: {
      1: '成品',
      2: '配件',
      3: '促销品',
    },
    orderType: '-1',
    navigationTop: '',
    navigationHeight: '',
    shopSup: {
      openEdit: false,
      checked: false,
      checkNumber: 0,
      totalMoney: '0.00',
      totalCount: 0,
    },
    listData: [],
    keyword:'',
    showDelActionView: false,
    preDeleteIds:[],
    delActions:[{name:'删除',color: '#FF4A4D'}]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.setData({
      navigationTop: App.globalData.menuButtonClientReact.top,
      navigationPaddingRight: App.globalData.windowWidth - App.globalData.menuButtonClientReact.left,
      navigationHeight: App.globalData.menuButtonClientReact.height + 6 //设计的6px
    })
    this.initCartList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#list-layout').boundingClientRect()
    query.select('#foot-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH:  res[1].top - res[0].top
      })
    })
  },
  /**
   * 当小程序从前台进入后台，会触发 onHide
   */
  onHide: function () {
    this.data.shopSup.openEdit = false
    this.setData({
      shopSup: this.data.shopSup
    })
  },
  onClickBack() {
    wx.navigateBack({
      delta: 1
    })
  },
  onSearchClick(e) {
    this.data.keyword = e.detail
    this.initCartList()
   },
   // 初始化商品数据
  initCartList() {
    const params = {
      orderType:this.data.orderType==-1?'':this.data.orderType,
      invoiceSetsOfBooksId: wx.getStorageSync('checkSetsOfBooksId'),
      keyword:this.data.keyword
    }
    App.getHttp()._post('/api/vcs/shoppingCart/getPolicyCartList', params).then(res => {
      let tempList = []
      if (res) {
        // tempList = res
        for (let index = 0; index < res.length; index++) {
          const groupInfo = res[index]
          groupInfo.checked = false
          groupInfo.checkNumber = 0
          groupInfo.usableDataNum = 0
          groupInfo.dataNum = 0
          for (let iindex = 0; iindex < groupInfo.policyCartList.length; iindex++) {
            let pData = groupInfo.policyCartList[iindex]
            pData.checked = false
            for (let iiindex = 0; iiindex < pData.shoppingCartList.length; iiindex++) {
              let itemData = pData.shoppingCartList[iiindex]
              itemData.checked = false
              groupInfo.dataNum += 1
              if (itemData.isUsable === 2 && itemData.applyPrice > 0) {
                groupInfo.usableDataNum += 1
              }
            }
          }
        }
        tempList = res
      }
      this.data.shopSup.totalMoney = '0.00'
      this.data.shopSup.totalCount = 0
      this.data.shopSup.checked = false
      this.data.shopSup.checkNumber = 0
      this.data.shopSup.openEdit = false
      this.setData({
        listData: tempList,
        shopSup: this.data.shopSup
      })
    })
  },
  // 还原商品数据
  initCartData() {
    let tempList = []
    let res = this.data.listData
    if (res) {
      // tempList = res
      for (let index = 0; index < res.length; index++) {
        const groupInfo = res[index]
        groupInfo.checked = false
        groupInfo.checkNumber = 0
        groupInfo.usableDataNum = 0
        groupInfo.dataNum = 0
        for (let iindex = 0; iindex < groupInfo.policyCartList.length; iindex++) {
          let pData = groupInfo.policyCartList[iindex]
          pData.checked = false
          for (let iiindex = 0; iiindex < pData.shoppingCartList.length; iiindex++) {
            let itemData = pData.shoppingCartList[iiindex]
            itemData.checked = false
            groupInfo.dataNum += 1
            if (itemData.isUsable === 2 && itemData.applyPrice > 0) {
              groupInfo.usableDataNum += 1
            }
          }
        }
      }
      tempList = res
    }
    this.data.shopSup.totalMoney = '0.00'
    this.data.shopSup.totalCount = 0
    this.data.shopSup.checked = false
    this.data.shopSup.checkNumber = 0
    this.setData({
      listData: tempList,
      shopSup: this.data.shopSup
    })
  },
  // 顶部下拉更新数据
  onPullRefresh() {
    this.onShow()
  },
  // 点击管理切换管理模式
  onClickOpenEdit() {
    this.data.shopSup.openEdit = !this.data.shopSup.openEdit
    this.setData({
      shopSup: this.data.shopSup,
    })
    this.initCartData()
  },
  // 去结算和批量删除处理
  onClickFillOrDeleteOrder() {
    // isPersent 2是赠品  1非赠品, null非赠品 后端搞得什么飞机玩意
    // 控制下结算状态下选中价钱大于0的商品
    const list = this.data.listData
    let shoppingCartList = []
    let groupList = []
    let preDeleteIds = []
    let priceZero = false;
    let priceZeroName = '';
    for (let index = 0; index < list.length; index++) {
      let groupObj = list[index]
      let groupObjCartList = []
      for (let iindex = 0; iindex < groupObj.policyCartList.length; iindex++) {
        const ordTypeObj = groupObj.policyCartList[iindex];
        for (let iiindex = 0; iiindex < ordTypeObj.shoppingCartList.length; iiindex++) {
          const cartItem = ordTypeObj.shoppingCartList[iiindex];
          if (cartItem.checked) {
            preDeleteIds.push(cartItem.id) // 收集选中的ID
            groupObjCartList.push(cartItem)
            if (!priceZero) {
              priceZero = !cartItem.applyPrice;
              priceZeroName = cartItem.itemName;
            }
          }
        }
      }
      if (groupObjCartList.length > 0) {
        groupList.push(groupObj)
        shoppingCartList.push(...groupObjCartList)
      }
    }
    if (shoppingCartList.length > 0) {
      if (this.data.shopSup.openEdit) {
        //去删除
        this.setData({
          showDelActionView:true,
          preDeleteIds: preDeleteIds
        })
      } else {
        //去结算
        if (groupList.length > 1) {
          wx.showToast({
            title: '您只能针对同一个分组中商品进行结算',
            icon: 'none'
          })
        } else if (shoppingCartList.length > 10) {
          wx.showToast({
            title: '一次最多提交10行商品，请分多次提交！',
            icon: 'none'
          })
        } else if (priceZero) {
          wx.showToast({
            title: '无价格商品，不允许下单！',
            icon: 'none'
          })
        } else {
          delete groupList[0].policyCartList
          wx.setStorageSync('settlementData', {
            groupObj: groupList[0],
            shoppingCartList,
            shopCartIds: preDeleteIds
          })
          wx.navigateTo({
            url: '/pages_sub/pages/ccs/down/order/fill/index',
          })
        }
      }
    } else {
      wx.showToast({
        title: '请选择一个分组中的商品进行结算！',
        icon: 'none'
      })
    }
  },
  /**
   * 常规商品数量修改
   */
  // 数量调整
  onStepChange(e) {
    const dataset = e.currentTarget.dataset
    const rootIndex = dataset.rootIndex
    const policyIndex = dataset.policyIndex
    const itemIndex = dataset.itemIndex
    let item = this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex]
    if (item['isBulkOrder'] === 2 && item['shoppingCartItemType'] !== 2) {
      if ((e.detail % item.itemNumber) > 0) {
        wx.showToast({
          title: '整托商品，必须下单整托数的整数倍！',
          icon: 'none'
        })
        // this.setData({
        //   listData: this.data.listData
        // })
        return
      }
    }
    let params = {
      id: this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].id,
      purchaseQty: e.detail,
    }
    App.getHttp()._post('/api/vcs/shoppingCart/update', params).then(res => {
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      })
      this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].purchaseQty = e.detail
      this.calculateMoney()
    })
  },
  // 数量调整的联动
  calculateMoney() {
    let totalMoney = 0
    let totalCount = 0
    const list = this.data.listData
    for (let index = 0; index < list.length; index++) {
      const groupObj = list[index]
      for (let iindex = 0; iindex < groupObj.policyCartList.length; iindex++) {
        const ordTypeObj = groupObj.policyCartList[iindex];
        for (let iiindex = 0; iiindex < ordTypeObj.shoppingCartList.length; iiindex++) {
          const cartItem = ordTypeObj.shoppingCartList[iiindex];
          if (cartItem.checked) {
            totalMoney += cartItem.purchaseQty * cartItem.applyPrice
            totalCount += cartItem.purchaseQty *1
          }
        }
      }
    }
    this.data.shopSup.totalMoney = totalMoney.toFixed(2)
    this.data.shopSup.totalCount = totalCount
    this.setData({
      shopSup: this.data.shopSup,
      listData:this.data.listData
    })
  },
  // 购选联动处理
  onClickChecked(e) {
    // rootIndex供应商分组, policyIndex:政策分组, itemIndex商品组(普通商品或者分组商品), groupItemIndex分组商品明细组 
    const dataset = e.currentTarget.dataset
    const rootIndex = dataset.rootIndex
    const policyIndex = dataset.policyIndex
    const groupItemIndex = dataset.groupItemIndex
    const itemIndex = dataset.itemIndex
    let checked = false
    switch (dataset.checkedLayer) {
      // 0 全选
      case '0':
        checked = this.data.shopSup.checked
        this.data.shopSup.checked = !checked
        this.data.shopSup.checkNumber = !checked ? this.data.listData.length : 0
        const groupList = this.data.listData
        for (let index = 0; index < groupList.length; index++) {
          const groupInfo = groupList[index]
          this.data.listData[index].checked = !checked
          groupInfo.checkNumber = !checked ? groupInfo.usableDataNum : 0
          for (let iindex = 0; iindex < groupInfo.policyCartList.length; iindex++) {
            this.data.listData[index].policyCartList[iindex].checked = !checked
            const orderType = groupInfo.policyCartList[iindex]
            for (let iiindex = 0; iiindex < orderType.shoppingCartList.length; iiindex++) {
              let itemData = this.data.listData[index].policyCartList[iindex].shoppingCartList[iiindex]
              if ((itemData.isUsable === 2 && itemData.applyPrice > 0) || this.data.shopSup.openEdit) {
                itemData.checked = !checked
              }
            }
          }
        }
        break;
      // 1 供应商层面
      case '1':
        // this.data.shopSup.checked = false
        const rootInfo = this.data.listData[rootIndex]
        checked = rootInfo.checked
        this.data.listData[rootIndex].checked = !checked
        this.data.shopSup.checkNumber = !checked ? (this.data.shopSup.checkNumber + 1) : (this.data.shopSup.checkNumber - 1)
        if (this.data.shopSup.checkNumber === this.data.listData.length) {
          this.data.shopSup.checked = true
        } else {
          this.data.shopSup.checked = false
        }
        rootInfo.checkNumber = !checked ? rootInfo.usableDataNum : 0;
        for (let index = 0; index < rootInfo.policyCartList.length; index++) {
          this.data.listData[rootIndex].policyCartList[index].checked = !checked
          const orderType = rootInfo.policyCartList[index]
          for (let iindex = 0; iindex < orderType.shoppingCartList.length; iindex++) {
            let itemData = this.data.listData[rootIndex].policyCartList[index].shoppingCartList[iindex]
            // itemData.checked = !checked
            if ((itemData.isUsable === 2 && itemData.applyPrice > 0) || this.data.shopSup.openEdit) {
              itemData.checked = !checked
            }
          }
        }
        break;
      // 3 商品层面
      case '3':
        // this.data.shopSup.checked = false
        const rootInfoq = this.data.listData[rootIndex]
        checked = this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].checked
        this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].checked = !checked
        rootInfoq.checkNumber = !checked ? ((rootInfoq.checkNumber || 0) + 1) : ((rootInfoq.checkNumber || 0) - 1)
        let rootInfoqChecked = false
        if (this.data.shopSup.openEdit) {
          rootInfoqChecked = rootInfoq.checkNumber === rootInfoq.dataNum
        } else {
          rootInfoqChecked = rootInfoq.checkNumber === rootInfoq.usableDataNum
        }
        if (rootInfoqChecked !== rootInfoq.checked) {
          rootInfoq.checked = rootInfoqChecked
          this.data.shopSup.checkNumber = !rootInfoqChecked ? (this.data.shopSup.checkNumber - 1) : (this.data.shopSup.checkNumber + 1)
          if (this.data.shopSup.checkNumber === this.data.listData.length) {
            this.data.shopSup.checked = true
          } else {
            this.data.shopSup.checked = false
          }
        }
        break;
      default:
        break;
    }
    this.setData({
      listData: this.data.listData,
      shopSup: this.data.shopSup
    })
    this.calculateMoney()
  },
  // 删除接口调用
  deleteShopCart() {
    App.getHttp()._post('/api/vcs/shoppingCart/delete', {
      ids:this.data.preDeleteIds
    }).then(res => {
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      setTimeout(() => {
        this.initCartList()
      }, 2000);
    })
  },
  // 跳转详情
  onClickGood(e) {
    return
    const custInfo = e.currentTarget.dataset.custInfo
    const itemId = e.currentTarget.dataset.itemId
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/skuDetail/index?itemId=${itemId}&vendorId=${custInfo.vendorId}&vendorCode=${custInfo.vendorCode}&vendorSetsOfBooksId=${custInfo.vendorSetsOfBooksId}&invoiceCustCode=${custInfo.invoiceCustCode}&invoiceCustId=${custInfo.invoiceCustId}&invoiceSetsOfBooksId=${custInfo.invoiceSetsOfBooksId}`
    })
  },
  // 收藏商品
  onClickCollect(e){
    const custInfo = e.currentTarget.dataset.custInfo
    const itemId = e.currentTarget.dataset.itemId
    const params = {
      vendorName: custInfo.vendorName,
      vendorCode: custInfo.vendorCode,
      vendorSetsOfBooksId: custInfo.vendorSetsOfBooksId,
      invoiceCustCode: custInfo.invoiceCustCode,
      invoiceSetsOfBooksId:custInfo.invoiceSetsOfBooksId,
      itemId: itemId,
      sourceModule:'down'//向下模块的收藏
    }
    App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect', params).then(res => {
      wx.showToast({
        title: '已收藏',
        icon:'success'
      })
    })
  },
  // 删除单个商品
  onClickDelete(e){
    const {rootIndex,policyIndex,itemIndex} = e.currentTarget.dataset
    const cartItem =this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex]
     this.setData({
      showDelActionView:true,
      preDeleteIds: [cartItem.id]
    })
  },
  // 删除提醒弹窗回调
  onCloseDelActionView(e){
    if(e.type&&e.type=='select'){
      this.deleteShopCart()
    }
    this.setData({
      showDelActionView:false
    })
  }
})