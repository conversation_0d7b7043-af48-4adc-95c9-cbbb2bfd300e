// pages_sub/pages/ccs/down/policy/group/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dataInfo:{},
    id: '',
    type: '',
    title: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let type = options.type
    let title = ''
    if(type== 'buyoutPrice'){
      title = '一口价组合'
    }else if(type== 'reduction'){
      title = '满减组合'
    }else if(type== 'discount'){
      title = '满折组合'
    }else if(type== 'salesGift'){
      title = '满赠组合'
    }
    this.setData({id: options.id, type: options.type,title})
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onSearchClick(){
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/policy/policySearchHis/index?type=${this.data.type}`,
    })
  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
  getData(){
    const vendorInfo = wx.getStorageSync('custInfo')
    let param={
      id: this.data.id,
      custId: vendorInfo.scustId,
      custCode: vendorInfo.scustCode
    }
    App.getHttp()._post('/api/psi/policyOrder/queryPolicyShopingById', param, true).then((res)=>{
      this.setData({dataInfo: res.content})
      console.log('list',this.data.dataInfo, this.data.dataInfo.policyConfigItemResponseDTOList[0].policyItemGroupLineResponseDTOList)
    })
  },
  addToCarts(e){
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let index = e.currentTarget.dataset.value // 获取传入的参数
    console.log('storage', vendorInfo, supInfo, custInfo)
    let param={
      "orderType": this.data.dataInfo.promotionType,
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  vendorInfo.scustId,
      "invoiceCustCode":  vendorInfo.scustCode,
      "sourceBillHeadId": this.data.dataInfo.id,
      "sourceBillNo": this.data.dataInfo.policyCode,
      qtyModel: 2,
      itemList:this.handleItemList(index)
    }
    App.getHttp()._post('/api/psi/shoppingCart/addToCarts', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  handleOrderType(){
    // orderType
    // COMMON(0, "普通"),
    // REDUCE(1, "满减政策订单"),
    // DISCOUNT(2, "满折政策订单"),
    // SP_PRICE(3, "一口价政策订单"),
    // GIFT(4, "满赠政策订单"),
    // CROWDFUND(5, "众筹订单");
    // promotionType 字典值
    // 1满减 2满折 3一口价 4满赠 5众筹
    switch(dataInfo.policyType){
      case 1: return 1;
      case 2: return 2;
      case 3: return 3; 
      case 4: return 4; 
      case 5: return 5; 
    }
  },
  handleItemList(index){
    let policyConfigItemResponseDTOItem = this.data.dataInfo.policyConfigItemResponseDTOList[index]
    let itemList = policyConfigItemResponseDTOItem.policyItemGroupLineResponseDTOList.map(item=>{
      let newItem = {}
      newItem.itemId = item.itemId
      newItem.itemCode = item.itemCode
      newItem.purchaseQty = 1
      newItem.sourceBillLineId = policyConfigItemResponseDTOItem.id
      // newItem.itemGroup = policyConfigItemResponseDTOItem.groupCode
      newItem.groupId = policyConfigItemResponseDTOItem.groupId
      /** 本品 */
      // GOODS(1, "本品"),
      /** 货补 */
      // GOODSPOOL(2, "货补"),
      /** 赠品 */
      // GIFT(3, "赠品"),
      newItem.itemLineType = 1
      return newItem;
    })
    return itemList
  }

})