// pages_sub/pages/ccs/down/policy/crowdfunding/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH:500,
    showList:[],
    serchValue: '',
    showPop: false,
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    steps:[],
    navTop: 24,
    navHeight: 24,
    vendorInfo:{},
    showDialog:false,
    itemInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadMore()
  },
  onShow: function () {
    const vendorInfo = wx.getStorageSync("vendorInfo");
    this.setData({vendorInfo})
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },
  onSearchClick(e){
    // console.log(11)
    // this.setData({
    //   searchValue: e.detail,
    //   pageIndex: 1,
    //   pages: 0,
    //   showList: [],
    // })
    // this.loadMore()
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/policy/policySearchHis/index?type=crowdfunding`,
    })
  },
  onRefresh(){
    this.setData({
      showList: [],
      pageIndex: 1,
      pages: 0,
    })
    this.loadMore()
  },
  loadMore(){
    if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    // let param ={
    //   "pageIndex": this.data.pageIndex,
    //   "pageSize": this.data.pageSize,
    //   "param": {
    //     // receiveUserSetsId: userInfo.setsOfBooksId,
    //     keywords: this.data.searchValue,
    //     vendorSetsOfBooksId: vendorInfo.ssetsOfBooksId, // 传上游账套
    //     "custId": custInfo.custId,
    //     "promotionType": 5, // 1满减 2满折 3一口价 4满赠 5众筹
    //     "publishState": 2, // 已发布
    //     "state": 2, // 状态生效
    //     "addType": 2 // 1 :厂家新增 2: 客户新增
    //   }
    // }
    // console.log(param)
    // // /api/mms/news/myx/page
    // App.getHttp()._post('/api/vcs/policy/policyShoping/page', param, true).then((res)=>{
    //   console.log('itemList',res)
    //   this.setData({
    //     showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
    //     pageIndex: this.data.pageIndex + 1,
    //     pages: res.pages
    //   })
    // })
    // 列表返回价格区间
    let newParam = {
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        keywords: this.data.searchValue,
        vendorSetsOfBooksId: vendorInfo.ssetsOfBooksId, // 传上游账套
        custId: vendorInfo.scustId,
        custCode: vendorInfo.scustCode,
        crowdType: 2,
        isUsable: 2,
        isRelease: 2,
      }
    }
    App.getHttp()._post('/api/psi/policyOrder/getPageCrowdOrders', newParam, true).then((res)=>{
      this.setData({
        showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
  onItemClick(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let item = this.data.showList[index]
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/policy/crowdfundingDetail/index?id=${item.id}`,
    })
  },
  addToCarts(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param={
      "orderType": 5, // 众筹
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  vendorInfo.scustId,
      "invoiceCustCode":  vendorInfo.scustCode,
      "sourceBillHeadId": this.data.showList[index].id,
      "sourceBillNo": this.data.showList[index].code,
      qtyModel: 2,
      itemList:this.handleItemList(index)
    }
    App.getHttp()._post('/api/psi/shoppingCart/addToCarts', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  handleItemList(index){
    let item1 = this.data.showList[index]
    return [item1].map(item=>{
      let newItem = {}
      newItem.itemId = item.itemId
      newItem.itemCode = item.itemCode
      newItem.purchaseQty = 1
      newItem.sourceBillLineId = item.id
      // newItem.itemGroup = item.groupCode
      // newItem.groupId = item.groupId
      /** 本品 */
      // GOODS(1, "本品"),
      /** 货补 */
      // GOODSPOOL(2, "货补"),
      /** 赠品 */
      // GIFT(3, "赠品"),
      newItem.itemLineType = 1
      return newItem;
    })
  },
  showDialog(e){
    this.setData({itemInfo: e.detail, showDialog: true})
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})