// pages_sub/pages/ccs/down/policy/single/index.js
// pages_sub/pages/ccs/down/policy/group/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dataInfo:{},
    id: '',
    type: '',
    title: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let type = options.type
    let title = ''
    if(type== 'buyoutPrice'){
      title = '一口价单品'
    }else if(type== 'reduction'){
      title = '满减单品'
    }else if(type== 'discount'){
      title = '满折单品'
    }else if(type== 'salesGift'){
      title = '满赠单品'
    }
    this.setData({id: options.id, type: options.type,title})
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onSearchClick(){
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/down/policy/policySearchHis/index?type=${this.data.type}`,
    })
  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
  getData(){
    const vendorInfo = wx.getStorageSync('vendorInfo') 
    let param={
      id: this.data.id,
      custId: vendorInfo.scustId,
      custCode: vendorInfo.scustCode
    }
    App.getHttp()._post('/api/psi/policyOrder/queryPolicyShopingById', param, true).then((res)=>{
      this.setData({dataInfo: res.content})
    })
  },
  addToCarts(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param={
      "orderType": this.data.dataInfo.promotionType,
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  vendorInfo.scustId,
      "invoiceCustCode":  vendorInfo.scustCode,
      "sourceBillHeadId": this.data.dataInfo.id,
      "sourceBillNo": this.data.dataInfo.policyCode,
      qtyModel: 2,
      itemList:this.handleItemList(index)
    }
    App.getHttp()._post('/api/psi/shoppingCart/addToCarts', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  handleItemList(index){
    let item1 = this.data.dataInfo.policyConfigItemResponseDTOList[index]
    return [item1].map(item=>{
      let newItem = {}
      newItem.itemId = item.itemId
      newItem.itemCode = item.itemCode
      newItem.purchaseQty = 1
      newItem.sourceBillLineId = item.id
      // newItem.itemGroup = item.groupCode
      // newItem.groupId = item.groupId
      /** 本品 */
      // GOODS(1, "本品"),
      /** 货补 */
      // GOODSPOOL(2, "货补"),
      /** 赠品 */
      // GIFT(3, "赠品"),
      newItem.itemLineType = 1
      return newItem;
    })
  }
})