<!--pages_sub/pages/ccs/down/policy/single/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" id="root-layout">
  <van-sticky class="top-layout">
    <list-head isShowType="{{true}}" showSupplier="{{false}}" showSupplierText="{{false}}" customTitle="{{title}}" />
    <policy-search bind:onSearchClick="onSearchClick" />
    <policy-title dataInfo="{{dataInfo}}" />
  </van-sticky>
  <view wx:if="{{dataInfo.policyConfigItemResponseDTOList.length>0}}" class="{{dataInfo.promotionType != 3 && dataInfo.note ? 'discount-margin' : ''}}">
    <policy-item wx:for="{{dataInfo.policyConfigItemResponseDTOList}}" wx:for-item="item1" promotionType="{{dataInfo.promotionType}}" wx:key="index" itemInfo="{{item1}}" data-value="{{index}}" bind:addToCarts="addToCarts"/>
  </view>
</view>