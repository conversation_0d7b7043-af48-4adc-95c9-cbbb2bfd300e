<!--pages_sub/pages/ccs/down/policy/buyoutPrice/index.wxml-->
<view class="root-layout" id="root-layout">
  <van-sticky class="top-layout" id="top-layout">
    <list-head isShowType="{{false}}" showSupplier="{{false}}" showSupplierText="{{false}}" customTitle="一口价" />
    <policy-search bind:onSearchClick="onSearchClick" />
  </van-sticky>
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
    <view class="policy-bvendorName">{{vendorInfo.bvendorName}}</view>
    <block wx:if="{{showList.length > 0}}">
      <view wx:for="{{showList}}" wx:for-item="item" wx:key="index" data-value="{{index}}" bindtap="onItemClick">
        <policy-list-item itemInfo="{{item}}" />
      </view>
    </block>
    <view class="m-t-25p" wx:else>
      <no-product noneTxt="暂无数据" />
    </view>
  </listView>
</view>