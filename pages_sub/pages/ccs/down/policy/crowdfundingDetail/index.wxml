<!--pages_sub/pages/ccs/down/policy/crowdfundingDetail/index.wxml-->
<view class="page root-layout" id="page-layout">
  <van-sticky class="top-layout">
    <view style="height: {{navTop}}px; background:#fff;"></view>
    <view class="nav-layout" style="height:{{navHeight}}rpx; line-height:{{navHeight}}rpx" id="nav-layout">
      <view class="top-nav">
        <view class="nav-left" bindtap="goBack">
          <van-icon name="arrow-left" size="40rpx" bindtap="onClickArrow" color="#242424" />
          <view class="top-nav-text">众筹</view>
        </view>
        <view class="nav-right" bindtap="transferBtnClick">
          <van-icon name="/asset/imgs/cart.png" custom-class="top-img" catchtap="linkToCarts" />
          <view class="img-txt">购物车</view>
        </view>
      </view>
    </view>
  </van-sticky>
  <block>
    <view>
      政策单号：{{dataInfo.code}}
    </view>
    <view>
      政策标题：{{dataInfo.name}}
    </view>
    <view>
      政策描述：{{dataInfo.remark}}
    </view>
    <view>
      最小众筹数量:{{dataInfo.minCrowdQuantity}}
    </view>
    <view>
      最大封顶数：{{dataInfo.maxCrowdQuantity}}
    </view>
    <view>
      <countDown />
    </view>
    <view>众筹阶梯：</view>
    <view calss="table-header">
      <view class="table-item">序号</view>
      <view class="table-item">起始值</view>
      <view class="table-item">终止值</view>
      <view class="table-item">价格</view>
    </view>
    <view wx:for="{{dataInfo.sections}}" wx:for-item="item" wx:key="index" data-value="{{index}}" class="table-row">
      <view class="table-item">{{index}}</view>
      <view class="table-item">{{item.beginSection}}</view>
      <view class="table-item">{{item.endSection}}</view>
      <view class="table-item">{{item.taxPrice}}</view>
    </view>
    <view>已众筹数量：{{dataInfo.actualCrowdQuantity}}</view><view>已众筹价格：{{dataInfo.actualCrowdPrice}}</view>
    <van-image width="40" height="40" fit="contain" src="{{dataInfo.itemUrl}}" />
    <view>
      <view>众筹商品名称：{{dataInfo.itemName}}</view>
      <view>众筹商品编码：{{dataInfo.itemCode}}</view>
      <view>众筹商品型号：{{dataInfo.specs}}</view>
      <view>{{dataInfo.normalPrice}}</view>
    </view>
    <view>
    </view>
    <van-image width="40" height="40" fit="contain" src="/asset/imgs/cart.png" catchtap="addToCarts" />
  </block>
</view>