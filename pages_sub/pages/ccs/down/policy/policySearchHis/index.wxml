<!--pages_sub/pages/ccs/down/policy/policySearchHis/index.wxml-->
<view class="root-layout" id="root-layout">
    <van-sticky class="top-layout" id="top-layout">
        <nav-bar title="搜索" />
        <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{false}}" bind:onSearchTxtClick="onSearchClick" bind:onConfirm="onSearchClick" id="search" />
    </van-sticky>
    <search-history wx:if="{{!showList.length}}" showHis="{{!showList.length}}" busCode="{{busCode}}" busModule="{{busModule}}" id="searchHistory" bind:onClickHisKeyWord="onClickHisKeyWord" />
    <view class="scroll-container" wx:else>
        <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onRefresh" bind:loadmore="loadMore">
            <view class="item-layout" wx:if="{{showList.length>0}}">
                <view wx:for="{{showList}}" wx:for-item="item" wx:key="index" data-value="{{index}}" bindtap="onItemClick">
                    <policy-list-item itemInfo="{{item}}" bind:addToCarts="addToCarts" data-value="{{index}}" promotionType="{{promotionType}}" bind:showDialog="showDialog"/>
                </view>
            </view>
            <view class="m-t-25p" wx:else>
                <no-product noneTxt="暂无数据" />
            </view>
        </listView>
        <crowdfunding-dialog itemInfo="{{itemInfo}}" showDialog="{{showDialog}}"/>
    </view>
</view>