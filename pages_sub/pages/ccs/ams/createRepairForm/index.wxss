/* pages_sub/pages/ccs/ams/createRepairForm/index.wxss */
page {
  height: 100%;
  background-color: #F2F2F2;
}

/* 页面布局 */
.page-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.root-layout {
  height: 100%;
}

.top-layout {
  background: #fff;
  z-index: 100;
}

/* 创建表单样式 */
.create-form {
  flex: 1;
  overflow-y: auto;
}

.header-title {
  display: flex;
  justify-content: space-between;
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx;
  color: #242424;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
  background: #fff;
}

.header-baseInfo {
  margin-left: 32rpx;
  font-weight: 500;
}

.custom-group-layout {
  padding-left: 32rpx;
  background-color: white;
}

.field-class {
  line-height: 96rpx !important;
  padding: 0 0 0 18rpx !important;
  display: flex;
  align-items: center;
  font-size: 32rpx !important;
  font-family: PingFang SC-Regular, PingFang SC !important;
  font-weight: 400 !important;
}

.field-class-textarea {
  box-sizing: border-box;
  line-height: 96rpx !important;
  padding: 0 0 0 18rpx !important;
}

.label-class {
  color: #242424 !important;
  font-weight: 400 !important;
  font-size: 32rpx !important;
  font-family: PingFang SC-Regular, PingFang SC !important;
}

.field-class::before, .field-class-textarea::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0 !important;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-size: 28rpx !important;
}

.input-class {
  height: 96rpx !important;
  line-height: 96rpx !important;
  margin-right: 32rpx !important;
}

.textarea-class {
  margin-top: 32rpx !important;
  min-height: 96rpx !important;
  line-height: 42rpx !important;
  margin-right: 32rpx !important;
  white-space: pre-wrap !important;
  word-break: break-all !important;
}

.van-field__body--textarea {
  padding: 0 !important;
}

.input-class .van-field__placeholder, .field-class-textarea .van-field__placeholder {
  color: #BDBDBD !important;
}

.van-field__word-limit {
  margin-right: 32rpx;
  font-size: 28rpx !important;
}

.van-icon-arrow {
  margin-left: -16rpx;
  margin-right: 32rpx;
}

.van-icon-arrow::before {
  width: 32rpx;
  height: 32rpx;
}

/* 上传图片区域 */
.upload-pic-box {
  background: #fff;
  padding-bottom: 16rpx;
}

.upload-pic {
  height: 92rpx;
  line-height: 92rpx;
  color: #242424;
  font-size: 32rpx;
  background: #fff;
}

.uploader-box {
  padding: 0 32rpx;
}

/* 按钮样式 */
.btn-box {
  margin-top: 24rpx;
  padding: 0 24rpx;
}

.btn-class {
  height: 96rpx !important;
  border-radius: 8rpx !important;
  border: 0 !important;
  background: #00b9c3 !important;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF !important;
  font-size: 32rpx !important;
  line-height: 96rpx !important;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 历史记录列表样式 */
.history-list {
  flex: 1;
  overflow-y: auto;
}

.scroll-layout {
  height: 100%;
}

.item-layout {
  padding: 24rpx;
}

.list-item-repair {
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-number {
  font-size: 32rpx;
  font-weight: 500;
  color: #242424;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
}

.status-1 {
  background: #e6f7ff;
  color: #1890ff;
}

.status-2 {
  background: #fff7e6;
  color: #fa8c16;
}

.status-3 {
  background: #f6ffed;
  color: #52c41a;
}

.status-4 {
  background: #fff2e8;
  color: #fa541c;
}

.status-5 {
  background: #f0f5ff;
  color: #2f54eb;
}

.status-6 {
  background: #f9f0ff;
  color: #722ed1;
}

.status-7 {
  background: #fff1f0;
  color: #f5222d;
}

.status-8 {
  background: #f6ffed;
  color: #52c41a;
}

.status-9 {
  background: #e6fffb;
  color: #13c2c2;
}

.status-10 {
  background: #f5f5f5;
  color: #8c8c8c;
}

.item-content {
  margin-bottom: 24rpx;
}

.item-content > view {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 40rpx;
}

.item-content .label {
  color: #8c8c8c;
  min-width: 140rpx;
  flex-shrink: 0;
}

.item-content .value {
  color: #242424;
  flex: 1;
  word-break: break-all;
}

.fault-desc .value {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.create-time {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 工具类 */
.m-t-25p {
  margin-top: 25%;
}

/* 单行省略 */
.single-line-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 两行省略 */
.two-line-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 上传组件样式调整 */
.van-uploader__upload {
  width: 160rpx !important;
  height: 160rpx !important;
  margin-right: 16rpx !important;
  margin-bottom: 16rpx !important;
}

/* 标签页样式 */
.van-tabs__wrap {
  background: #fff;
}

.van-tab {
  font-size: 32rpx !important;
  color: #8c8c8c !important;
}

.van-tab--active {
  color: #00b9c3 !important;
  font-weight: 500 !important;
}

.van-tabs__line {
  background-color: #00b9c3 !important;
} 

/* 省市区选择相关样式 */
.region-result {
  padding: 24rpx 32rpx;
  background: #fafafa;
  border-top: 1rpx solid #f0f0f0;
}

.station-available,
.station-unavailable {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 40rpx;
}

.station-available {
  color: #52c41a;
}

.station-unavailable {
  color: #ff4d4f;
}

.station-available .van-icon,
.station-unavailable .van-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
}

.form-section {
  margin-top: 24rpx;
}

/* 提交确认弹窗样式 */
.submit-confirm-content {
  padding: 32rpx;
}

.confirm-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
}

.confirm-items {
  padding-left: 20rpx;
}

.confirm-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 12rpx;
}

/* 服务进度按钮样式 */
.progress-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.progress-btn van-icon {
  margin-right: 8rpx;
  color: #1989fa;
}

/* 服务进度弹窗样式 */
.progress-popup {
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.progress-header text {
  font-size: 32rpx;
  font-weight: bold;
}

.progress-header van-icon {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.progress-content {
  padding: 32rpx 24rpx;
  max-height: calc(80vh - 100rpx);
  overflow-y: auto;
}

.progress-steps {
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  left: 15rpx;
  top: 30rpx;
  bottom: 30rpx;
  width: 2rpx;
  background-color: #eee;
  z-index: 1;
}

.step {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding: 20rpx 0;
  z-index: 2;
}

.step-dot {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #eee;
  margin-right: 24rpx;
  position: relative;
  z-index: 3;
}

.step.active .step-dot {
  background-color: #1989fa;
}

.step-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.step-time {
  font-size: 24rpx;
  color: #999;
}

.step.active .step-title {
  color: #1989fa;
  font-weight: bold;
}

/* 日期查询区域样式 */
.date-filter-section {
  padding: 24rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.date-filter-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.date-filter-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
  white-space: nowrap;
}

.date-filter-buttons {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

/* 新的卡片样式 */
.repair-order-card {
  background-color: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-header .order-info {
  flex: 1;
}

.card-header .order-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.card-header .order-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.card-header .order-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.card-content {
  padding: 24rpx;
}

.content-row {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.content-row:last-child {
  margin-bottom: 0;
}

.content-row .label {
  color: #666;
  margin-right: 16rpx;
  min-width: 140rpx;
}

.content-row .value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 运营商信息样式 */
.operator-name {
  font-weight: 500;
  color: #1989fa;
}

.operator-separator {
  white-space: pre;
}

.operator-address {
  color: #666;
  font-size: 26rpx;
}

.card-footer {
  padding: 24rpx;
  border-top: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 16rpx;
}

.card-footer .progress-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  width: fit-content;
}

.card-footer .progress-btn van-icon {
  margin-right: 8rpx;
  color: #1989fa;
}

.empty-state {
  padding: 80rpx 0;
  text-align: center;
}

/* 取消订单对话框样式 */
.cancel-dialog {
  width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 32rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.dialog-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  padding: 10rpx;
  color: #999;
}

.dialog-content {
  padding: 32rpx;
}

.order-info {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.order-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.order-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.memo-section {
  margin-bottom: 32rpx;
}

.memo-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
}

.textarea-container {
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  background: #fff;
  position: relative;
}

.cancel-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.word-count {
  position: absolute;
  bottom: 10rpx;
  right: 15rpx;
  font-size: 24rpx;
  color: #999;
}

.dialog-footer {
  display: flex;
  gap: 24rpx;
  padding: 0 32rpx 32rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.cancel-btn {
  border-color: #d9d9d9;
  color: #666;
}

.confirm-btn {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

/* 修改模式提示样式 */
.edit-mode-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  background-color: #e6f7ff;
  color: #1989fa;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 修改模式按钮组样式 */
.edit-mode-buttons {
  display: flex;
  gap: 20rpx;
}

.edit-mode-buttons .van-button {
  flex: 1;
}

.cancel-edit-btn {
  border-color: #999 !important;
  color: #999 !important;
}

/* 地址操作按钮样式 */
.address-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 8rpx;
}

.address-actions van-icon {
  padding: 8rpx;
  border-radius: 6rpx;
  background-color: #f7f8fa;
  transition: all 0.3s ease;
}

.address-actions van-icon:active {
  background-color: #e6f3ff;
  transform: scale(0.95);
}