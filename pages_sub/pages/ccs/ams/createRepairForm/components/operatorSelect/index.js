Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    // 运营商列表
    operatorList: {
      type: Array,
      value: []
    }
  },

  data: {
    selectedOperator: null
  },

  methods: {
    onClose() {
      this.setData({ show: false })
      this.triggerEvent('close')
    },

    onSelect(e) {
      const { index } = e.currentTarget.dataset
      const operator = this.data.operatorList[index]
      this.setData({ 
        selectedOperator: operator,
        show: false
      })
      this.triggerEvent('select', { operator })
    }
  }
}) 