<!-- 运营商选择弹窗 -->
<van-popup 
  show="{{ show }}" 
  position="bottom" 
  round 
  bind:close="onClose"
  custom-class="operator-popup"
>
  <view class="operator-container">
    <view class="operator-header">
      <view class="operator-title">选择运营商</view>
      <view class="operator-subtitle">请选择就近的运营商门店</view>
    </view>
    
    <view class="operator-list">
      <view 
        class="operator-item" 
        wx:for="{{operatorList}}" 
        wx:key="id"
        data-index="{{index}}"
        bindtap="onSelect"
      >
        <image class="operator-image" src="{{item.imageUrl}}" mode="aspectFill" />
        <view class="operator-info">
          <view class="operator-address">{{item.address}}</view>
          <view class="operator-contact">
            <text>联系人：{{item.contactPerson}}</text>
            <text>电话：{{item.contactPhone}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="operator-footer">
      <van-button block type="default" bind:click="onClose">取消</van-button>
    </view>
  </view>
</van-popup> 