.operator-popup {
  max-height: 80vh;
}

.operator-container {
  padding: 24rpx;
}

.operator-header {
  text-align: center;
  padding: 20rpx 0 40rpx;
}

.operator-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.operator-subtitle {
  font-size: 24rpx;
  color: #999;
}

.operator-list {
  margin-bottom: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.operator-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.operator-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}

.operator-info {
  flex: 1;
  overflow: hidden;
}

.operator-address {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  line-height: 1.4;
}

.operator-contact {
  font-size: 24rpx;
  color: #999;
  display: flex;
  flex-direction: column;
}

.operator-contact text {
  margin-bottom: 8rpx;
}

.operator-footer {
  padding: 20rpx 0;
} 