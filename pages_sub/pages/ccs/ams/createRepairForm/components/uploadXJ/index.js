// pages_sub/pages/ccs/ams/createRepairForm/components/uploadXJ/index.js
import { DOMAINXJ } from '../../../../../../../utils/server'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    config: {
      type: Object,
      value: {
        key: 'fileIds',
        attrs: { max: 3 }
      }
    },
    initFileIds: {
      type: null, // 支持字符串或数组
      value: ''
    },
    allDisabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    fileList: [],
    fileIds: [],
    removedFileIds: [], // 删除的文件ID列表
  },

  /**
   * 在组件实例进入页面节点树时执行
   */
  attached() {
    // 设置比例适配
    const query = wx.createSelectorQuery().in(this)
    query.select('.van-cell').boundingClientRect((rect) => {
      if (rect) {
        const byclear = rect.width / 375
        this.setData({ byclear })
      }
    }).exec()
  },

  /**
   * 数据监听器
   */
  observers: {
    initFileIds: function (data) {
      console.log('initFileIds 观察器触发:', data, '当前fileIds:', this.data.fileIds, '当前fileList:', this.data.fileList.map(f => f.fileId))
      
      if (Array.isArray(data) && data.length === 0) {
        console.log('initFileIds: 传入空数组，清空后端文件，保留临时文件。')
        const tempFiles = this.data.fileList.filter(file => typeof file.fileId === 'string' && file.fileId.startsWith('temp_'))
        this.setData({
          fileIds: [],
          fileList: tempFiles,
          removedFileIds: []
        })
        return
      }
      
      if (data === undefined || data === null) {
        return
      }
      
      const value = data
      const newBackendFileIds = Array.isArray(value) ? value : (value ? (typeof value === 'string' ? value.split(',') : [value]) : [])
      const currentBackendFileIds = this.data.fileIds.map(String)
      
      if (JSON.stringify(currentBackendFileIds.sort()) === JSON.stringify(newBackendFileIds.map(String).sort())) {
        console.log('initFileIds: 后端文件ID列表未改变，跳过更新。')
        return
      }
      
      this.setData({
        fileIds: newBackendFileIds,
      })
      
      if (newBackendFileIds.length > 0) {
        this.getFileList(newBackendFileIds).then(backendFiles => {
          const existingTempFiles = this.data.fileList.filter(file => typeof file.fileId === 'string' && file.fileId.startsWith('temp_'))
          const mergedFileList = [...backendFiles, ...existingTempFiles]
          this.setData({
            fileList: mergedFileList
          })
          console.log('initFileIds: 合并后的文件列表:', mergedFileList.map(f => f.fileId))
        }).catch(err => {
          console.error('initFileIds: 获取文件列表失败:', err)
          const existingTempFiles = this.data.fileList.filter(file => typeof file.fileId === 'string' && file.fileId.startsWith('temp_'))
          this.setData({
            fileList: existingTempFiles
          })
        })
      } else {
        const existingTempFiles = this.data.fileList.filter(file => typeof file.fileId === 'string' && file.fileId.startsWith('temp_'))
        this.setData({
          fileList: existingTempFiles
        })
      }
      
      console.log('initFileIds: 设置新的后端fileIds:', newBackendFileIds)
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取文件列表
     */
    async getFileList(fileIds) {
      try {
        console.log('获取文件列表, fileIds:', fileIds)
        
        // 确保fileIds是字符串格式（逗号分隔），符合后端期望
        let fileIdsParam = fileIds
        if (Array.isArray(fileIds)) {
          fileIdsParam = fileIds.join(',')
        } else if (typeof fileIds === 'string') {
          fileIdsParam = fileIds
        } else {
          fileIdsParam = String(fileIds)
        }
        
        console.log('转换后的fileIds参数:', fileIdsParam)
        
        const param = {
          fileIds: fileIdsParam
        }
        
        const url = DOMAINXJ + "/api/AfterSalePsiOrder/GetFileList"
        const res = await getApp().getHttp()._requestXJ(url, param, 'POST')
        console.log('文件列表接口返回:', res)
        
        if (res && res.success && res.response) {
          const list = []
          const files = res.response || []
          
          files.forEach(item => {
            let fileUrl = item.fileUrl || item.url || ''
            
            if (fileUrl.startsWith('/')) {
              fileUrl = DOMAINXJ + fileUrl
            }
            
            const isImage = ['jpg', 'png', 'jpeg', 'bmp', 'gif', 'webp'].includes(item.fileType?.toLowerCase())
            const isVideo = ['mp4', 'avi', 'mov', 'wmv', 'flv', '3gp', 'm4v', 'mkv', 'webm'].includes(item.fileType?.toLowerCase())
            
            list.push({
              url: fileUrl,
              isImage,
              isVideo: isVideo || (fileUrl && /\.(mp4|avi|mov|wmv|flv|3gp|m4v|mkv|webm)(\?.*)?$/i.test(fileUrl)),
              fileId: String(item.fileId || item.id || `backend_temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`),
              fileType: item.fileType || fileUrl?.split('.').pop()?.split('?')[0],
              name: item.fileName || item.name || `文件_${item.fileId || item.id || '未知'}.${item.fileType || 'file'}`,
              size: item.fileSize || item.size || '未知大小',
              status: 'success'
            })
          })
          
          console.log('处理后的文件列表:', list)
          return list
        } else {
          console.error('获取文件列表失败:', res)
          return []
        }
      } catch (error) {
        console.error('获取文件列表异常:', error)
        return []
      }
    },

    /**
     * 直接选择文件 - 使用微信原生API
     */
    directChooseFile() {
      if (this.data.allDisabled) {
        return
      }
      
      const maxCount = this.data.config?.attrs?.max || 5
      const currentCount = this.data.fileList.length
      
      if (currentCount >= maxCount) {
        wx.showToast({
          title: `最多只能选择${maxCount}个文件`,
          icon: 'none'
        })
        return
      }
      
      const remainingCount = maxCount - currentCount
      
      wx.showActionSheet({
        itemList: ['拍照', '录制视频', '从相册选择图片', '从相册选择视频'],
        success: (res) => {
          console.log('用户选择:', res.tapIndex)
          switch (res.tapIndex) {
            case 0:
              this.takePhoto()
              break
            case 1:
              this.recordVideo()
              break
            case 2:
              this.chooseImages(remainingCount)
              break
            case 3:
              this.chooseVideos(remainingCount)
              break
          }
        }
      })
    },

    /**
     * 选择图片
     */
    chooseImages(count) {
      wx.chooseImage({
        count: count,
        sizeType: ['original', 'compressed'],
        sourceType: ['album'],
        success: (res) => {
          console.log('选择图片成功:', res)
          this.processSelectedFiles(res.tempFiles, 'image')
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 选择视频
     */
    chooseVideos(count) {
      // 微信小程序的chooseVideo一次只能选择一个
      wx.chooseVideo({
        sourceType: ['album'],
        maxDuration: 60,
        camera: 'back',
        success: (res) => {
          console.log('选择视频成功:', res)
          const videoFile = {
            path: res.tempFilePath,
            size: res.size,
            duration: res.duration,
            height: res.height,
            width: res.width
          }
          this.processSelectedFiles([videoFile], 'video')
        },
        fail: (err) => {
          console.error('选择视频失败:', err)
          wx.showToast({
            title: '选择视频失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 拍照
     */
    takePhoto() {
      wx.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: (res) => {
          console.log('拍照成功:', res)
          this.processSelectedFiles(res.tempFiles, 'image')
        },
        fail: (err) => {
          console.error('拍照失败:', err)
          wx.showToast({
            title: '拍照失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 录制视频
     */
    recordVideo() {
      wx.chooseVideo({
        sourceType: ['camera'],
        maxDuration: 60,
        camera: 'back',
        success: (res) => {
          console.log('录制视频成功:', res)
          const videoFile = {
            path: res.tempFilePath,
            size: res.size,
            duration: res.duration,
            height: res.height,
            width: res.width
          }
          this.processSelectedFiles([videoFile], 'video')
        },
        fail: (err) => {
          console.error('录制视频失败:', err)
          wx.showToast({
            title: '录制视频失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 处理选择的文件
     */
    processSelectedFiles(files, type) {
      console.log('处理选择的文件:', files, type)
      
      if (!files || files.length === 0) {
        return
      }
      
      const baseTime = Date.now()
      const formattedFiles = files.map((file, index) => ({
        url: file.path,
        thumb: file.path,
        size: file.size,
        type: type,
        name: `${type}_${baseTime}_${index}.${type === 'image' ? 'jpg' : 'mp4'}`,
        fileId: `temp_${baseTime}_${index}_${Math.random().toString(36).substr(2, 9)}`,
        isImage: type === 'image',
        isVideo: type === 'video',
        fileType: type === 'image' ? 'jpg' : 'mp4',
        duration: file.duration,
        height: file.height,
        width: file.width
      }))
      
      this.batchUploadFiles(formattedFiles)
    },

    /**
     * 批量上传文件
     */
    async batchUploadFiles(files) {
      console.log('开始批量上传文件:', files)
      
      if (files.some(file => file.isVideo)) {
        console.log('检测到视频文件，建议使用H.264编码的MP4格式以获得最佳兼容性')
      }
      
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
      
      try {
        const uploadPromises = files.map(async (file, i) => {
          const uploadTask = wx.uploadFile({
            url: DOMAINXJ + '/api/AfterSalePsiOrder/BatchUploadFiles',
            filePath: file.url,
            name: 'files',
            timeout: 30000,
            success: (res) => {
              console.log(`文件${i}上传成功:`, res)
              
              try {
                const data = JSON.parse(res.data)
                console.log(`文件${i}解析的数据:`, data)
                
                if (data.success && data.response) {
                  // 适配新的返回格式：data.response = { fileIds: [9], successCount: 1, ... }
                  const response = data.response
                  
                  if (response.fileIds && Array.isArray(response.fileIds) && response.fileIds.length > 0) {
                    const fileId = response.fileIds[0] // 取第一个文件ID
                    
                    console.log(`文件${i}获得ID:`, fileId)
                    
                    // 由于后端只返回fileId，没有fileUrl，我们需要构造一个临时的fileObject
                    // 实际的URL信息需要通过getFileList再次获取，这里先用临时数据
                    const fileObject = {
                      url: file.url, // 暂时使用本地临时路径
                      isImage: file.isImage,
                      isVideo: file.isVideo,
                      fileId: fileId,
                      fileType: file.isImage ? 'jpg' : (file.isVideo ? 'mp4' : 'file'),
                      name: file.name || `uploaded_${Date.now()}_${i}.${file.isImage ? 'jpg' : 'mp4'}`,
                      size: file.size,
                      status: 'success',
                      needRefresh: true // 标记需要刷新URL
                    }
                    
                    // 添加到临时文件列表
                    const currentFileList = this.data.fileList || []
                    const tempFileIndex = currentFileList.findIndex(f => f.fileId === file.fileId)
                    
                    if (tempFileIndex >= 0) {
                      // 替换临时文件
                      currentFileList[tempFileIndex] = fileObject
                    } else {
                      // 添加新文件
                      currentFileList.push(fileObject)
                    }
                    
                    this.setData({
                      fileList: currentFileList
                    })
                    
                    // 更新文件ID列表
                    const newFileIds = currentFileList.map(file => file.fileId)
                    
                    // 触发change事件
                    this.triggerEvent('change', {
                      key: this.data.config?.key,
                      fileList: currentFileList,
                      fileIds: newFileIds,
                      removedFileIds: this.data.removedFileIds || [],
                      uploadNum: currentFileList.length
                    })
                    
                    return fileObject
                  } else {
                    console.error(`文件${i}上传成功但无有效fileId:`, response)
                    throw new Error('上传成功但无有效文件ID')
                  }
                } else {
                  console.error(`文件${i}上传失败:`, data)
                  throw new Error(data.msg || '上传失败')
                }
              } catch (parseError) {
                console.error(`文件${i}解析响应失败:`, parseError, res.data)
                throw parseError
              }
            },
            fail: (err) => {
              console.error(`文件${i}上传失败:`, err)
              throw err
            }
          })
          
          return uploadTask
        })
        
        await Promise.all(uploadPromises)
        
        // 上传完成后，刷新文件列表以获取正确的服务器URL
        const currentFileList = this.data.fileList || []
        const uploadedFileIds = currentFileList
          .filter(file => file.needRefresh && typeof file.fileId === 'number')
          .map(file => file.fileId)
        
        if (uploadedFileIds.length > 0) {
          console.log('刷新已上传文件的URL:', uploadedFileIds)
          try {
            const refreshedFiles = await this.getFileList(uploadedFileIds)
            
            // 合并刷新后的文件信息
            const updatedFileList = currentFileList.map(file => {
              if (file.needRefresh && typeof file.fileId === 'number') {
                const refreshedFile = refreshedFiles.find(rf => rf.fileId == file.fileId)
                if (refreshedFile) {
                  return {
                    ...file,
                    url: refreshedFile.url,
                    name: refreshedFile.name || file.name,
                    needRefresh: false
                  }
                }
              }
              return file
            })
            
            this.setData({
              fileList: updatedFileList
            })
            
            // 重新触发change事件，传递更新后的文件列表
            this.triggerEvent('change', {
              key: this.data.config?.key,
              fileList: updatedFileList,
              fileIds: updatedFileList.map(file => file.fileId),
              removedFileIds: this.data.removedFileIds || [],
              uploadNum: updatedFileList.length
            })
            
            console.log('文件URL刷新完成')
          } catch (error) {
            console.error('刷新文件URL失败:', error)
          }
        }
        
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
        
      } catch (error) {
        console.error('批量上传失败:', error)
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
      } finally {
        wx.hideLoading()
      }
    },

    /**
     * 预览文件
     */
    previewFile(event) {
      console.log('预览文件 - 完整事件对象:', event)
      
      let detail = event.detail || {}
      let dataset = event.currentTarget?.dataset || {}
      
      const fileType = detail.fileType || detail.file?.fileType || dataset.fileType
      const url = detail.url || detail.file?.url || dataset.url
      const isImage = detail.isImage !== undefined ? detail.isImage : dataset.isImage
      const isVideo = detail.isVideo !== undefined ? detail.isVideo : dataset.isVideo
      
      console.log('预览参数:', { fileType, url, isImage, isVideo })
      
      if (!url) {
        console.error('预览失败: 找不到文件URL')
        wx.showToast({
          title: '文件URL无效',
          icon: 'none'
        })
        return
      }
      
      wx.showLoading({
        title: '加载中...',
        mask: true
      })
      
      // 根据URL后缀判断文件类型
      const urlLower = url.toLowerCase()
      const isImageByUrl = /\.(jpg|jpeg|png|gif|bmp|webp|tiff)(\?.*)?$/i.test(urlLower)
      const isVideoByUrl = /\.(mp4|avi|mov|wmv|flv|3gp|m4v|mkv|webm)(\?.*)?$/i.test(urlLower)
      
      const actualIsImage = isImage || isImageByUrl
      const actualIsVideo = isVideo || isVideoByUrl
      
      console.log('最终文件类型判断:', { actualIsImage, actualIsVideo })
      
      if (actualIsImage) {
        // 图片预览
        const imageUrls = this.data.fileList.filter(file => file.isImage).map(file => file.url)
        wx.previewImage({
          current: url,
          urls: imageUrls,
          success: () => {
            console.log('图片预览成功')
          },
          fail: (err) => {
            console.error('图片预览失败:', err)
            wx.showToast({
              title: '图片预览失败',
              icon: 'none'
            })
          },
          complete: () => {
            wx.hideLoading()
          }
        })
      } else if (actualIsVideo) {
        // 视频预览
        if (wx.previewMedia) {
          wx.previewMedia({
            sources: [{
              url: url,
              type: 'video'
            }],
            current: 0,
            success: () => {
              console.log('视频预览成功')
            },
            fail: (err) => {
              console.error('视频预览失败:', err)
              wx.hideLoading()
              wx.showModal({
                title: '视频预览失败',
                content: '当前环境不支持视频预览，是否下载查看？',
                success: (res) => {
                  if (res.confirm) {
                    this.downloadAndSaveVideo(url)
                  }
                }
              })
            },
            complete: () => {
              wx.hideLoading()
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '不支持视频预览',
            icon: 'none'
          })
        }
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '不支持的文件类型',
          icon: 'none'
        })
      }
    },

    /**
     * 下载并保存视频
     */
    downloadAndSaveVideo(url) {
      wx.showLoading({ title: '下载中...' })
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.saveVideoToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: '已保存到相册',
                icon: 'success'
              })
            },
            fail: () => {
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          })
        },
        fail: () => {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
        },
        complete: () => {
          wx.hideLoading()
        }
      })
    },

    /**
     * 删除文件
     */
    deleteFile(event) {
      console.log('删除文件事件:', event)
      
      const dataset = event.currentTarget.dataset
      const index = dataset.index
      const fileId = dataset.fileId
      const url = dataset.url
      
      console.log('删除参数:', { index, fileId, url })
      
      const fileList = this.data.fileList
      let targetFile = null
      let targetIndex = -1
      
      // 优先使用index
      if (index !== undefined && index >= 0 && index < fileList.length) {
        targetFile = fileList[index]
        targetIndex = index
      } else if (fileId) {
        targetIndex = fileList.findIndex(file => file.fileId === fileId)
        if (targetIndex >= 0) {
          targetFile = fileList[targetIndex]
        }
      } else if (url) {
        targetIndex = fileList.findIndex(file => file.url === url)
        if (targetIndex >= 0) {
          targetFile = fileList[targetIndex]
        }
      }
      
      if (!targetFile || targetIndex < 0) {
        console.error('找不到要删除的文件')
        wx.showToast({
          title: '删除失败：找不到文件',
          icon: 'none'
        })
        return
      }
      
      console.log('找到目标文件:', targetFile, '索引:', targetIndex)
      
      wx.showModal({
        title: '确认删除',
        content: `确定要删除文件"${targetFile.name || '未知文件'}"吗？`,
        success: (res) => {
          if (res.confirm) {
            console.log('用户确认删除文件:', targetFile.name || targetFile.fileId)
            
            const newFileList = [...fileList]
            
            console.log('删除操作:', {
              删除前数量: newFileList.length,
              删除的索引: targetIndex,
              删除的文件: targetFile
            })
            
            // 从数组中移除
            newFileList.splice(targetIndex, 1)
            
            console.log('删除操作:', {
              删除前数量: fileList.length,
              删除后数量: newFileList.length,
              删除的索引: targetIndex,
              删除的文件: targetFile
            })
            
            // 如果是已上传的文件（有数字ID），添加到删除列表
            if (targetFile.fileId && typeof targetFile.fileId === 'string' && !targetFile.fileId.startsWith('temp_')) {
              const numericId = parseInt(targetFile.fileId)
              if (!isNaN(numericId)) {
                const newRemovedFileIds = [...(this.data.removedFileIds || []), numericId]
                this.setData({
                  removedFileIds: newRemovedFileIds
                })
                console.log('已上传文件，添加到删除列表:', newRemovedFileIds)
              }
            }
            
            this.setData({
              fileList: newFileList
            })
            
            // 触发change事件
            this.triggerEvent('change', {
              key: this.data.config?.key,
              fileList: newFileList,
              fileIds: newFileList.map(file => file.fileId),
              removedFileIds: this.data.removedFileIds || [],
              uploadNum: newFileList.length
            })
            
            console.log('文件变更:', {
              fileIds: newFileList.map(file => file.fileId),
              uploadNum: newFileList.length,
              removedFileIds: this.data.removedFileIds
            })
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            
            console.log('删除操作完成，新文件列表:', newFileList.map(f => f.fileId))
          }
        }
      })
    },
  }
}) 