/* pages_sub/pages/ccs/ams/createRepairForm/components/uploadXJ/index.wxss */

.van-cell {
  background: white;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.van-field__label {
  font-size: 32rpx;
  color: #242424;
  margin-bottom: 16rpx;
  font-weight: 400;
  line-height: 1.5;
}

.van-field__label.red-dot::before {
  content: '*';
  color: #ee0a24;
  margin-right: 8rpx;
}

.van-field__error-message {
  color: #ee0a24;
  font-size: 24rpx;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 上传按钮区域 */
.upload-section {
  width: 100%;
  margin-bottom: 24rpx;
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 32rpx;
  background: #f7f8fa;
  border: 2rpx dashed #dcdee0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-btn:active {
  background: #ebedf0;
  border-color: #c8c9cc;
}

.upload-btn-icon {
  font-size: 48rpx;
  color: #969799;
}

.upload-btn-text {
  font-size: 28rpx;
  color: #646566;
  font-weight: 500;
}

.upload-hint {
  display: block;
  font-size: 24rpx;
  color: #969799;
  margin-top: 12rpx;
  text-align: center;
}

/* 文件列表显示 */
.file-list-display {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.file-item-display {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #ebedf0;
  background: #f7f8fa;
}

/* 文件预览区域 */
.file-preview-area {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.file-preview-area:active {
  transform: scale(0.95);
}

.file-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f0f1f5;
}

/* 视频预览样式 */
.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  z-index: 1;
}

/* 文件图标 */
.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  background-color: #f0f1f5;
  color: #c8c9cc;
}

/* 文件操作 */
.file-actions {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  z-index: 2;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(238, 10, 36, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.delete-btn:active {
  background-color: rgba(200, 16, 46, 0.9);
  transform: scale(0.9);
}

.delete-icon {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .file-item-display {
    width: 160rpx;
    height: 160rpx;
  }
  
  .video-play-icon {
    width: 48rpx;
    height: 48rpx;
    font-size: 24rpx;
  }
  
  .delete-btn {
    width: 32rpx;
    height: 32rpx;
  }
  
  .delete-icon {
    font-size: 18rpx;
  }
} 