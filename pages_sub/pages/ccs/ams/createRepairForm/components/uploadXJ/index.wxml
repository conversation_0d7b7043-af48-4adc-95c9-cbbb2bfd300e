<!--pages_sub/pages/ccs/ams/createRepairForm/components/uploadXJ/index.wxml-->
<view class="van-cell">
  <view wx:if="{{config.label}}" class="{{config.required?'van-field__label red-dot':'van-field__label'}}">
    <text>{{config.label}}</text>
  </view>
  
  <!-- 文件选择按钮 -->
  <view class="upload-section">
    <view class="upload-btn" bind:tap="directChooseFile">
      <text class="upload-btn-icon">📷</text>
      <text class="upload-btn-text">选择文件</text>
    </view>
    <text class="upload-hint">支持图片、视频 ({{fileList.length}}/{{config.attrs.max || 5}})</text>
  </view>
  
  <!-- 文件列表显示 -->
  <view wx:if="{{fileList.length > 0}}" class="file-list-display">
    <view wx:for="{{fileList}}" wx:key="fileId" wx:for-index="idx" class="file-item-display">
      <!-- 文件预览区域 - 可点击预览 -->
      <view class="file-preview-area" bind:tap="previewFile" data-url="{{item.url}}" data-file-type="{{item.fileType}}" data-is-image="{{item.isImage}}" data-is-video="{{item.isVideo}}">
        <image wx:if="{{item.isImage}}" src="{{item.url}}" class="file-preview" mode="aspectFill" />
        <view wx:elif="{{item.isVideo}}" class="video-preview">
          <image src="{{item.url}}" class="file-preview" mode="aspectFill" />
          <view class="video-play-icon">▶</view>
        </view>
        <view wx:else class="file-preview file-icon">📄</view>
      </view>
      
      <!-- 删除按钮 -->
      <view class="file-actions">
        <view class="delete-btn" catchtap="deleteFile" data-file-id="{{item.fileId}}" data-url="{{item.url}}" data-index="{{idx}}">
          <text class="delete-icon">✕</text>
        </view>
      </view>
    </view>
  </view>
  
  <view class="van-field__error-message" wx:if="{{isErrorMessage}}">
    {{isErrorMessage}}
  </view>
</view> 