.fee-popup {
  max-height: 80vh;
}

.fee-container {
  padding: 24rpx;
}

.fee-header {
  text-align: center;
  padding: 20rpx 0 40rpx;
}

.fee-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.fee-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 收费标准图片容器 */
.fee-image-container {
  margin-bottom: 40rpx;
  text-align: center;
}

.fee-standard-image {
  width: 100%;
  max-width: 100%;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.image-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 16rpx;
  text-align: center;
}

/* 备用文本列表样式 */
.fee-list {
  margin-bottom: 40rpx;
}

.fee-item {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.fee-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.fee-item-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.fee-item-price {
  font-size: 28rpx;
  color: #f44;
  font-weight: bold;
}

.fee-item-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.fee-footer {
  padding: 20rpx 0;
}