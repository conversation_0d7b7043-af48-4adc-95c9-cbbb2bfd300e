<!-- 收费标准弹窗 -->
<van-popup
  show="{{ show }}"
  position="bottom"
  round
  bind:close="onClose"
  custom-class="fee-popup"
>
  <view class="fee-container">
    <view class="fee-header">
      <view class="fee-title">收费标准</view>
      <view class="fee-subtitle">以下为参考价格，实际费用以维修情况为准</view>
    </view>

    <!-- 收费标准图片显示 -->
    <view class="fee-image-container" wx:if="{{!showImageError}}">
      <image
        class="fee-standard-image"
        src="{{feeStandardImage}}"
        mode="widthFix"
        bind:tap="onImagePreview"
        bind:error="onImageError"
        show-menu-by-longpress="{{true}}"
      />
      <view class="image-tip">点击图片可放大查看</view>
    </view>

    <!-- 图片加载失败时显示文本列表 -->
    <view class="fee-list" wx:if="{{showImageError}}">
      <view class="fee-item" wx:for="{{feeList}}" wx:key="title">
        <view class="fee-item-header">
          <text class="fee-item-title">{{item.title}}</text>
          <text class="fee-item-price">{{item.fee}}</text>
        </view>
        <view class="fee-item-desc">{{item.desc}}</view>
      </view>
    </view>

    <view class="fee-footer">
      <van-button block type="primary" bind:click="onConfirm">我知道了</van-button>
    </view>
  </view>
</van-popup>