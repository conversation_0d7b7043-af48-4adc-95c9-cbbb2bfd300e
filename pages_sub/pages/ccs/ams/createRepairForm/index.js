// pages_sub/pages/ccs/ams/createRepairForm/index.js
const App =getApp()
import { DOMAIN, DOMAINXJ } from '../../../../../utils/server'
const baseXjUrl = DOMAINXJ
const baseUrl = DOMAIN
Page({
  /**
   * 页面的初始数据
   */
  data: {
    active: 0,
    tabList: [
      { name: '创建报修单', value: 'create' },
      { name: '历史记录', value: 'history' }
    ],
    showProgress: false, // 是否显示服务进度弹窗
    currentOrder: {}, // 当前选中的订单
    listViewH: '', // 列表视图高度
    historyList: [], // 历史记录列表
    
    // 日期查询相关
    startDate: '',
    endDate: '',
    showDatePicker: false,
    currentPickerDate: new Date().getTime(),
    // 预约时间选择器的日期范围（今天及以后）
    minDate: new Date().getTime(),
    maxDate: new Date(new Date().getFullYear() + 1, 11, 31).getTime(),
    // 历史记录查询选择器的日期范围（过去2年到今天）
    historyMinDate: new Date(new Date().getFullYear() - 2, 0, 1).getTime(),
    historyMaxDate: new Date().getTime(),
    datePickerType: '', // 'start' 或 'end'
    
    // 取消订单相关
    showCancelDialog: false, // 是否显示取消对话框
    cancelMemo: '', // 取消备注
    currentCancelOrder: null, // 当前要取消的订单
    
    // 修改模式相关
    isEditMode: false, // 是否为修改模式
    editingOrderId: '', // 正在修改的订单ID
    originalOrderData: null, // 原始订单数据
    isEnteringEditMode: false, // 是否正在进入修改模式（用于跳过chooseTab检查）
    // 创建报修单表单数据
    formData: {
      psiOrderName: '', // 维修单单号
      psiOrderType: '', // 服务类型
      psiOrderTypeName: '', // 服务类型名称
      customerID: '', // 客户ID
      customerName: '', // 客户名称
      customerTel: '', // 客户联系方式
      appointmentTime: '', // 客户预约时间
      startMailNo: '', // 送修运单号
      operatorId: '', // 运营商ID
      operatorName: '', // 运营商名称
      operatorAddress: '', // 运营商地址
      operatorDisplayText: '', // 运营商显示文本（名称+地址）
      operatorContactPerson: '', // 运营商联系人
      operatorContactPhone: '', // 运营商联系电话
      feedbackMemo: '', // 故障描述
      provinceCode: '', // 省编码
      cityCode: '', // 市编码
      countyCode: '', // 区编码
      provinceName: '', // 省名称
      cityName: '', // 市名称
      countyName: '', // 区名称
      latitude: '', // 工单纬度
      longitude: '', // 工单经度
      address: '', // 客户地址
      detailAddress: '', // 详细地址与门牌号
      status: 1, // 工单状态，默认未派工
      /* 状态对照表：
       * 1.未派工 2.已派工 3.已响应 4.已预约 5.已出发 
       * 6.已到位 7.维修中 8.费用结算 9.已付款 
       * 10.已完工 11.已评价 12.已取消
       */
      surveyStatus: 1, // 回访状态，默认待回访
      actualAppointmentTime: '', // 实际预约时间
      cancelMemo: '', // 取消备注
      cancelReason: '', // 取消原因
      cancelTime: '' // 取消时间
    },
    // 服务类型选项
    serviceTypeColumns: [
      { text: '上门服务', value: 1 },
      { text: '自己送修', value: 2 }
    ],
    showServiceTypePop: false,
    // 省市区选择相关
    showRegionPicker: false,
    region: [],
    regionText: '',
    hasCheckedRegion: false,
    hasStation: false,
    // 时间选择器相关
    showTimePicker: false,
    currentDate: new Date().getTime(),
    // 历史记录列表（移动到上面统一管理）
    isCommit: false,
    uploadNum: 0,
    fileRelationIds: [],
    uploadedFiles: [], // 已上传的文件列表
    removedFileIds: [], // 被删除的文件ID列表
    // 上传配置
    uploadConfig: {
      key: 'fileRelationIds',
      attrs: {
        max: 5,
        accept: 'image/*,video/*', // 支持图片和视频
        capture: ['album', 'camera'], // 支持相册和拍摄
        uploadIcon: 'photograph'
      }
    },
    // 收费标准弹窗
    showFeeStandard: false,
    // 提交确认弹窗
    showSubmitConfirm: false,
    // 运营商选择弹窗
    showOperatorSelect: false,
    // 运营商列表
    operatorList: [],
    // 当前选中的运营商
    selectedOperator: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('onShow - 页面显示, active:', this.data.active, 'customerID:', this.data.formData.customerID)
    if (this.data.active === 1 && this.data.formData.customerID) {
      console.log('onShow - 条件满足，开始加载历史记录')
      // 确保日期范围已初始化
      if (!this.data.startDate || !this.data.endDate) {
        this.initDefaultDateRange()
      }
      this.loadHistoryList()
    } else {
      console.log('onShow - 条件不满足，跳过加载历史记录')
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置默认预约时间为当前时间+1小时
    const now = new Date()
    now.setHours(now.getHours() + 1)
    now.setMinutes(0, 0, 0) // 设置为整点
    
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    const hour = now.getHours().toString().padStart(2, '0')
    const minute = now.getMinutes().toString().padStart(2, '0')
    
    const defaultTime = `${year}-${month}-${day} ${hour}:${minute}`
    
      this.setData({
      'formData.appointmentTime': defaultTime,
      currentDate: now.getTime()
    })

    // 初始化默认查询日期（最近一周）
    this.initDefaultDateRange()
    
    // 获取微信用户OpenId
    this.getWxUserInfo()
  },

  /**
   * 获取默认日期范围（最近一周）
   */
  getDefaultDateRange() {
    const today = new Date()
    const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    
    const formatDate = (date) => {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }
    
    return {
      startDate: formatDate(oneWeekAgo),
      endDate: formatDate(today)
    }
  },

  /**
   * 初始化默认日期范围（最近一周）
   */
  initDefaultDateRange() {
    const dateRange = this.getDefaultDateRange()
    this.setData({
      startDate: dateRange.startDate,
      endDate: dateRange.endDate
    })
  },

  /**
   * 获取微信用户信息
   */
  getWxUserInfo() {
    wx.login({
      success: (res) => {
        // 发送 res.code 到新界后台换取 openId
        const url = baseXjUrl + "/api/AfterSalePsiRegion/GetOpenId";
        App.getHttp()._requestXJ(url, {
          code: res.code
        }, 'POST').then(result => {
          if (result && result.response && result.response.openid) {
            this.setData({
              'formData.customerID': result.response.openid
            })
            
            // 如果当前是历史记录tab，则加载历史记录
            console.log('getWxUserInfo success - 当前active状态:', this.data.active)
            if (this.data.active === 1) {
              console.log('getWxUserInfo success - 开始加载历史记录')
              // 确保日期范围已初始化
              if (!this.data.startDate || !this.data.endDate) {
                this.initDefaultDateRange()
              }
              this.loadHistoryList()
            } else {
              console.log('getWxUserInfo success - 不在历史记录tab，跳过加载历史记录')
            }
          }
        }).catch(error => {
          console.error('获取用户OpenId失败:', error)
          // 开发环境fallback：使用一个模拟的OpenId
          this.setData({
            'formData.customerID': 'mock_openid_123456'
          })
          
          // 如果当前是历史记录tab，则加载历史记录
          console.log('getWxUserInfo fallback - 当前active状态:', this.data.active)
          if (this.data.active === 1) {
            console.log('getWxUserInfo fallback - 开始加载历史记录')
            // 确保日期范围已初始化
            if (!this.data.startDate || !this.data.endDate) {
              this.initDefaultDateRange()
            }
            this.loadHistoryList()
          } else {
            console.log('getWxUserInfo fallback - 不在历史记录tab，跳过加载历史记录')
          }
        })
      },
      fail: (error) => {
        console.error('微信登录失败:', error)
      }
    })
  },



  /**
   * 打开省市区选择
   */
  openCitySelect() {
    this.setData({
      showRegionPicker: true
    })
  },

  /**
   * 关闭省市区选择
   */
  onCloseCitySelect() {
    this.setData({
      showRegionPicker: false
    })
  },

  /**
   * 省市区选择变化
   */
  citySelectChange(e) {
    const result = e.detail
    this.setData({
      showRegionPicker: false
    })
    
    if (result && result.length >= 3 && result[0].code && result[1].code && result[2].code) {
      this.setData({
        region: result,
        regionText: result.map(v => v.name).join(' ')
      })
      
      // 检查该区域是否有站点
      this.checkStationAvailability(result)
      
      // 加载该区域的运营商列表
      this.loadOperatorList(result)
    } else {
      wx.showToast({
        title: '请选择完整的省市区',
        icon: 'none'
      })
    }
  },

  /**
   * 加载运营商列表
   */
  loadOperatorList(regionValues) {
    if (!regionValues || regionValues.length < 2) {
      return
    }

    const param = {
      provinceCode: regionValues[0].code || '',
      cityCode: regionValues[1].code || ''
    }
    const url = baseUrl + "/api/vcs/dashboardAgency/agency/query";
    App.getHttp()._post(url, param, true).then(res => {
      if (res.code === 200 && res.content) {
        console.log('后端返回的运营商数据:', res.content)
        // 转换接口数据为我们需要的格式
        const operators = res.content.map(item => {
          console.log('运营商原始数据:', item)
          return {
            id: item.id,
            name: item.name || item.contactName, // 运营商名称优先取name字段
            address: item.regionName + item.addr,
            imageUrl: baseXjUrl +'/uploads/psi/images/xj.webp', // 默认图片
            contactPerson: item.contactName,
            contactPhone: item.contactPhone
          }
        })
        
        console.log('转换后的运营商数据:', operators)

        this.setData({
          operatorList: operators
        })
      }
    }).catch(error => {
      console.error('获取运营商列表失败:', error)
      wx.showToast({
        title: '获取运营商列表失败',
        icon: 'none'
      })
    })
  },

  /**
   * 点击选择运营商
   */
  onOperatorClick() {
    if (!this.data.hasCheckedRegion) {
      wx.showToast({
        title: '请先选择服务区域',
        icon: 'none'
      })
      return
    }

    if (!this.data.hasStation) {
      wx.showToast({
        title: '该区域暂无服务站点',
        icon: 'none'
      })
      return
    }

    this.setData({
      showOperatorSelect: true
    })
  },

  /**
   * 关闭运营商选择弹窗
   */
  onOperatorClose() {
    this.setData({
      showOperatorSelect: false
    })
  },

  /**
   * 选择运营商
   */
  onOperatorSelect(e) {
    const { operator } = e.detail
    console.log('选择的运营商信息:', operator)
    console.log('当前服务类型:', this.data.formData.psiOrderType)
    
    // 构建运营商显示文本（只显示地址）
    const operatorDisplayText = operator.address || ''
    
    this.setData({
      selectedOperator: operator,
      'formData.operatorId': operator.id,
      'formData.operatorName': operator.name,
      'formData.operatorAddress': operator.address,
      'formData.operatorDisplayText': operatorDisplayText,
      'formData.operatorContactPerson': operator.contactPerson,
      'formData.operatorContactPhone': operator.contactPhone,
      showOperatorSelect: false
    })
    
    console.log('设置后的收件人信息:', {
      contactPerson: operator.contactPerson,
      contactPhone: operator.contactPhone
    })

    // 如果是自己送修，自动填充收件信息
    if (this.data.formData.psiOrderType === 2) {
      this.setData({
        'formData.address': operator.address,
        'formData.detailAddress': `收件人：${operator.contactPerson}，电话：${operator.contactPhone}`
      })
      console.log('自己送修模式 - 已填充收件信息')
    }
    
    // 显示选择成功的提示
    wx.showToast({
      title: '运营商选择成功',
      icon: 'success',
      duration: 1500
    })
    
    // 验证收件人信息是否正确设置
    setTimeout(() => {
      console.log('验证当前表单数据:', {
        operatorContactPerson: this.data.formData.operatorContactPerson,
        operatorContactPhone: this.data.formData.operatorContactPhone,
        psiOrderType: this.data.formData.psiOrderType
      })
    }, 100)
  },

  /**
   * 检查站点可用性
   */
  checkStationAvailability(regionValues) {
    try {
      // 确保regionValues是数组且有足够的元素
      if (!Array.isArray(regionValues) || regionValues.length < 3) {
        this.setData({
          hasCheckedRegion: true,
          hasStation: false
        })
        return
      }
      
      const param = {
        provinceCode: regionValues[0].code || '',
        cityCode: regionValues[1].code || '',
        countyCode: regionValues[2].code || ''
      }
      const url =baseXjUrl +"/api/AfterSalePsiRegion/CheckRegionHasStation";
      App.getHttp()._requestXJ(url, param, 'POST')
      .then(res => {
        console.log('检查站点返回数据:', res)
        
        if (res && res.success === true && res.status === 200) {
          // 根据返回的数据结构判断是否有站点
          const hasStation = res.response && res.response.hasStation === true
          
          this.setData({
            hasCheckedRegion: true,
            hasStation: hasStation
          })
          
          if (!hasStation) {
            wx.showToast({
              title: '该区域暂无服务站点',
              icon: 'none'
            })
          }
        } else {
          // 后端返回了错误
          console.error('检查服务站点失败:', res)
          this.setData({
            hasCheckedRegion: true,
            hasStation: false
          })
          
          const errorMsg = res?.msg || '检查服务站点失败'
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          })
        }
      }).catch(error => {
        console.error('检查服务站点失败:', error)
        
        this.setData({
          hasCheckedRegion: true,
          hasStation: false
        })
        
        wx.showToast({
          title: error && error.msg ? error.msg : '检查服务站点失败',
          icon: 'none',
          duration: 3000
        })
      })
    } catch (error) {
      console.error('检查服务站点异常:', error)
      this.setData({
        hasCheckedRegion: true,
        hasStation: false
      })
    }
  },

  /**
   * 切换标签页
   */
  chooseTab(e) {
    const index = e.detail.index
    console.log('chooseTab - 切换到tab:', index)
    
    // 如果正在进入修改模式，跳过检查
    if (this.data.isEnteringEditMode) {
      console.log('chooseTab - 正在进入修改模式，跳过检查')
      this.setData({ isEnteringEditMode: false })
      this.actualSwitchTab(index)
      return
    }
    
    // 如果在修改模式，提示用户
    if (this.data.isEditMode) {
      wx.showModal({
        title: '退出修改',
        content: '确定要退出修改模式吗？未保存的修改将丢失。',
        success: (res) => {
          if (res.confirm) {
            this.resetEditMode()
            this.actualSwitchTab(index)
          }
        }
      })
      return
    }
    
    this.actualSwitchTab(index)
  },

  /**
   * 实际执行Tab切换
   */
  actualSwitchTab(index) {
    this.setData({
      active: index
    }, () => {
      // 在 setData 回调中处理历史记录逻辑，确保 active 状态已更新
    if (index === 1) {
        console.log('chooseTab - 切换到历史记录tab，当前active:', this.data.active)
        
        // 切换到历史记录时清空列表并重新加载
        this.setData({
          historyList: []
        })
        
        // 确保日期范围已初始化（如果还没有设置的话）
        if (!this.data.startDate || !this.data.endDate) {
          console.log('chooseTab - 初始化默认日期范围')
          this.initDefaultDateRange()
        }
        
        // 只有在有customerID的情况下才加载历史记录
        if (this.data.formData.customerID) {
          console.log('chooseTab - 已有customerID，直接加载历史记录:', this.data.formData.customerID)
      this.loadHistoryList()
        } else {
          console.log('chooseTab - 没有customerID，先获取用户信息')
          // 如果没有customerID，先获取用户信息
          this.getWxUserInfo()
    }
      }
    })
  },

  /**
   * 服务类型选择
   */
  onServiceTypeClick() {
    this.setData({
      showServiceTypePop: true
    })
  },

  serviceTypeCancel() {
    this.setData({
      showServiceTypePop: false
    })
  },

  serviceTypeConfirm(e) {
    const item = e.detail.value
    const updateData = {
      'formData.psiOrderType': item.value,
      'formData.psiOrderTypeName': item.text,
      showServiceTypePop: false
    }
    
    // 如果切换到"自己送修"
    if (item.value === 2) {
      // 清空上门服务相关字段
      updateData['formData.appointmentTime'] = ''
      
      // 如果已选择运营商，自动填充收件信息
      if (this.data.selectedOperator) {
        updateData['formData.address'] = this.data.selectedOperator.address
        updateData['formData.detailAddress'] = `收件人：${this.data.selectedOperator.contactPerson}，电话：${this.data.selectedOperator.contactPhone}`
        // 确保运营商联系人信息也被设置
        updateData['formData.operatorContactPerson'] = this.data.selectedOperator.contactPerson
        updateData['formData.operatorContactPhone'] = this.data.selectedOperator.contactPhone
      } else {
        updateData['formData.address'] = ''
        updateData['formData.detailAddress'] = ''
        // 提示用户选择运营商
        setTimeout(() => {
          wx.showToast({
            title: '请先选择运营商',
            icon: 'none',
            duration: 2000
          })
        }, 500)
      }
    } else {
      // 如果切换到"上门服务"，清空运单号和地址信息
      updateData['formData.startMailNo'] = ''
      updateData['formData.address'] = ''
      updateData['formData.detailAddress'] = ''
    }
    
    this.setData(updateData)
  },

  /**
   * 表单字段变化处理
   */
  onFieldChange(e) {
    const { field } = e.currentTarget.dataset
    const value = e.detail
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 地址选择
   */
  onAddressClick() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'formData.address': res.address + res.name,
          'formData.latitude': res.latitude.toString(),//工单纬度
          'formData.longitude': res.longitude.toString(),//工单经度
          'formData.provinceName': res.provinceName,
          'formData.cityName': res.cityName,
          'formData.countyName': res.countyName
        })
      },
      fail: (err) => {
        console.error('选择地址失败', err)
        // 根据错误类型显示不同提示
        if (err.errMsg.includes('key') || err.errMsg.includes('referer')) {
          wx.showToast({
            title: '定位服务未配置，请联系管理员',
            icon: 'none',
            duration: 2000
          })
        } else if (err.errMsg.includes('auth')) {
          // 用户拒绝授权
          wx.showModal({
            title: '提示',
            content: '需要获取您的地理位置，请确认授权',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      this.onAddressClick()
                    }
                  }
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '选择地址失败，请重试',
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },

  /**
   * 定位获取地址
   */
  onLocationClick() {
    // 检查是否已选择服务区域
    if (!this.data.hasCheckedRegion || !this.data.hasStation) {
      wx.showToast({
        title: '请先选择服务区域',
        icon: 'none',
        duration: 2000
      })
      return
    }

    wx.showLoading({
      title: '打开地图...',
      mask: true
    })

    // 直接使用微信的位置选择接口，支持定位和地图选择
    wx.chooseLocation({
      success: (res) => {
        wx.hideLoading()
        console.log('选择位置结果:', res)
        
        // 组合完整地址
        const fullAddress = res.address + (res.name ? res.name : '')
        
        // 从地址中解析省市区信息
        const adInfo = this.parseAddressInfo(res.address)
        
        // 验证地址是否在服务区域内
        this.validateAddressInRegion(fullAddress, adInfo, res.latitude, res.longitude)
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('选择位置失败:', err)
        
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要定位权限',
            content: '需要获取您的地理位置，请在设置中开启定位权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      this.onLocationClick()
                    }
                  }
                })
              }
            }
          })
        } else if (err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '已取消选择',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: '地址获取失败，请重试',
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },

  /**
   * 从地址字符串中解析省市区信息
   */
  parseAddressInfo(address) {
    // 简单的地址解析，匹配常见的省市区格式
    const provinceRegex = /(.*?省|.*?自治区|.*?市)/
    const cityRegex = /(.*?市|.*?盟|.*?州)/
    const districtRegex = /(.*?区|.*?县|.*?市)/
    
    const adInfo = {
      province: '',
      city: '', 
      district: ''
    }
    
    // 解析省份
    const provinceMatch = address.match(provinceRegex)
    if (provinceMatch) {
      adInfo.province = provinceMatch[1]
      address = address.replace(provinceMatch[1], '')
    }
    
    // 解析城市
    const cityMatch = address.match(cityRegex)
    if (cityMatch) {
      adInfo.city = cityMatch[1]
      address = address.replace(cityMatch[1], '')
    }
    
    // 解析区县
    const districtMatch = address.match(districtRegex)
    if (districtMatch) {
      adInfo.district = districtMatch[1]
    }
    
    console.log('解析的地址信息:', adInfo)
    return adInfo
  },

  /**
   * 验证地址是否在服务区域内
   */
  validateAddressInRegion(address, adInfo, latitude, longitude) {
    const selectedRegion = this.data.region
    
    // 检查省市区是否匹配
    let isInRegion = false
    let regionMatch = ''
    
    if (selectedRegion.length >= 3) {
      // 检查区县级匹配
      const selectedCounty = selectedRegion[2]?.name || ''
      if (selectedCounty && adInfo.district && selectedCounty.includes(adInfo.district)) {
        isInRegion = true
        regionMatch = `${adInfo.province}${adInfo.city}${adInfo.district}`
      }
    }
    
    if (!isInRegion && selectedRegion.length >= 2) {
      // 检查城市级匹配
      const selectedCity = selectedRegion[1]?.name || ''
      if (selectedCity && adInfo.city && selectedCity.includes(adInfo.city)) {
        isInRegion = true
        regionMatch = `${adInfo.province}${adInfo.city}`
      }
    }
    
    if (!isInRegion && selectedRegion.length >= 1) {
      // 检查省级匹配
      const selectedProvince = selectedRegion[0]?.name || ''
      if (selectedProvince && adInfo.province && selectedProvince.includes(adInfo.province)) {
        isInRegion = true
        regionMatch = adInfo.province
      }
    }
    
    if (isInRegion) {
      // 地址在服务区域内，更新表单数据
    this.setData({
        'formData.address': address,
        'formData.latitude': latitude.toString(),
        'formData.longitude': longitude.toString()
      })
      
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
    } else {
      // 地址不在服务区域内，提示用户
      const currentRegionText = this.data.regionText
      wx.showModal({
        title: '地址区域不匹配',
        content: `定位地址：${regionMatch || '未知区域'}\n服务区域：${currentRegionText}\n\n您的当前位置不在选择的服务区域内，是否仍要使用此地址？`,
        confirmText: '仍要使用',
        cancelText: '重新选择',
        success: (res) => {
          if (res.confirm) {
            // 用户确认使用此地址
            this.setData({
              'formData.address': address,
              'formData.latitude': latitude.toString(),
              'formData.longitude': longitude.toString()
            })
            
            wx.showToast({
              title: '地址已设置',
              icon: 'success'
    })
          }
        }
      })
    }
  },

  /**
   * 预约时间选择
   */
  onAppointmentTimeClick() {
    this.setData({
      showTimePicker: true
    })
  },

  /**
   * 时间选择器确认
   */
  onTimePickerConfirm(e) {
    const date = new Date(e.detail)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    
    const formattedTime = `${year}-${month}-${day} ${hour}:${minute}`
    
    this.setData({
      'formData.appointmentTime': formattedTime,
      showTimePicker: false
    })
  },

  /**
   * 时间选择器取消
   */
  onTimePickerCancel() {
    this.setData({
      showTimePicker: false
    })
  },

  /**
   * 时间选择器关闭
   */
  onTimePickerClose() {
    this.setData({
      showTimePicker: false
    })
  },

  /**
   * 文件上传处理 - 适配批量上传
   */
  fileChange(e) {
    console.log('fileChange事件接收到数据:', e.detail)
    
    const { fileIds, fileList, removedFileIds, uploadNum, key } = e.detail
    
    // 更新删除的文件ID列表
    if (removedFileIds && Array.isArray(removedFileIds) && removedFileIds.length > 0) {
    this.setData({
        removedFileIds: removedFileIds
      })
      console.log('更新删除文件列表:', removedFileIds)
    }
    
    // 更新文件信息
    const newFileIds = Array.isArray(fileIds) ? fileIds : []
    const newUploadNum = uploadNum !== undefined ? uploadNum : newFileIds.length
    
    this.setData({
      fileRelationIds: newFileIds,
      uploadNum: newUploadNum,
      uploadedFiles: fileList || []
    })
    
    console.log('文件变更:', {
      fileIds: newFileIds,
      uploadNum: newUploadNum,
      removedFileIds: removedFileIds
    })
  },

  /**
   * 点击收费标准
   */
  onFeeStandardClick() {
    this.setData({
      showFeeStandard: true
    })
  },

  /**
   * 关闭收费标准弹窗
   */
  onFeeStandardClose() {
    this.setData({
      showFeeStandard: false
    })
  },

  /**
   * 确认收费标准
   */
  onFeeStandardConfirm() {
    this.setData({
      showFeeStandard: false
    })
  },

  /**
   * 提交报修单
   */
  onSureBtnClick() {
    // 先显示确认弹窗
    this.setData({
      showSubmitConfirm: true
    })
  },

  /**
   * 取消提交
   */
  onSubmitCancel() {
    this.setData({
      showSubmitConfirm: false
    })
  },

  /**
   * 确认提交
   */
  onSubmitConfirm() {
    this.setData({
      showSubmitConfirm: false,
      isCommit: true
    })

    const { formData } = this.data
    const mobilePattern = /^1[3456789]\d{9}$/
    let message = ''

    // 表单验证
    if (!this.data.regionText) {
      message = '请先选择服务区域'
    } else if (!this.data.hasStation) {
      message = '该区域暂无服务站点，请选择其他区域'
    } else if (!formData.customerName) {
      message = '客户名称不能为空'
    } else if (!formData.customerTel) {
      message = '客户联系方式不能为空'
    } else if (!mobilePattern.test(formData.customerTel)) {
      message = '客户联系方式格式不正确'
    } else if (!formData.psiOrderType) {
      message = '服务类型不能为空'
    } else if (!formData.operatorId) {
      message = '请选择运营商'
    } else if (formData.psiOrderType === 1 && !formData.appointmentTime) {
      message = '上门时间不能为空'
    } else if (!formData.feedbackMemo) {
      message = '故障描述不能为空'
    } else if (formData.psiOrderType === 1 && !formData.address) {
      message = '客户地址不能为空'
    } else if (formData.psiOrderType === 1 && !formData.detailAddress) {
      message = '详细地址不能为空'
    }

    if (message) {
      wx.showToast({
        title: message,
        icon: "none"
      })
      this.setData({
        isCommit: false
      })
      return
    }

    // 提交数据 - 使用新的批量上传流程
    const param = {
      ...formData,
      // 添加省市区信息
      provinceCode: this.data.region[0]?.code || '',
      provinceName: this.data.region[0]?.name || '',
      cityCode: this.data.region[1]?.code || '',
      cityName: this.data.region[1]?.name || '',
      countyCode: this.data.region[2]?.code || '',
      countyName: this.data.region[2]?.name || ''
    }

    // 根据是否为修改模式选择不同的API和文件处理方式
    let url, successMsg
    if (this.data.isEditMode) {
      url = baseXjUrl + "/api/AfterSalePsiOrder/UpdateOrder"
      param.id = this.data.editingOrderId
      // 修改模式：传递新增和删除的文件ID
      param.addFileIds = this.data.fileRelationIds || []
      param.removeFileIds = this.data.removedFileIds || []
      successMsg = '修改成功'
    } else {
      url = baseXjUrl + "/api/AfterSalePsiOrder/CreateOrder"
      // 新建模式：传递所有文件ID
      param.fileIds = this.data.fileRelationIds || []
      successMsg = '提交成功'
    }

    App.getHttp()._requestXJ(url, param, 'POST').then(res => {
      console.log(this.data.isEditMode ? '修改报修单返回结果:' : '创建报修单返回结果:', res)
      
      // 检查返回状态
      if (res && res.success === true && res.status === 200) {
        // 使用弹窗显示成功信息
        const modalTitle = this.data.isEditMode ? '修改成功' : '提交成功'
        const modalContent = this.data.isEditMode 
          ? '报修单已成功修改，您可以在历史记录中查看更新后的信息。' 
          : '报修单已成功提交，我们将尽快为您安排服务，您可以在历史记录中查看进度。'
        
        wx.showModal({
          title: modalTitle,
          content: modalContent,
          showCancel: false,
          confirmText: '查看记录',
          confirmColor: '#1989fa',
          success: (res) => {
            if (res.confirm) {
              if (this.data.isEditMode) {
                // 修改模式：重置修改状态并切换到历史记录
                this.resetEditMode()
              } else {
                // 新建模式：重置表单
          this.resetForm()
              }
          // 切换到历史记录
          this.setData({
            active: 1
          })
          this.loadHistoryList()
        }
          }
      })
      } else {
        // 后端返回了错误信息
        const errorMsg = res?.msg || (this.data.isEditMode ? '修改失败，请重试' : '提交失败，请重试')
        wx.showToast({
          title: errorMsg,
          icon: "none",
          duration: 3000
        })
        console.error(this.data.isEditMode ? '修改报修单失败:' : '创建报修单失败:', res)
      }
    }).catch(error => {
      console.error('提交失败', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: "none",
        duration: 3000
      })
    }).finally(() => {
      this.setData({
        isCommit: false
      })
    })
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      // 重置修改模式状态
      isEditMode: false,
      editingOrderId: '',
      originalOrderData: null,
      
      formData: {
        psiOrderName: '',
        psiOrderType: '',
        psiOrderTypeName: '',
        customerID: '',
        customerName: '',
        customerTel: '',
        appointmentTime: '',
        startMailNo: '',
        operatorId: '',
        operatorName: '',
        operatorAddress: '',
        operatorDisplayText: '',
        operatorContactPerson: '',
        operatorContactPhone: '',
        feedbackMemo: '',
        provinceCode: '',
        cityCode: '',
        countyCode: '',
        provinceName: '',
        cityName: '',
        countyName: '',
        latitude: '',
        longitude: '',
        address: '',
        detailAddress: '',
        status: 1,
        surveyStatus: 1,
        actualAppointmentTime: '',
        cancelMemo: '',
        cancelReason: '',
        cancelTime: ''
      },
      selectedOperator: null,
      fileRelationIds: [],
      uploadedFiles: [],
      removedFileIds: [],
      uploadNum: 0,
      region: [],
      regionText: '',
      hasCheckedRegion: false,
      hasStation: false
    })
    this.initPage()
  },



  /**
   * 加载历史记录
   */
  loadHistoryList() {
    // 确保日期范围已初始化（如果还没有设置的话）
    if (!this.data.startDate || !this.data.endDate) {
      console.log('loadHistoryList - 初始化默认日期范围')
      const dateRange = this.getDefaultDateRange()
      this.setData({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      })
    }
    
    console.log('loadHistoryList - 开始加载历史记录, customerID:', this.data.formData.customerID, '日期范围:', this.data.startDate, '到', this.data.endDate)
    
    // 直接调用真实API
    
    const param = {
      customerID: this.data.formData.customerID, // 使用OpenId过滤当前用户的历史记录
      startDate: this.data.startDate,
      endDate: this.data.endDate
    }

    const url = baseXjUrl + "/api/AfterSalePsiOrder/GetOrderList";
    App.getHttp()._requestXJ(url, param, 'POST').then(res => {
      console.log('获取历史记录返回结果:', res)
      
      if (res && res.success === true && res.status === 200) {
        // 后端返回的数据结构：res.response（直接是历史记录数组）
        const historyData = res.response || []
        
        // 为每个订单添加操作权限并处理字段映射
        const processedData = historyData.map(item => {
          let fileList = item.files || item.fileList || []
          
          // 处理文件URL，确保是完整路径
          if (fileList && fileList.length > 0) {
            fileList = fileList.map(file => ({
              ...file,
              url: file.fileUrl && file.fileUrl.startsWith('/') 
                ? baseXjUrl + file.fileUrl 
                : (file.fileUrl || file.url),
              isImage: file.isImage !== undefined ? file.isImage : ['jpg', 'png', 'jpeg', 'bmp', 'gif'].includes(file.fileType?.toLowerCase()),
              isVideo: file.isVideo !== undefined ? file.isVideo : ['mp4', 'avi', '3gp', 'wmv', 'mov'].includes(file.fileType?.toLowerCase())
            }))
          }
          
          return {
            ...item,
            // 操作权限
            canEdit: item.status < 5, // 只有未派工状态才能修改
            canDelete: item.status < 5, // 未派工或已取消才能删除
            // 字段映射兼容
            fileList: fileList,
            files: fileList  // 确保两个字段都有
          }
        })
        
      this.setData({
          historyList: processedData
        })
        
        console.log('成功加载历史记录，共', processedData.length, '条数据')
        console.log('处理后的数据样例:', processedData[0])
        // 检查文件数据
        processedData.forEach((item, index) => {
          if (item.files || item.fileList) {
            console.log(`订单${index}文件数据:`, {
              orderId: item.id,
              files: item.files,
              fileList: item.fileList,
              mappedFileList: item.files || item.fileList || []
            })
          }
        })
      } else {
        console.error('获取历史记录失败:', res)
        const errorMsg = res?.msg || '获取历史记录失败'
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
        
        // 设置为空列表
        this.setData({
          historyList: []
        })
      }
    }).catch(error => {
      console.error('加载历史记录失败', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
        duration: 3000
      })
      
      // 设置为空列表
      this.setData({
        historyList: []
      })
    })
  
  },

  /**
   * 查看历史记录详情
   */
  onHistoryItemClick(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.historyList[index]
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/ams/createRepairForm/detail/index?id=${item.id}`
    })
  },

  /**
   * 显示服务进度
   */
  showProgress(e) {
    // 阻止事件冒泡，避免触发列表项点击
    if (e && e.stopPropagation) {
      e.stopPropagation
    }
    const index = e.currentTarget.dataset.index
    const order = this.data.historyList[index]
    
    // 设置当前订单的进度时间
    const progressOrder = {
      ...order,
      // 根据状态设置各个节点的时间
      createTime: order.createTime,
      appointmentTime: order.appointmentTime,
      departureTime: order.status >= 5 ? '2024-12-01 15:30:00' : '',
      arrivalTime: order.status >= 6 ? '2024-12-01 16:00:00' : '',
      repairStartTime: order.status >= 7 ? '2024-12-01 16:15:00' : '',
      settlementTime: order.status >= 8 ? '2024-12-01 17:30:00' : '',
      completeTime: order.status >= 8 ? '2024-12-01 17:45:00' : '',
      evaluateTime: order.status >= 9 ? '2024-12-01 18:00:00' : ''
    }

    this.setData({
      currentOrder: progressOrder,
      showProgress: true
    })
  },

  /**
   * 关闭服务进度弹窗
   */
  onProgressClose() {
    this.setData({
      showProgress: false
    })
  },

  // ==================== 日期查询相关方法 ====================

  /**
   * 点击开始日期
   */
  onStartDateClick() {
    this.setData({
      datePickerType: 'start',
      currentPickerDate: this.data.startDate ? new Date(this.data.startDate).getTime() : new Date().getTime(),
      showDatePicker: true
    })
  },

  /**
   * 点击结束日期
   */
  onEndDateClick() {
    this.setData({
      datePickerType: 'end',
      currentPickerDate: this.data.endDate ? new Date(this.data.endDate).getTime() : new Date().getTime(),
      showDatePicker: true
    })
  },

  /**
   * 日期选择器确认
   */
  onDatePickerConfirm(e) {
    const selectedDate = new Date(e.detail)
    const year = selectedDate.getFullYear()
    const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0')
    const day = selectedDate.getDate().toString().padStart(2, '0')
    const dateStr = `${year}-${month}-${day}`

    if (this.data.datePickerType === 'start') {
      this.setData({
        startDate: dateStr,
        showDatePicker: false
      })
    } else {
      this.setData({
        endDate: dateStr,
        showDatePicker: false
      })
    }
  },

  /**
   * 关闭日期选择器
   */
  onDatePickerClose() {
    this.setData({
      showDatePicker: false
    })
  },

  /**
   * 日期查询
   */
  onDateSearch() {
    if (!this.data.startDate || !this.data.endDate) {
      wx.showToast({
        title: '请选择查询日期范围',
        icon: 'none'
      })
      return
    }

    if (new Date(this.data.startDate) > new Date(this.data.endDate)) {
      wx.showToast({
        title: '开始日期不能大于结束日期',
        icon: 'none'
      })
      return
    }

    this.loadHistoryList()
  },

  /**
   * 重置日期查询
   */
  onDateReset() {
    this.initDefaultDateRange()
    this.loadHistoryList()
  },

  // ==================== 操作按钮相关方法 ====================

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.historyList[index]
    
    // 将完整数据存储到全局，避免URL传参长度限制和接口请求
    getApp().globalData.currentOrderDetail = item
    
    wx.navigateTo({
      url: `./detail/index?fromCache=true`
    })
  },



  /**
   * 修改订单
   */
  onEditOrder(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.historyList[index]
    console.log('开始修改订单:', item)
    console.log('订单数据详情:', JSON.stringify(item, null, 2))
    
    // 解析省市区信息（可能在不同字段中）
    const provinceCode = item.provinceCode || ''
    const cityCode = item.cityCode || ''
    const countyCode = item.countyCode || ''
    const provinceName = item.provinceName || ''
    const cityName = item.cityName || ''
    const countyName = item.countyName || ''
    
    // 构建region数组
    const regionArray = []
    if (provinceCode && provinceName) {
      regionArray.push({ code: provinceCode, name: provinceName })
    }
    if (cityCode && cityName) {
      regionArray.push({ code: cityCode, name: cityName })
    }
    if (countyCode && countyName) {
      regionArray.push({ code: countyCode, name: countyName })
    }
    
    // 构建regionText（保持与正常选择一致的格式）
    const regionParts = [provinceName, cityName, countyName].filter(part => part && part.trim())
    const regionText = regionParts.join(' ')
    
    console.log('解析的区域信息:', { regionArray, regionText })
    
    // 设置进入修改模式标识，并填充表单数据
    this.setData({
      // 进入修改模式标识（用于跳过chooseTab检查）
      isEnteringEditMode: true,
      
      // 修改模式标识
      isEditMode: true,
      editingOrderId: item.id,
      originalOrderData: { ...item },
      
      // 填充表单字段
      formData: {
        ...this.data.formData,
        customerName: item.customerName || '',
        customerTel: item.customerTel || '',
        psiOrderType: item.psiOrderType || '',
        psiOrderTypeName: item.psiOrderTypeName || '',
        feedbackMemo: item.feedbackMemo || '',
        appointmentTime: item.appointmentTime || '',
        startMailNo: item.startMailNo || '',
        customerAddress: item.customerAddress || item.address || '',
        detailAddress: item.detailAddress || '',
        latitude: item.latitude || '',
        longitude: item.longitude || '',
        operatorId: item.operatorId || '',
        operatorName: item.operatorName || '',
        operatorAddress: item.operatorAddress || '',
        operatorDisplayText: item.operatorAddress || '',
        operatorContactPerson: item.operatorContactPerson || '',
        operatorContactPhone: item.operatorContactPhone || '',
        fullAddress: item.fullAddress || item.address || '',
        address: item.address || item.customerAddress || '',
        provinceCode: provinceCode,
        cityCode: cityCode,
        countyCode: countyCode,
        provinceName: provinceName,
        cityName: cityName,
        countyName: countyName
      },
      
      // 填充服务区域信息
      region: regionArray,
      regionText: regionText,
      hasCheckedRegion: regionArray.length > 0,
      hasStation: true, // 已有订单说明当时有服务站点
      
      // 填充运营商信息
      selectedOperator: item.operatorId ? {
        id: item.operatorId,
        name: item.operatorName || '',
        address: item.operatorAddress || '',
        contactPerson: item.operatorContactPerson || '',
        contactPhone: item.operatorContactPhone || ''
      } : null,
      
      // 文件列表 - 适配批量上传
      fileRelationIds: item.fileRelationIds ? 
        (typeof item.fileRelationIds === 'string' ? 
          item.fileRelationIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : 
          Array.isArray(item.fileRelationIds) ? item.fileRelationIds : []
        ) : [],
      uploadedFiles: item.files || [],
      removedFileIds: [], // 修改模式下重置删除列表
      uploadNum: item.files ? item.files.length : 0, // 设置上传数量
      
      // 切换到创建页面
      active: 0
    })
    
    // 如果有区域信息，检查站点并加载运营商列表
    if (regionArray.length >= 2) {
      this.checkStationAvailability(regionArray)
      this.loadOperatorList(regionArray)
    }
    
    // 延迟刷新上传组件，确保文件数据正确加载
    setTimeout(() => {
      if (this.data.fileRelationIds && this.data.fileRelationIds.length > 0) {
        // 通过选择器获取上传组件并强制刷新
        const uploadComponent = this.selectComponent('#upload-xj') || this.selectComponent('upload-xj')
        if (uploadComponent) {
          console.log('强制刷新上传组件，fileIds:', this.data.fileRelationIds)
          uploadComponent.getFileList(this.data.fileRelationIds)
        }
      }
    }, 500)
    
      wx.showToast({
      title: '已进入修改模式',
      icon: 'success'
    })
  },

  /**
   * 删除/取消订单
   */
  onDeleteOrder(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.historyList[index]
    
    // 设置当前要取消的订单信息
    this.setData({
      currentCancelOrder: {
        ...item,
        index: index
      },
      cancelMemo: '', // 清空之前的备注
      showCancelDialog: true // 显示取消对话框
    })
  },

  /**
   * 取消备注输入
   */
  onCancelMemoInput(e) {
    const value = e.detail.value || ''
    console.log('取消备注输入:', value)
    
    this.setData({
      cancelMemo: value
    })
  },

  /**
   * 关闭取消对话框
   */
  onCloseCancelDialog() {
    this.setData({
      showCancelDialog: false,
      cancelMemo: '',
      currentCancelOrder: null
    })
  },

  /**
   * 确认取消订单
   */
  onConfirmCancel() {
    const { currentCancelOrder, cancelMemo } = this.data
    
    console.log('确认取消 - 当前备注:', cancelMemo, '长度:', cancelMemo ? cancelMemo.length : 0)
    
    if (!cancelMemo || cancelMemo.trim() === '') {
      wx.showToast({
        title: '请填写取消原因',
        icon: 'none'
      })
      return
    }

    // 调用取消接口
    this.cancelRepairOrder(currentCancelOrder.id, currentCancelOrder.index, cancelMemo.trim())
  },

  /**
   * 执行取消报修单
   */
  cancelRepairOrder(orderId, index, cancelMemo) {
    // 关闭对话框
    this.setData({
      showCancelDialog: false
    })

    // 调用取消接口
    const url = baseXjUrl + "/api/AfterSalePsiOrder/CancelOrder"
    const param = { 
      id: orderId,
      cancelMemo: cancelMemo // 取消备注
    }
    
    App.getHttp()._requestXJ(url, param, 'POST').then(res => {
      if (res && res.success === true && res.status === 200) {
        wx.showToast({
          title: '取消成功',
          icon: 'success'
        })
        
        // 重新加载列表以获取最新状态
    this.loadHistoryList()
      } else {
        const errorMsg = res?.msg || '取消失败，请重试'
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    }).catch(error => {
      console.error('取消失败', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }).finally(() => {
      // 清理数据
      this.setData({
        cancelMemo: '',
        currentCancelOrder: null
      })
    })
  },

  /**
   * 重置修改模式
   */
  resetEditMode() {
    this.setData({
      isEditMode: false,
      editingOrderId: '',
      originalOrderData: null,
      
      // 重置表单数据，保留基本的用户信息
      formData: {
        ...this.data.formData,
        customerName: '',
        customerTel: '',
        psiOrderType: '',
        psiOrderTypeName: '',
        feedbackMemo: '',
        appointmentTime: '',
        startMailNo: '',
        customerAddress: '',
        detailAddress: '',
        latitude: '',
        longitude: '',
        operatorId: '',
        operatorName: '',
        operatorAddress: '',
        operatorDisplayText: '',
        operatorContactPerson: '',
        operatorContactPhone: '',
        fullAddress: ''
      },
      
      // 重置其他状态
      fileRelationIds: '',
      hasStation: false,
      hasCheckedRegion: false,
      regionText: '',
      selectedOperator: null,
      showOperatorSelect: false
    })
  },

  /**
   * 取消修改（按钮点击）
   */
  onCancelEdit() {
    wx.showModal({
      title: '取消修改',
      content: '确定要取消修改吗？未保存的修改将丢失。',
      success: (res) => {
        if (res.confirm) {
          this.resetEditMode()
          wx.showToast({
            title: '已取消修改',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({
      historyList: []
    })
    this.loadHistoryList()
  },


}) 