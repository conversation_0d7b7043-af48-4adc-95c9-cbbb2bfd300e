<!--pages_sub/pages/ccs/ams/createRepairForm/index.wxml-->
<view class="page-layout root-layout" id="root-layout">
  <!-- 修改模式提示 -->
  <view wx:if="{{isEditMode}}" class="edit-mode-tip">
    <van-icon name="edit" size="16" color="#1989fa" />
    <text>修改报修单模式</text>
  </view>

  <van-sticky offset-top="0" class="top-layout" id="top-layout">
    <van-tabs active="{{ active }}" bind:change="chooseTab">
      <van-tab wx:for="{{tabList}}" wx:key="index" title="{{item.name}}" name="{{index}}">
      </van-tab>
    </van-tabs>
  </van-sticky>

  <!-- 创建报修单 -->
  <view wx:if="{{active === 0}}" class="create-form">
    <!-- 省市区选择 -->
    <view class="header-title">
      <view class="header-baseInfo">服务区域</view>
    </view>
    
    <van-cell-group custom-class="custom-group-layout">
      <!-- 省市区选择 -->
      <van-field 
        value="{{ regionText }}" 
        label="服务区域" 
        placeholder="请选择省市区" 
        is-link 
        readonly 
        required 
        bindtap="openCitySelect" 
        custom-class="field-class" 
        input-align="right" 
        input-class="input-class" 
        label-class="label-class" 
      />
      
      <!-- 站点检查结果 -->
      <view wx:if="{{ hasCheckedRegion }}" class="region-result">
        <view wx:if="{{ hasStation }}" class="station-available">
          <van-icon name="success" color="#52c41a" />
          <text>该区域有服务站点，可以继续填写报修信息</text>
        </view>
        <view wx:else class="station-unavailable">
          <van-icon name="cross" color="#ff4d4f" />
          <text>该区域暂无服务站点，请选择其他区域</text>
        </view>
      </view>
    </van-cell-group>
    
    <!-- 基本信息（只在有站点时显示） -->
    <view wx:if="{{ hasStation }}" class="form-section">
      <view class="header-title">
        <view class="header-baseInfo">基本信息</view>
      </view>
      
      <van-cell-group custom-class="custom-group-layout">
      
        <!-- 收费标准 -->
        <van-field 
          value="点击查看收费标准" 
          label="收费标准" 
          is-link 
          readonly 
          bindtap="onFeeStandardClick" 
          custom-class="field-class" 
          input-align="right" 
          input-class="input-class" 
          label-class="label-class" 
        />
        <!-- 客户姓名 -->
        <van-field 
          value="{{ formData.customerName }}" 
          label="姓名" 
          placeholder="请输入客户姓名" 
          required 
          bind:change="onFieldChange" 
          data-field="customerName"
          custom-class="field-class" 
          input-align="right" 
          input-class="input-class" 
          label-class="label-class" 
        />
        
        <!-- 客户联系方式 -->
        <van-field 
          value="{{ formData.customerTel }}" 
          label="手机号" 
          placeholder="请输入手机号" 
          required 
          bind:change="onFieldChange" 
          data-field="customerTel"
          custom-class="field-class" 
          input-align="right" 
          input-class="input-class" 
          label-class="label-class" 
          type="number"
          maxlength="11"
        />
        
                 <!-- 运营商选择 -->
         <van-field 
           value="{{ formData.operatorDisplayText }}" 
           label="运营商" 
           placeholder="请选择运营商" 
           is-link 
           readonly 
           required 
           bindtap="onOperatorClick" 
           type="textarea"
           autosize="{{ {minHeight: 60, maxHeight: 120} }}"
           custom-class="field-class-textarea" 
           input-align="left" 
           input-class="textarea-class" 
           label-class="label-class" 
         />

        <!-- 服务类型 -->
        <van-field 
          value="{{ formData.psiOrderTypeName }}" 
          label="服务类型" 
          placeholder="请选择服务类型" 
          is-link 
          readonly 
          required 
          bindtap="onServiceTypeClick" 
          custom-class="field-class" 
          input-align="right" 
          input-class="input-class" 
          label-class="label-class" 
        />

        
        <!-- 上门时间 -->
        <van-field 
          wx:if="{{ formData.psiOrderType === 1 }}"
          value="{{ formData.appointmentTime }}" 
          label="上门时间" 
          placeholder="请选择上门时间" 
          is-link 
          readonly 
          required 
          bindtap="onAppointmentTimeClick" 
          custom-class="field-class" 
          input-align="right" 
          input-class="input-class" 
          label-class="label-class" 
        />
        
        <!-- 收件信息（自己送修时显示） -->
        <block wx:if="{{ formData.psiOrderType === 2 }}">
          <!-- 收件人 -->
          <van-field 
            value="{{ formData.operatorContactPerson || '请先选择运营商' }}" 
            label="收件人" 
            readonly 
            custom-class="field-class" 
            input-align="right" 
            input-class="input-class" 
            label-class="label-class" 
          />
          
          <!-- 联系电话 -->
          <van-field 
            value="{{ formData.operatorContactPhone || '请先选择运营商' }}" 
            label="联系电话" 
            readonly 
            custom-class="field-class" 
            input-align="right" 
            input-class="input-class" 
            label-class="label-class" 
          />
          
          <!-- 运单号 -->
          <van-field 
            value="{{ formData.startMailNo }}" 
            label="运单号" 
            placeholder="请输入送修运单号" 
            bind:change="onFieldChange" 
            data-field="startMailNo"
            custom-class="field-class" 
            input-align="right" 
            input-class="input-class" 
            label-class="label-class" 
          />
        </block>

        <!-- 客户地址 -->
        <van-field 
          wx:if="{{ formData.psiOrderType === 1 }}"
          value="{{ formData.address }}" 
          label="客户地址" 
          placeholder="请选择客户地址" 
          is-link 
          readonly 
          required 
          bindtap="onAddressClick" 
          type="textarea"
          autosize="{{ {minHeight: 60, maxHeight: 120} }}"
          custom-class="field-class-textarea" 
          input-align="left" 
          input-class="textarea-class" 
          label-class="label-class"
          use-right-icon-slot
        >
          <view slot="right-icon" class="address-actions">
            <van-icon name="location-o" size="20" color="#1989fa" catchtap="onLocationClick" />
          </view>
        </van-field>
        
        <!-- 详细地址与门牌号 -->
        <van-field 
          wx:if="{{ formData.psiOrderType === 1 }}"
          value="{{ formData.detailAddress }}" 
          label="详细地址" 
          placeholder="请输入详细地址与门牌号" 
          type="textarea" 
          autosize="{{ {minHeight: 60, maxHeight: 120} }}"
          maxlength="200" 
          required 
          bind:change="onFieldChange" 
          data-field="detailAddress"
          custom-class="field-class-textarea" 
          input-align="left" 
          input-class="textarea-class" 
          label-class="label-class" 
        />
        
        <!-- 故障描述 -->
        <van-field 
          value="{{ formData.feedbackMemo }}" 
          label="故障描述" 
          type="textarea" 
          placeholder="请输入故障描述" 
          autosize 
          show-word-limit 
          row="3" 
          maxlength="2000" 
          required 
          bind:change="onFieldChange" 
          data-field="feedbackMemo"
          custom-class="field-class-textarea" 
          input-align="right" 
          input-class="textarea-class" 
          label-class="label-class" 
        />
        
        <!-- 上传图片和视频 -->
        <view class="upload-pic-box">
          <view class="upload-pic">上传图片/视频（{{uploadNum}}/5）</view>
          <view class="uploader-box">
            <upload-xj 
              id="upload-xj"
              config="{{ uploadConfig }}" 
              initFileIds="{{ fileRelationIds }}"
              bind:change="fileChange" 
            />
          </view>
        </view>
      </van-cell-group>
      
      <view class="btn-box">
        <!-- 修改模式下的按钮组 -->
        <view wx:if="{{isEditMode}}" class="edit-mode-buttons">
          <van-button 
            block 
            plain
            bindtap="onCancelEdit" 
            custom-class="cancel-edit-btn"
          >
            取消修改
          </van-button>
          <van-button 
            wx:if="{{!isCommit}}" 
            block 
            bindtap="onSureBtnClick" 
            custom-class="btn-class flex_center"
          >
            保存修改
          </van-button>
          <van-button 
            wx:else 
            block 
            loading 
            custom-class="btn-class flex_center"
          >
            保存中...
          </van-button>
        </view>
        
        <!-- 新建模式下的按钮 -->
        <view wx:else>
          <van-button 
            wx:if="{{!isCommit}}" 
            block 
            bindtap="onSureBtnClick" 
            custom-class="btn-class flex_center"
          >
            提交
          </van-button>
          <van-button 
            wx:else 
            block 
            loading 
            custom-class="btn-class flex_center"
          >
            提交中...
          </van-button>
        </view>
      </view>
    </view>
    
    <!-- 服务类型选择器弹窗 -->
    <van-popup 
      show="{{ showServiceTypePop }}" 
      position="bottom" 
      custom-style="height: 60%;" 
      bind:click-overlay="serviceTypeCancel"
    >
      <van-picker 
        title="选择服务类型" 
        show-toolbar 
        columns="{{ serviceTypeColumns }}" 
        bind:confirm="serviceTypeConfirm" 
        bind:cancel="serviceTypeCancel" 
      />
    </van-popup>
    
    <!-- 省市区选择器弹窗 -->
    <van-popup 
      show="{{ showRegionPicker }}" 
      position="bottom" 
      bind:close="onCloseCitySelect" 
      round 
      :lock-scroll="true"
    >
      <city-select 
        bind:addressChange="citySelectChange" 
        bind:closeCitySelect="onCloseCitySelect" 
      />
    </van-popup>
    
    <!-- 时间选择器弹窗 -->
    <van-popup 
      show="{{ showTimePicker }}" 
      position="bottom" 
      bind:close="onTimePickerClose"
    >
      <van-datetime-picker
        type="datetime"
        value="{{ currentDate }}"
        min-date="{{ minDate }}"
        max-date="{{ maxDate }}"
        bind:confirm="onTimePickerConfirm"
        bind:cancel="onTimePickerCancel"
      />
    </van-popup>

    <!-- 运营商选择弹窗 -->
    <operator-select
      show="{{ showOperatorSelect }}"
      operator-list="{{ operatorList }}"
      bind:close="onOperatorClose"
      bind:select="onOperatorSelect"
    />
  </view>

  <!-- 历史记录 -->
  <view wx:if="{{active === 1}}" class="history-list">
    <!-- 日期查询区域 -->
    <view class="date-filter-section">
      <view class="date-filter-row">
        <view class="date-filter-item">
          <text class="filter-label">开始日期：</text>
          <van-field
            value="{{ startDate }}"
            placeholder="选择开始日期"
            readonly
            bind:tap="onStartDateClick"
            suffix-icon="calendar-o"
          />
        </view>
        <view class="date-filter-item">
          <text class="filter-label">结束日期：</text>
          <van-field
            value="{{ endDate }}"
            placeholder="选择结束日期"
            readonly
            bind:tap="onEndDateClick"
            suffix-icon="calendar-o"
          />
        </view>
      </view>
      <view class="date-filter-buttons">
        <van-button size="small" type="primary" bind:tap="onDateSearch">查询</van-button>
        <van-button size="small" plain bind:tap="onDateReset">重置</van-button>
      </view>
    </view>

    <listView 
      class="scroll-layout" 
      viewHeightPx="{{listViewH}}" 
      bind:pullRefresh="onRefresh"
    >
      <view class="item-layout" wx:if="{{historyList.length > 0}}">
        <view 
          class="repair-order-card" 
          wx:for="{{historyList}}" 
          wx:key="index" 
          data-index="{{index}}"
        >
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="order-info">
              <view class="order-number">{{item.psiOrderName || '暂无单号'}}</view>
              <view class="order-status status-{{item.status}}">{{item.statusName || '未知状态'}}</view>
            </view>
            <view class="order-time">{{item.createTime}}</view>
          </view>
          
          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="content-row">
              <text class="label">客户信息：</text>
              <text class="value">{{item.customerName}} {{item.customerTel}}</text>
            </view>
            <view class="content-row">
              <text class="label">服务类型：</text>
              <text class="value">{{item.psiOrderType === 1 ? '上门服务' : '自己送修'}}</text>
            </view>
            
            <!-- 上门服务显示预约时间和地址 -->
            <block wx:if="{{item.psiOrderType === 1}}">
              <view class="content-row">
                <text class="label">预约时间：</text>
                <text class="value">{{item.appointmentTime}}</text>
              </view>
              <view class="content-row">
                <text class="label">服务地址：</text>
                <text class="value">{{item.fullAddress || item.address}}</text>
              </view>
            </block>
            
            <!-- 自己送修显示运营商和运单号 -->
            <block wx:else>
              <view class="content-row">
                <text class="label">运营商：</text>
                <text class="value">
                  <text wx:if="{{item.operatorName}}" class="operator-name">{{item.operatorName}}</text>
                  <text wx:if="{{item.operatorName && item.operatorAddress}}" class="operator-separator">\n</text>
                  <text wx:if="{{item.operatorAddress}}" class="operator-address">{{item.operatorAddress}}</text>
                </text>
              </view>
              <view class="content-row" wx:if="{{item.startMailNo}}">
                <text class="label">运单号：</text>
                <text class="value">{{item.startMailNo}}</text>
              </view>
            </block>
            
            <view class="content-row">
              <text class="label">故障描述：</text>
              <text class="value">{{item.feedbackMemo}}</text>
            </view>
          </view>
          
          <!-- 卡片底部操作按钮 -->
          <view class="card-footer">
            <view class="footer-left">
              <view class="progress-btn" catch:tap="showProgress" data-index="{{index}}">
                <van-icon name="clock-o" />
                <text>服务进度</text>
              </view>
            </view>
            <view class="footer-right">
              <van-button size="small" plain bind:tap="onViewDetail" data-index="{{index}}">查看详情</van-button>
              <van-button 
                size="small" 
                type="primary" 
                bind:tap="onEditOrder" 
                data-index="{{index}}"
                wx:if="{{item.canEdit}}"
              >
                修改
              </van-button>
              <van-button 
                size="small" 
                type="danger" 
                bind:tap="onDeleteOrder" 
                data-index="{{index}}"
                wx:if="{{item.canDelete}}"
              >
               取消
              </van-button>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" wx:else>
        <van-empty description="暂无报修记录" />
      </view>
    </listView>

    <!-- 日期选择器 -->
    <van-popup show="{{showDatePicker}}" position="bottom" bind:close="onDatePickerClose">
      <van-datetime-picker
        type="date"
        value="{{currentPickerDate}}"
        min-date="{{historyMinDate}}"
        max-date="{{historyMaxDate}}"
        bind:confirm="onDatePickerConfirm"
        bind:cancel="onDatePickerClose"
      />
    </van-popup>
  </view>
</view>

<!-- 收费标准组件 -->
<fee-standard
  show="{{showFeeStandard}}"
  bind:close="onFeeStandardClose"
  bind:confirm="onFeeStandardConfirm"
/>

<!-- 提交确认弹窗 -->
<van-dialog
  use-slot
  title="{{isEditMode ? '确认修改' : '确认提交'}}"
  show="{{ showSubmitConfirm }}"
  show-cancel-button
  confirm-button-text="{{isEditMode ? '确认修改' : '确认提交'}}"
  cancel-button-text="我再想想"
  bind:confirm="onSubmitConfirm"
  bind:cancel="onSubmitCancel"
>
  <view class="submit-confirm-content">
    <view class="confirm-title">请确认已知晓以下内容：</view>
    <view class="confirm-items">
      <view class="confirm-item">1. 维修费用将根据实际维修情况收取</view>
      <view class="confirm-item">2. 上门服务费：50元/次</view>
      <view class="confirm-item">3. 检测维修费：100元起/次</view>
      <view class="confirm-item">4. 如需更换配件，费用另计</view>
    </view>
  </view>
</van-dialog>

<!-- 服务进度弹窗 -->
<van-popup
  show="{{ showProgress }}"
  position="bottom"
  round
  custom-style="padding-bottom: env(safe-area-inset-bottom);"
  bind:close="onProgressClose"
>
  <view class="progress-popup">
    <view class="progress-header">
      <text>服务进度</text>
      <van-icon name="cross" bindtap="onProgressClose" />
    </view>
    <view class="progress-content">
      <view class="progress-steps">
        <view class="step {{currentOrder.status >= 1 ? 'active' : ''}}" wx:if="{{true}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">创建</text>
            <text class="step-time">{{currentOrder.createTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 2 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">派工</text>
            <text class="step-time">{{currentOrder.assignTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 3 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">响应</text>
            <text class="step-time">{{currentOrder.responseTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 4 ? 'active' : ''}}" wx:if="{{currentOrder.psiOrderType === 1}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">预约</text>
            <text class="step-time">{{currentOrder.appointmentTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 5 ? 'active' : ''}}" wx:if="{{currentOrder.psiOrderType === 1}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">出发</text>
            <text class="step-time">{{currentOrder.departureTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 6 ? 'active' : ''}}" wx:if="{{currentOrder.psiOrderType === 1}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">到位</text>
            <text class="step-time">{{currentOrder.arrivalTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 7 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">维修中</text>
            <text class="step-time">{{currentOrder.repairStartTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 8 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">费用结算</text>
            <text class="step-time">{{currentOrder.settlementTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 9 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">已付款</text>
            <text class="step-time">{{currentOrder.paymentTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 10 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">完工</text>
            <text class="step-time">{{currentOrder.completeTime || ''}}</text>
          </view>
        </view>
        <view class="step {{currentOrder.status >= 11 ? 'active' : ''}}">
          <view class="step-dot"></view>
          <view class="step-info">
            <text class="step-title">评价</text>
            <text class="step-time">{{currentOrder.evaluateTime || ''}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 取消订单对话框 -->
<van-popup show="{{showCancelDialog}}" position="center" round bind:close="onCloseCancelDialog">
  <view class="cancel-dialog">
    <view class="dialog-header">
      <view class="dialog-title">取消报修单</view>
      <van-icon name="cross" class="close-icon" bindtap="onCloseCancelDialog" />
    </view>
    
    <view class="dialog-content">
      <view class="order-info">
        <text class="order-name">{{currentCancelOrder.psiOrderName || '报修单'}}</text>
        <text class="order-desc">{{currentCancelOrder.feedbackMemo}}</text>
      </view>
      
      <view class="memo-section">
        <view class="memo-label">取消原因 <text class="required">*</text></view>
        <view class="textarea-container">
          <textarea
            value="{{cancelMemo}}"
            placeholder="请输入取消原因"
            maxlength="200"
            show-confirm-bar="{{false}}"
            auto-height
            bindinput="onCancelMemoInput"
            class="cancel-textarea"
          />
          <view class="word-count">{{cancelMemo.length || 0}}/200</view>
        </view>
      </view>
    </view>
    
    <view class="dialog-footer">
      <van-button plain custom-class="cancel-btn" bindtap="onCloseCancelDialog">
        取消
      </van-button>
      <van-button type="danger" custom-class="confirm-btn" bindtap="onConfirmCancel">
        确认取消
      </van-button>
    </view>
  </view>
</van-popup>




