/* pages_sub/pages/ccs/ams/createRepairForm/detail/index.wxss */
page {
  background-color: #F2F2F2;
}

.page-layout {
  min-height: 100vh;
  padding: 24rpx;
}

.detail-container {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.section {
  margin-bottom: 24rpx;
}

.section-title {
  padding: 32rpx 32rpx 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #242424;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-list {
  padding: 0 32rpx;
}

.info-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  line-height: 40rpx;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
}

.info-item .label {
  color: #8c8c8c;
  min-width: 160rpx;
  flex-shrink: 0;
}

.info-item .value {
  color: #242424;
  flex: 1;
  word-break: break-all;
}

.info-item.full-width .label {
  margin-bottom: 16rpx;
  min-width: auto;
}

.info-item.full-width .value {
  width: 100%;
}

.address-text {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 状态样式 */
.status-text {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
}

.status-1 {
  background: #e6f7ff;
  color: #1890ff;
}

.status-2 {
  background: #fff7e6;
  color: #fa8c16;
}

.status-3 {
  background: #f6ffed;
  color: #52c41a;
}

.status-4 {
  background: #fff2e8;
  color: #fa541c;
}

.status-5 {
  background: #f0f5ff;
  color: #2f54eb;
}

.status-6 {
  background: #f9f0ff;
  color: #722ed1;
}

.status-7 {
  background: #fff1f0;
  color: #f5222d;
}

.status-8 {
  background: #f6ffed;
  color: #52c41a;
}

.status-9 {
  background: #e6fffb;
  color: #13c2c2;
}

.status-10 {
  background: #f5f5f5;
  color: #8c8c8c;
}

/* 可点击元素样式 */
.phone-number {
  color: #00b9c3 !important;
  text-decoration: underline;
}

.address-text {
  color: #00b9c3 !important;
  text-decoration: underline;
}

/* 导航栏相关样式 */
.navbar {
  background: #333;
  color: white;
  padding: 24rpx;
  text-align: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 500;
}

/* 图片和视频列表样式 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 32rpx;
  background: #fff;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-item:active {
  transform: scale(0.95);
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f7f8fa;
}

/* 图片包装器 */
.image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 视频预览样式 */
.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.play-icon {
  color: white;
  font-size: 24rpx;
  margin-left: 4rpx; /* 调整播放按钮的视觉居中 */
}

/* 错误状态样式 */
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 3;
  border: 1rpx dashed #dcdee0;
}

.error-icon {
  font-size: 32rpx;
  color: #c8c9cc;
  margin-bottom: 4rpx;
}

.error-text {
  font-size: 20rpx;
  color: #969799;
}

.error-tip {
  margin-top: 8rpx;
  font-size: 18rpx;
  color: #999;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .image-item {
    width: 160rpx;
    height: 160rpx;
  }
  
  .video-play-icon {
    width: 48rpx;
    height: 48rpx;
  }
  
  .play-icon {
    font-size: 20rpx;
  }
} 