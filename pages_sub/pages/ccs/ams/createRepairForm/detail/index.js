// pages_sub/pages/ccs/ams/createRepairForm/detail/index.js
import {  DOMAINXJ } from '../../../../../../utils/server'
const App = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    orderId: '',
    orderDetail: {},
    fileList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('Detail页面接收参数:', options)
    
    if (options.fromCache === 'true') {
      // 从全局数据获取订单详情，无需请求接口
      const orderDetail = getApp().globalData.currentOrderDetail
      if (orderDetail) {
        // 获取文件列表，尝试多种可能的字段名
        let fileList = []
        if (orderDetail.files && Array.isArray(orderDetail.files)) {
          fileList = orderDetail.files
        } else if (orderDetail.fileList && Array.isArray(orderDetail.fileList)) {
          fileList = orderDetail.fileList
        } else if (orderDetail.attachments && Array.isArray(orderDetail.attachments)) {
          fileList = orderDetail.attachments
        } else if (orderDetail.fileRelations && Array.isArray(orderDetail.fileRelations)) {
          fileList = orderDetail.fileRelations
        }
        
        // 处理文件URL，确保是完整路径
        if (fileList && fileList.length > 0) {
          fileList = fileList.map((file, index) => {
            let fileUrl = file.fileUrl || file.url
            
            console.log(`文件${index} 原始URL:`, fileUrl)
            
            // 确保URL是完整路径
            if (fileUrl) {
              if (fileUrl.startsWith('/')) {
                fileUrl = DOMAINXJ + fileUrl
              } else if (!fileUrl.startsWith('http') && !fileUrl.startsWith('data:')) {
                fileUrl = DOMAINXJ + '/' + fileUrl.replace(/^\/+/, '')
              }
            }
            
            console.log(`文件${index} 处理后URL:`, fileUrl)
            
            return {
              ...file,
              url: fileUrl,
              isImage: file.isImage !== undefined ? file.isImage : ['jpg', 'png', 'jpeg', 'bmp', 'gif', 'webp'].includes(file.fileType?.toLowerCase()),
              isVideo: file.isVideo !== undefined ? file.isVideo : ['mp4', 'avi', '3gp', 'wmv', 'mov'].includes(file.fileType?.toLowerCase()),
              showError: false // 初始不显示错误
            }
          })
        }
        
        console.log('文件数据调试:', {
          'orderDetail.files': orderDetail.files,
          'orderDetail.fileList': orderDetail.fileList,
          'orderDetail.attachments': orderDetail.attachments,
          'orderDetail.fileRelations': orderDetail.fileRelations,
          'finalFileList': fileList,
          'fileListLength': fileList.length
        })
        
      this.setData({
          orderId: orderDetail.id,
          orderDetail: orderDetail,
          fileList: fileList
        })
        console.log('从缓存加载订单详情:', orderDetail)
        
        // 清理全局数据
        getApp().globalData.currentOrderDetail = null
      } else {
        // 如果缓存数据不存在，提示错误并返回
        wx.showToast({
          title: '数据获取失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } else if (options.orderId || options.id) {
      // 兼容直接传ID的方式
      const orderId = options.orderId || options.id
      this.setData({ orderId })
      this.getOrderDetail(orderId)
    } else {
      wx.showToast({
        title: '缺少订单ID',
        icon: 'none'
      })
    }
    
    // 添加URL调试信息
    console.log('DOMAINXJ配置:', DOMAINXJ)
    console.log('当前页面URL:', getCurrentPages()[getCurrentPages().length - 1].route)
  },

  /**
   * 获取订单详情
   */
  async getOrderDetail(orderId) {
    console.log('getOrderDetail - 开始加载订单详情, orderId:', orderId)
    
    wx.showLoading({
      title: '加载中...',
      mask: true
    })
    
    try {
      // 使用现有的API调用方式
    const param = {
        id: orderId
      }

      const baseXjUrl = DOMAINXJ
      const url = baseXjUrl + "/api/AfterSalePsiOrder/GetOrderDetail"
      
      const res = await App.getHttp()._requestXJ(url, param, 'POST')
      console.log('获取订单详情返回结果:', res)

      if (res && res.success === true && res.status === 200) {
        const orderDetail = res.response || {}
        
        // 获取文件列表，尝试多种可能的字段名
        let fileList = []
        if (orderDetail.files && Array.isArray(orderDetail.files)) {
          fileList = orderDetail.files
        } else if (orderDetail.fileList && Array.isArray(orderDetail.fileList)) {
          fileList = orderDetail.fileList
        } else if (orderDetail.attachments && Array.isArray(orderDetail.attachments)) {
          fileList = orderDetail.attachments
        } else if (orderDetail.fileRelations && Array.isArray(orderDetail.fileRelations)) {
          fileList = orderDetail.fileRelations
        }
        
        // 处理文件URL，确保是完整路径
        if (fileList && fileList.length > 0) {
          fileList = await Promise.all(fileList.map(async (file, index) => {
            let fileUrl = file.fileUrl || file.url
            
            console.log(`文件${index} 原始URL:`, fileUrl)
            
            // 确保URL是完整路径
            if (fileUrl) {
              if (fileUrl.startsWith('/')) {
                fileUrl = DOMAINXJ + fileUrl
              } else if (!fileUrl.startsWith('http') && !fileUrl.startsWith('data:')) {
                fileUrl = DOMAINXJ + '/' + fileUrl.replace(/^\/+/, '')
              }
            }
            
            console.log(`文件${index} 处理后URL:`, fileUrl)
            
            // 测试URL可访问性（可选，用于调试）
            if (fileUrl) {
              this.testImageUrl(fileUrl).then(isAccessible => {
                console.log(`文件${index} URL可访问性:`, isAccessible)
              })
            }
            
            return {
              ...file,
              url: fileUrl,
              isImage: file.isImage !== undefined ? file.isImage : ['jpg', 'png', 'jpeg', 'bmp', 'gif', 'webp'].includes(file.fileType?.toLowerCase()),
              isVideo: file.isVideo !== undefined ? file.isVideo : ['mp4', 'avi', '3gp', 'wmv', 'mov'].includes(file.fileType?.toLowerCase()),
              showError: false // 初始不显示错误
            }
          }))
        }
        
        console.log('文件数据调试:', {
          'orderDetail.files': orderDetail.files,
          'orderDetail.fileList': orderDetail.fileList,
          'orderDetail.attachments': orderDetail.attachments,
          'orderDetail.fileRelations': orderDetail.fileRelations,
          'finalFileList': fileList,
          'fileListLength': fileList.length
        })
        
      this.setData({
          orderDetail: orderDetail,
          fileList: fileList
        })
        
        console.log('成功加载订单详情:', orderDetail)
        
      } else {
        console.error('获取订单详情失败:', res)
        const errorMsg = res?.msg || '获取订单详情失败'
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 测试图片URL是否可访问
   */
  testImageUrl(url) {
    return new Promise((resolve) => {
      if (!url) {
        resolve(false)
        return
      }
      
      wx.downloadFile({
        url: url,
        timeout: 5000, // 5秒超时
        success: (res) => {
          if (res.statusCode === 200) {
            console.log('URL测试成功:', url)
            resolve(true)
          } else {
            console.log('URL测试失败，状态码:', res.statusCode, url)
            resolve(false)
          }
        },
        fail: (err) => {
          console.log('URL测试失败:', err, url)
          resolve(false)
        }
      })
    })
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      1: '已创建',
      2: '未派工',
      3: '已派工',
      4: '已响应',
      5: '已出发',
      6: '已到位',
      7: '维修中',
      8: '已完工',
      9: '已评价',
      10: '已取消'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取服务类型文本
   */
  getServiceTypeText(type) {
    return type === 1 ? '上门服务' : '客户送修'
  },

  /**
   * 获取回访状态文本
   */
  getSurveyStatusText(status) {
    return status === 1 ? '待回访' : '已回访'
  },

  /**
   * 获取取消原因文本
   */
  getCancelReasonText(reason) {
    const reasonMap = {
      1: '客户取消',
      2: '无法上门',
      3: '虚假单'
    }
    return reasonMap[reason] || ''
  },

  /**
   * 获取收费类型文本
   */
  getCostTypeText(type) {
    const typeMap = {
      1: '免费',
      2: '收费',
      3: '保内',
      4: '保外'
    }
    return typeMap[type] || '未知'
  },

  /**
   * 拨打师傅电话
   */
  onCallWorker() {
    const { WorkerTel } = this.data.orderDetail
    if (WorkerTel) {
      wx.makePhoneCall({
        phoneNumber: WorkerTel,
        fail: () => {
          wx.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    }
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset
    const urls = this.data.fileList.filter(item => !item.isVideo).map(item => item.url)
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  /**
   * 预览文件（图片和视频）
   */
  onPreviewFile(e) {
    const { url, type, isVideo } = e.currentTarget.dataset
    const fileType = type ? type.toLowerCase() : ''
    
    console.log('预览文件:', { url, type, isVideo, fileType })
    
    if (!url) {
      wx.showToast({
        title: '文件URL无效',
        icon: 'none'
      })
      return
    }
    
    wx.showLoading({
      title: '加载中...',
      mask: true
    })
    
    // 图片类型预览
    if (!isVideo && ['jpg', 'png', 'jpeg', 'gif', 'bmp', 'webp'].includes(fileType)) {
      const urls = this.data.fileList.filter(item => !item.isVideo && item.url).map(item => item.url)
      wx.previewImage({
        current: url,
        urls: urls,
        success: () => {
          console.log('图片预览成功')
        },
        fail: (err) => {
          console.error('图片预览失败:', err)
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          })
        },
        complete: () => {
          wx.hideLoading()
        }
      })
      return
    }
    
    // 视频类型预览
    if (isVideo || ['mp4', 'avi', '3gp', 'wmv', 'mov', 'flv', 'mkv'].includes(fileType)) {
      if (wx.previewMedia) {
        wx.previewMedia({
          sources: [{
            url: url,
            type: 'video'
          }],
          current: 0,
          success: () => {
            console.log('视频预览成功')
          },
          fail: (err) => {
            console.error('视频预览失败:', err)
            // 降级处理：尝试下载查看
            wx.hideLoading()
            wx.showModal({
              title: '视频预览失败',
              content: '当前环境不支持视频预览，是否下载查看？',
              success: (res) => {
                if (res.confirm) {
                  wx.showLoading({ title: '下载中...' })
                  wx.downloadFile({
                    url: url,
                    success: (downloadRes) => {
                      wx.saveVideoToPhotosAlbum({
                        filePath: downloadRes.tempFilePath,
                        success: () => {
                          wx.showToast({
                            title: '已保存到相册',
                            icon: 'success'
                          })
                        },
                        fail: () => {
                          wx.showToast({
                            title: '保存失败',
                            icon: 'none'
                          })
                        }
                      })
                    },
                    fail: () => {
                      wx.showToast({
                        title: '下载失败',
                        icon: 'none'
                      })
                    },
                    complete: () => {
                      wx.hideLoading()
                    }
                  })
                }
              }
            })
          },
          complete: () => {
            wx.hideLoading()
          }
        })
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '不支持视频预览',
          icon: 'none'
        })
      }
      return
    }
    
    // 其他文件类型
    wx.hideLoading()
    wx.showToast({
      title: '不支持的文件类型',
      icon: 'none'
    })
  },

  /**
   * 拨打运营商电话
   */
  onCallOperator() {
    const { operatorContactPhone } = this.data.orderDetail
    if (operatorContactPhone) {
      wx.makePhoneCall({
        phoneNumber: operatorContactPhone,
        fail: () => {
          wx.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    }
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index
    const fileList = this.data.fileList
    
    if (fileList[index]) {
      console.error('图片加载失败:', {
        index: index,
        url: fileList[index].url,
        detail: e.detail,
        errorCode: e.detail?.errMsg,
        networkType: 'unknown'
      })
      
      // 获取网络状态
      wx.getNetworkType({
        success: (res) => {
          console.log('当前网络类型:', res.networkType)
          console.log('网络可用:', res.isConnected !== false)
        }
      })
      
      const newFileList = [...fileList]
      const originalUrl = fileList[index].url
      
      // 如果是第一次失败，尝试多种修复策略
      if (!fileList[index].retried) {
        console.log('尝试修复URL，原始URL:', originalUrl)
        
        // 尝试直接访问测试
        this.testUrlAccess(originalUrl).then(accessible => {
          console.log('URL直接访问测试结果:', accessible)
          
          if (!accessible) {
            // 如果无法访问，显示特殊错误信息
            newFileList[index] = {
              ...newFileList[index],
              showError: true,
              errorType: 'network', // 标记为网络错误
              retried: true
            }
            this.setData({ fileList: newFileList })
            
            // 显示网络提示
            wx.showModal({
              title: '图片加载失败',
              content: '当前网络环境无法访问服务器资源，可能是网络限制导致。是否尝试切换网络？',
              showCancel: true,
              confirmText: '重试',
              cancelText: '忽略',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 重置错误状态，重新尝试
                  newFileList[index] = {
                    ...newFileList[index],
                    showError: false,
                    retried: false
                  }
                  this.setData({ fileList: newFileList })
                }
              }
            })
          }
        })
        
        // 标记已重试
        newFileList[index] = {
          ...newFileList[index],
          retried: true
        }
        this.setData({ fileList: newFileList })
        return
      }
      
      // 第二次失败，显示错误状态
      newFileList[index] = {
        ...newFileList[index],
        showError: true,
        errorType: 'load_failed'
      }
      this.setData({ fileList: newFileList })
    }
  },

  /**
   * 测试URL访问性
   */
  testUrlAccess(url) {
    return new Promise((resolve) => {
      if (!url) {
        resolve(false)
        return
      }
      
      console.log('开始测试URL访问性:', url)
      
      // 尝试下载文件测试网络连通性
      wx.downloadFile({
        url: url,
        timeout: 8000, // 8秒超时
        success: (res) => {
          console.log('URL访问测试成功:', {
            url: url,
            statusCode: res.statusCode,
            tempFilePath: res.tempFilePath
          })
          resolve(res.statusCode === 200)
        },
        fail: (err) => {
          console.error('URL访问测试失败:', {
            url: url,
            error: err,
            errMsg: err.errMsg
          })
          resolve(false)
        }
      })
    })
  },

  /**
   * 修复图片URL
   */
  fixImageUrl(url) {
    if (!url) return url
    
    // 如果是相对路径，添加域名
    if (url.startsWith('/')) {
      return DOMAINXJ + url
    }
    
    // 如果URL格式不正确，尝试修复
    if (!url.startsWith('http') && !url.startsWith('data:')) {
      return DOMAINXJ + '/' + url
    }
    
    return url
  },

  /**
   * 检查网络环境并给出建议
   */
  checkNetworkEnvironment() {
    console.log('开始检查网络环境...')
    
    wx.getNetworkType({
      success: (res) => {
        console.log('网络检测结果:', res)
        
        const { networkType, isConnected } = res
        
        if (!isConnected) {
          wx.showModal({
            title: '网络异常',
            content: '当前设备未连接网络，请检查网络设置后重试',
            showCancel: false
          })
          return
        }
        
        // 检查是否在内网环境
        if (DOMAINXJ.includes('10.16.28.77') || DOMAINXJ.includes('192.168.') || DOMAINXJ.includes('172.')) {
          console.log('检测到内网地址，可能存在访问限制')
          
          if (networkType === 'wifi') {
            wx.showModal({
              title: '网络提示',
              content: '当前连接WiFi网络，但可能无法访问内网服务器。建议：\n1. 确保连接到正确的WiFi\n2. 或切换到移动数据网络\n3. 联系管理员配置网络权限',
              showCancel: true,
              confirmText: '重试加载',
              cancelText: '知道了',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  this.retryLoadAllImages()
                }
              }
            })
          } else {
            wx.showModal({
              title: '网络受限',
              content: '当前网络环境可能无法访问服务器资源，请联系管理员或尝试切换网络环境',
              showCancel: true,
              confirmText: '重试加载',
              cancelText: '知道了',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  this.retryLoadAllImages()
                }
              }
            })
          }
        }
      },
      fail: (err) => {
        console.error('网络检测失败:', err)
      }
    })
  },

  /**
   * 重试加载所有失败的图片
   */
  retryLoadAllImages() {
    const fileList = this.data.fileList
    const newFileList = fileList.map(file => ({
      ...file,
      showError: false,
      retried: false,
      errorType: null
    }))
    
    this.setData({ fileList: newFileList })
    
    wx.showToast({
      title: '正在重试加载...',
      icon: 'loading',
      duration: 2000
    })
  },

  /**
   * 查看地图
   */
  onViewMap() {
    const { latitude, longitude, address } = this.data.orderDetail
    if (latitude && longitude) {
      wx.openLocation({
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        name: address,
        address: address
      })
    } else {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      })
    }
  }
}) 