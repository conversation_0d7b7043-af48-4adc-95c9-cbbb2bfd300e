<!--pages_sub/pages/ccs/ams/createRepairForm/detail/index.wxml-->
<view class="page-layout">
  <view class="detail-container">
    <!-- 订单基本信息 -->
    <view class="section">
      <view class="section-title">基本信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">维修单号：</text>
          <text class="value">{{orderDetail.psiOrderName}}</text>
        </view>
        <view class="info-item">
          <text class="label">工单状态：</text>
          <text class="value">
            {{orderDetail.status === 1 ? '未派工' : 
            orderDetail.status === 2 ? '已派工' : 
            orderDetail.status === 3 ? '已响应' : 
            orderDetail.status === 4 ? '已预约' : 
            orderDetail.status === 5 ? '已出发' : 
            orderDetail.status === 6 ? '已到位' : 
            orderDetail.status === 7 ? '维修中' : 
            orderDetail.status === 8 ? '费用结算' : 
            orderDetail.status === 9 ? '已付款' : 
            orderDetail.status === 10 ? '已完工' : 
            orderDetail.status === 11 ? '已评价' : 
            orderDetail.status === 12 ? '已取消' : '未知状态'}}
          </text>
        </view>
        <view class="info-item">
          <text class="label">服务类型：</text>
          <text class="value">
            {{orderDetail.psiOrderType === 1 ? '上门服务' : 
            orderDetail.psiOrderType === 2 ? '自己送修':'未知' }}
            </text>
        </view>
    
      </view>
    </view>

    <!-- 客户信息 -->
    <view class="section">
      <view class="section-title">客户信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">客户姓名：</text>
          <text class="value">{{orderDetail.customerName}}</text>
        </view>
        <view class="info-item">
          <text class="label">联系方式：</text>
          <text class="value phone-number" >{{orderDetail.customerTel}}</text>
        </view>
      </view>
    </view>

    <!-- 上门服务信息 -->
    <block wx:if="{{orderDetail.psiOrderType === 1}}">
    <view class="section">
      <view class="section-title">预约信息</view>
      <view class="info-list">
        <view class="info-item">
            <text class="label">预约上门时间：</text>
          <text class="value">{{orderDetail.appointmentTime}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.actualAppointmentTime}}">
          <text class="label">实际时间：</text>
          <text class="value">{{orderDetail.actualAppointmentTime}}</text>
        </view>
          <view class="info-item full-width">
            <text class="label">客户地址：</text>
            <text class="value address-text" >{{orderDetail.fullAddress || orderDetail.address}}</text>
          </view>
          <view class="info-item full-width" wx:if="{{orderDetail.detailAddress}}">
            <text class="label">详细地址：</text>
            <text class="value address-text">{{orderDetail.detailAddress}}</text>
          </view>
          <!-- 上门服务的运营商信息 -->
          <view class="info-item full-width" wx:if="{{orderDetail.operatorAddress}}">
            <text class="label">服务商：</text>
            <text class="value address-text">{{orderDetail.operatorAddress}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 自己送修信息 -->
    <block wx:else>
      <view class="section">
        <view class="section-title">送修信息</view>
        <view class="info-list">
          <view class="info-item">
            <text class="label">运单号：</text>
            <text class="value">{{orderDetail.startMailNo}}</text>
          </view>
          <view class="info-item full-width">
            <text class="label">运营商地址：</text>
            <text class="value address-text">{{orderDetail.operatorAddress}}</text>
          </view>
          <view class="info-item">
            <text class="label">收件人：</text>
            <text class="value">{{orderDetail.operatorContactPerson}}</text>
          </view>
          <view class="info-item">
            <text class="label">联系电话：</text>
            <text class="value phone-number" bindtap="onCallOperator">{{orderDetail.operatorContactPhone}}</text>
          </view>
      </view>
    </view>
    </block>

    <!-- 故障信息 -->
    <view class="section">
      <view class="section-title">故障信息</view>
      <view class="info-list">
        <view class="info-item full-width">
          <text class="label">故障描述：</text>
          <text class="value">{{orderDetail.feedbackMemo}}</text>
        </view>
      </view>
    </view>

    <!-- 师傅信息 -->
    <view class="section" wx:if="{{orderDetail.WorkerID}}">
      <view class="section-title">师傅信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">师傅姓名：</text>
          <text class="value">{{orderDetail.WorkerName}}</text>
        </view>
        <view class="info-item">
          <text class="label">联系方式：</text>
          <text class="value phone-number" bindtap="onCallWorker">{{orderDetail.WorkerTel}}</text>
        </view>
      </view>
    </view>

    <!-- 费用信息 -->
    <view class="section" wx:if="{{orderDetail.TotalCost}}">
      <view class="section-title">费用信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">收费类型：</text>
          <text class="value">{{getCostTypeText(orderDetail.CostType)}}</text>
        </view>
        <view class="info-item">
          <text class="label">合计费用：</text>
          <text class="value">{{orderDetail.TotalCost}} 元</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.PartsCost > 0}}">
          <text class="label">配件费：</text>
          <text class="value">{{orderDetail.PartsCost}} 元</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.WorkCost > 0}}">
          <text class="label">服务费：</text>
          <text class="value">{{orderDetail.WorkCost}} 元</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.TravelCost > 0}}">
          <text class="label">上门费：</text>
          <text class="value">{{orderDetail.TravelCost}} 元</text>
        </view>
      </view>
    </view>

    <!-- 取消信息 -->
    <view class="section" wx:if="{{orderDetail.status === 12}}">
      <view class="section-title">取消信息</view>
      <view class="info-list">
        <view class="info-item" wx:if="{{orderDetail.cancelReason}}">
          <text class="label">取消原因：</text>
          <text class="value">{{getCancelReasonText(orderDetail.cancelReason)}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.cancelMemo}}">
          <text class="label">取消备注：</text>
          <text class="value">{{orderDetail.cancelMemo}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.cancelTime}}">
          <text class="label">取消时间：</text>
          <text class="value">{{orderDetail.cancelTime}}</text>
        </view>
      </view>
    </view>

    <!-- 图片和视频信息 -->
    <view class="section" wx:if="{{fileList.length > 0}}">
      <view class="section-title">相关文件</view>
      <view class="image-list">
        <view 
          class="image-item" 
          wx:for="{{fileList}}" 
          wx:key="index"
          bindtap="onPreviewFile"
          data-url="{{item.url}}"
          data-type="{{item.fileType || 'image'}}"
          data-is-video="{{item.isVideo}}"
        >
          <!-- 视频文件显示 -->
          <view wx:if="{{item.isVideo}}" class="video-preview">
            <image 
              src="{{item.url}}" 
              mode="aspectFill"
              webp="{{true}}"
              binderror="onImageError"
              data-index="{{index}}"
            />
            <view class="video-play-icon">
              <text class="play-icon">▶</text>
            </view>
          </view>
          <!-- 图片文件显示 -->
          <view wx:else class="image-wrapper">
            <image 
              src="{{item.url}}" 
              mode="aspectFill"
              webp="{{true}}"
              binderror="onImageError"
              data-index="{{index}}"
            />
          </view>
          
          <!-- 只在确实加载失败时显示错误状态 -->
          <view wx:if="{{item.showError}}" class="image-error">
            <text class="error-icon">
              {{item.errorType === 'network' ? '🌐' : '📷'}}
            </text>
            <text class="error-text">
              {{item.errorType === 'network' ? '网络受限' : '加载失败'}}
            </text>
            <view wx:if="{{item.errorType === 'network'}}" class="error-tip">
              <text>请检查网络设置</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间信息 -->
    <view class="section">
      <view class="section-title">时间信息</view>
      <view class="info-list">
        <view class="info-item" wx:if="{{orderDetail.createTime}}">
          <text class="label">创建时间：</text>
          <text class="value">{{orderDetail.createTime}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.updateTime}}">
          <text class="label">更新时间：</text>
          <text class="value">{{orderDetail.updateTime}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 