/* pages_sub/pages/ccs/ams/repairRegistration/index.wxss */
page {
  height: 100%;
  background-color: #F2F2F2;
}

/* 页面布局 */
.page-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.root-layout {
  height: 100%;
}

/* 标题样式 */
.header-title {
  display: flex;
  justify-content: space-between;
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx;
  color: #242424;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
  background: #fff;
}

.header-baseInfo {
  margin-left: 32rpx;
  font-weight: 500;
}

/* 头像上传样式 */
.avatar-section {
  background: #fff;
  margin-bottom: 24rpx;
}

.avatar-container {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-title {
  font-size: 32rpx;
  color: #242424;
  font-weight: 500;
  margin-bottom: 32rpx;
}

.avatar-upload {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 2rpx dashed #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: #fafafa;
  transition: all 0.3s ease;
}

.avatar-upload:active {
  transform: scale(0.95);
  background: #f0f0f0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
  text-align: center;
}

/* 头像上传状态 */
.avatar-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #1989fa;
  margin-top: 8rpx;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24rpx;
}

.custom-group-layout {
  padding-left: 32rpx;
  background-color: white;
}

.field-class {
  line-height: 96rpx !important;
  padding: 0 0 0 18rpx !important;
  display: flex;
  align-items: center;
  font-size: 32rpx !important;
  font-family: PingFang SC-Regular, PingFang SC !important;
  font-weight: 400 !important;
}

.label-class {
  color: #242424 !important;
  font-weight: 400 !important;
  font-size: 32rpx !important;
  font-family: PingFang SC-Regular, PingFang SC !important;
}

.input-class {
  height: 96rpx !important;
  line-height: 96rpx !important;
  margin-right: 32rpx !important;
}

.field-class::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0 !important;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-size: 28rpx !important;
}

.input-class .van-field__placeholder {
  color: #BDBDBD !important;
}

/* 提示信息样式 */
.tips-section {
  background: #fff;
  margin-bottom: 24rpx;
}

.tips-content {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-left: 6rpx solid #1989fa;
  background: #f0f9ff;
  margin: 24rpx 32rpx;
  border-radius: 8rpx;
}

.tips-content text {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #1989fa;
  line-height: 1.4;
}

/* 状态提示样式 */
.status-section {
  background: #fff;
  margin-bottom: 24rpx;
}

.status-content {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-left: 6rpx solid #07c160;
  background: #f0f9f0;
  margin: 24rpx 32rpx;
  border-radius: 8rpx;
}

.status-content text {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #07c160;
  font-weight: 500;
}

/* 按钮样式 */
.btn-box {
  margin-top: 24rpx;
  padding: 0 24rpx;
  }
  
.btn-class {
  height: 96rpx !important;
  border-radius: 8rpx !important;
  border: 0 !important;
  background: #00b9c3 !important;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF !important;
  font-size: 32rpx !important;
  line-height: 96rpx !important;
  }
  
.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* van组件样式覆盖 */
.van-icon-arrow {
  margin-left: -16rpx;
  margin-right: 32rpx;
  }
  
.van-icon-arrow::before {
  width: 32rpx;
  height: 32rpx;
}

/* 加载状态 */
.van-button--loading {
  opacity: 0.8;
}

/* 头像上传动画效果 */
.avatar-upload {
  transition: all 0.2s ease;
}

.avatar-upload:hover {
  border-color: #1989fa;
  background: #f8fdff;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .header-title {
    margin-top: 16rpx;
    margin-bottom: 16rpx;
}

  .avatar-container {
    padding: 24rpx;
  }
  
  .avatar-upload {
    width: 160rpx;
    height: 160rpx;
  }
  
  .tips-content,
  .status-content {
    margin: 16rpx 24rpx;
    padding: 20rpx 24rpx;
  }
  
  .btn-box {
    padding: 0 16rpx;
  }
} 