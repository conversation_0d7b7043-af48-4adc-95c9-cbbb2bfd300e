// pages_sub/pages/ccs/ams/repairRegistration/index.js
const App = getApp()
import { DOMAIN, DOMAINXJ } from '../../../../../utils/server'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      name: '',           // 姓名
      idNumber: '',       // 身份证号
      mobile: '',         // 手机号
      picUrl: ''          // 师傅头像URL
    },
    
    // 用户信息
    userInfo: null,       // 当前用户信息
    setsOfBooksId: '',    // 零售商ID
    
    // 状态
    isSubmitting: false,  // 是否正在提交
    isRegistered: false,  // 是否已注册
    isUploadingAvatar: false, // 是否正在上传头像
    
    // 原始数据（用于判断是否有变更）
    originalData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo()
    this.loadWorkerInfo()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载用户信息
    this.loadUserInfo()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    try {
      let user = wx.getStorageSync('userInfo')
      let setsOfBooksId = wx.getStorageSync('checkSetsOfBooksId')
      let setsOfBooksName = wx.getStorageSync('checkSetsOfBooksName')
      console.log('当前用户:', user?.userId, setsOfBooksName)
      
      if (!user || !user.userId) {
        wx.showModal({
          title: '提示',
          content: '请先登录',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }
      
      if (!setsOfBooksId) {
        wx.showModal({
          title: '提示', 
          content: '缺少零售商信息',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }
      
      this.setData({
        userInfo: user,
        setsOfBooksId: setsOfBooksId
      })
      
    } catch (error) {
      console.error('加载用户信息失败:', error)
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载师傅信息
   */
  loadWorkerInfo() {
    if (!this.data.userInfo) {
      // 如果用户信息还没加载完成，延迟执行
      setTimeout(() => {
        this.loadWorkerInfo()
      }, 100)
      return
    }

    const param = {
      retailerUserID: this.data.userInfo.id || this.data.userInfo.userId,
      retailerID: this.data.setsOfBooksId
    }

    console.log('查询师傅信息参数:', param)

    // 使用新的师傅信息查询接口
    const url = DOMAINXJ + "/api/AfterSaleTmUser/GetTmUser"
    
    App.getHttp()._requestXJ(url, param, 'POST').then(res => {
      console.log('师傅信息查询结果:', res)
      
      if (res && res.success === true && res.status === 200) {
        const workerData = res.response
        
        if (workerData && workerData.id) {
          // 处理头像URL，确保是完整路径
          let picUrl = workerData.picUrl || ''
          if (picUrl && picUrl.startsWith('/')) {
            picUrl = DOMAINXJ + picUrl
          }
          
          console.log('加载师傅信息，头像URL:', picUrl)
          
          // 已有师傅信息，填充表单
          this.setData({
            formData: {
              name: workerData.name || '',
              idNumber: workerData.idNumber || '',
              mobile: workerData.mobile || '',
              picUrl: picUrl
            },
            isRegistered: true,
            originalData: {
              name: workerData.name || '',
              idNumber: workerData.idNumber || '',
              mobile: workerData.mobile || '',
              picUrl: picUrl
            }
          })
        } else {
          // 没有师傅信息，显示空表单
          this.setData({
            isRegistered: false,
            originalData: null
          })
        }
      } else {
        console.log('暂无师傅信息，显示注册表单')
        this.setData({
          isRegistered: false,
          originalData: null
        })
      }
    }).catch(error => {
      console.error('查询师傅信息失败:', error)
      // 查询失败时也显示注册表单
      this.setData({
        isRegistered: false,
        originalData: null
      })
    })
  },

  /**
   * 表单字段变化处理
   */
  onFieldChange(e) {
    const { field } = e.currentTarget.dataset
    const value = e.detail || e.detail.value || ''
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 选择头像
   */
  onChooseAvatar() {
    if (this.data.isUploadingAvatar) {
      wx.showToast({
        title: '正在上传中...',
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'],
      success: (res) => {
        console.log('选择头像成功:', res)
        this.uploadAvatar(res.tempFiles[0].tempFilePath)
      },
      fail: (error) => {
        console.error('选择头像失败:', error)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 上传头像
   */
  uploadAvatar(tempFilePath) {
    this.setData({ isUploadingAvatar: true })

    wx.showLoading({
      title: '上传中...',
      mask: true
    })

    // 使用项目中的文件上传接口
    const url = DOMAINXJ + "/api/AfterSaleTmUser/FileUpload"
    
    wx.uploadFile({
      url: url,
      filePath: tempFilePath,
      name: 'file',
      formData: {
        'fileType': 'avatar'
      },
      header: {
        'Content-Type': 'multipart/form-data'
      },
      success: (res) => {
        console.log('头像上传成功:', res)
        
        try {
          const data = JSON.parse(res.data)
          if (data.success === true && data.status === 200) {
            let fileUrl = data.response.fileUrl || data.response.url
            
            // 确保URL是完整路径
            if (fileUrl && fileUrl.startsWith('/')) {
              fileUrl = DOMAINXJ + fileUrl
            }
            
            console.log('头像上传成功，完整URL:', fileUrl)
            
            this.setData({
              'formData.picUrl': fileUrl
            })
            wx.showToast({
              title: '头像上传成功',
              icon: 'success'
            })
          } else {
            throw new Error(data.msg || '上传失败')
          }
        } catch (error) {
          console.error('解析上传结果失败:', error)
          wx.showToast({
            title: '头像上传失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        console.error('头像上传失败:', error)
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        })
      },
      complete: () => {
        wx.hideLoading()
        this.setData({ isUploadingAvatar: false })
      }
    })
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { name, idNumber, mobile, picUrl } = this.data.formData
    
    // 验证头像
    /*
    if (!picUrl || picUrl.trim() === '') {
      wx.showToast({
        title: '请上传师傅头像',
        icon: 'none'
      })
      return false
    }
     */
    // 验证姓名
    if (!name || name.trim() === '') {
      wx.showToast({
        title: '请输入真实姓名',
        icon: 'none'
      })
      return false
    }
    
    if (name.length < 2 || name.length > 10) {
      wx.showToast({
        title: '姓名长度应在2-10个字符之间',
        icon: 'none'
      })
      return false
    }
    
    // 验证身份证号
    if (!idNumber || idNumber.trim() === '') {
      wx.showToast({
        title: '请输入身份证号',
        icon: 'none'
      })
      return false
    }
    
    // 身份证号码正则验证
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    if (!idCardRegex.test(idNumber)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return false
    }
    
    // 验证手机号
    if (!mobile || mobile.trim() === '') {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }
    
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(mobile)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }
    
    return true
  },

  /**
   * 头像加载成功
   */
  onAvatarLoad(e) {
    console.log('头像加载成功')
  },

  /**
   * 头像加载失败
   */
  onAvatarError(e) {
    console.error('头像加载失败:', e.detail)
    
    wx.showToast({
      title: '头像加载失败',
      icon: 'none'
    })
    
    // 可以选择清空头像URL，让用户重新上传
    // this.setData({
    //   'formData.picUrl': ''
    // })
  },

  /**
   * 保存师傅信息
   */
  onSave() {
    if (!this.validateForm()) {
      return
    }
    
    // 检查数据是否有变更
    if (this.data.isRegistered && this.data.originalData) {
      const hasChanged = Object.keys(this.data.formData).some(key => 
        this.data.formData[key] !== this.data.originalData[key]
      )
      
      if (!hasChanged) {
        wx.showToast({
          title: '数据无变更',
          icon: 'none'
        })
        return
      }
    }
    
    this.setData({ isSubmitting: true })
    
    const param = {
      retailerUserID: this.data.userInfo.id || this.data.userInfo.userId,
      retailerID: this.data.setsOfBooksId,
      name: this.data.formData.name.trim(),
      idNumber: this.data.formData.idNumber.trim(),
      mobile: this.data.formData.mobile.trim(),
      picUrl: this.data.formData.picUrl.trim()
    }
    
    console.log('保存师傅信息参数:', param)
    
    // 根据是否已注册选择不同的接口
    const url = this.data.isRegistered 
      ? DOMAINXJ + "/api/AfterSaleTmUser/UpdateTmUser"
      : DOMAINXJ + "/api/AfterSaleTmUser/RegisterTmUser"
    
    App.getHttp()._requestXJ(url, param, 'POST').then(res => {
      console.log('保存师傅信息结果:', res)
      
      if (res && res.success === true && res.status === 200) {
        const successMsg = this.data.isRegistered ? '信息更新成功' : '注册成功'
        
        wx.showModal({
          title: '成功',
          content: successMsg ,
          showCancel: false,
          success: () => {
            // 更新状态和原始数据
            this.setData({
              isRegistered: true,
              originalData: { ...this.data.formData }
            })
            
            // 可以选择返回上一页或刷新当前数据
            // wx.navigateBack()
          }
        })
      } else {
        const errorMsg = res?.msg || (this.data.isRegistered ? '更新失败，请重试' : '注册失败，请重试')
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }
    }).catch(error => {
      console.error('保存师傅信息失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
        duration: 3000
      })
    }).finally(() => {
      this.setData({ isSubmitting: false })
    })
  }
}) 