// pages_sub/pages/ccs/scanPDA/retail/index.js
const App = getApp()
var eventChannel = null
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dataList: [],
    inputValue: '',
    inputFocus: false,
    modalHidden: true,
    precent: '',
    order: {},
    mbbcRectTop: '24px',
    scrollH: '',
    supInfo: null,
    curCust: null,
    mode: 'add',
    modeTitle: '扫码抢模式-添加',
    modeOption: [
      { text: '扫码抢模式-扣减', value: 'reduce', url: '/api/psi/invCheckstand/decreaseInvCheckstand' },
      { text: '扫码抢模式-添加', value: 'add', url: '/api/psi/invCheckstand/addInvCheckstand' },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this
    eventChannel = this.getOpenerEventChannel()
    const findModeOption = this.data.modeOption.find(ele => ele.value === options.mode)
    this.setData({
      supInfo: wx.getStorageSync("supInfo"),
      curCust: wx.getStorageSync("curCust"),
      inputValue: '',
      mode: options.mode,
      modeTitle: findModeOption.text
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const that = this
    const mbbcRect = wx.getMenuButtonBoundingClientRect()
    if (mbbcRect)
      that.setData({
        mbbcRectTop: mbbcRect.top + 'px'
      })
    wx.getSystemInfo({
      success: (res) => {
        const query = wx.createSelectorQuery()
        query.select('#scorllview').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function (exceRes) {
          let scrollH2 = res.windowHeight - exceRes[0].top - 49
          that.setData({
            scrollH: scrollH2 + 'px',
            inputFocus: true
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getCheckoutPlatformList()
    console.log('=====onShow==');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    eventChannel.emit('acceptDataFromScanPDAPage', {
      refresh: true
    });
  },

  // 获取结账台列表
  getCheckoutPlatformList() {
    const url = "/api/psi/invCheckstand/getRtList";
    const params = {
      invoiceSetsOfBooksId: this.data.supInfo.setsOfBooksId,
      invoiceCustId: this.data.curCust.custId,
    };
    App.getHttp()
      ._postScanPDA(url, params)
      .then((res) => {
        if (res && res.length > 0) {
          this.setData({
            dataList: res.map(item => {
              return {
                itemId: item.itemId,
                itemName: item.itemName,
                specs: item.specs,
                scanCount: item.barCodeFlowResponseDTOList ? item.barCodeFlowResponseDTOList.length : 0
              }
            }),
          });
        } else {
          this.setData({
            dataList: []
          })
        }
      });
  },
  onClickConfirm() {
    console.error('==扫码input 代码click==')
    this.onInputConfirm({
      detail: {
        value: this.data.barCode
      }
    })
  },
  onInputChange(event) {
    const { value, keyCode } = event.detail
    if (value.indexOf('\n')>-1) {
      this.data.barCode = value.split('\n')[0]
    } else {
      this.data.barCode = value
    }
    // 删除键不触发
    if (keyCode === 8) return
    if (keyCode === 10 || value.length > 8) {
      this.onInputBlur()
      this.onClickConfirm()
    }
  },
  onInputConfirm(e) {
    console.error('==扫码input confirm==', e)
    if (!e.detail.value) {
      wx.showToast({
        title: '未检测到条码！',
        icon: "error",
      })
      return
    }
    let param = {
      invoiceSetsOfBooksId: this.data.supInfo.setsOfBooksId,
      invoiceCustId: this.data.curCust.custId,
      invoiceCustCode: this.data.curCust.custCode,
      standType: 1,
      barCode: e.detail.value
    }
    const findMode = this.data.modeOption.find(ele => ele.value === this.data.mode)
    App.getHttp()._postScanPDA(findMode.url, param).then(res => {
      wx.showToast({
        title: '扫码，成功！',
        icon: "success",
        duration: 2000
      })
      setTimeout(() => {
        this.getCheckoutPlatformList(true)
        this.setData({
          inputFocus: true,
          inputValue: ''
        })
      }, 2000);
    }).catch((errData) => {
      const errMsg = errData.chnDesc || '扫码，失败!'
      if (errMsg.length > 14) {
        wx.showModal({
          title: '异常提示',
          content: errMsg,
          showCancel: false,
          complete: () => {
            this.setData({
              inputFocus: true,
              inputValue: ''
            })
          }
        })
      } else {
        wx.showToast({
          title: errMsg,
          icon: 'none',
          duration: 2000
        })
        setTimeout(() => {
          this.setData({
            inputFocus: true,
            inputValue: ''
          })
        }, 2000);
      }

    })
  },

  // Modalcancel
  Modalcancel: function () {
    this.setData({
      modalHidden: true
    })
  },
  // Modalconfirm
  Modalconfirm: function () {
    let param = {
      billNo: this.data.billNo,
      billType: this.data.billType,
      headId: this.data.headId,
    }
    api._post('api/psi/scanBarcode/saveOrderByBillNo', param).then(res => {
      if (res) {
        wx.showToast({
          title: '提交成功',
          duration: 3000
        })
        App.globalData.barCodeList = []
        App.globalData.scanList = []
        setTimeout(function () {
          wx.switchTab({
            url: '/pages/index/index',
          })
        }, 3000)
      } else {
        wx.showToast({
          title: res.chnDesc,
          duration: 3000
        })
      }
    })
    this.setData({
      modalHidden: true
    })
  },
  showDetail: function (e) {
    wx.showModal({
      title: '商品信息',
      content: e.currentTarget.dataset.itemName + "/" + e.currentTarget.dataset.specs,
      showCancel: false,
      complete: () => {
        this.setData({
          inputFocus: true,
          inputValue: ''
        })
      }
    })
  },
  onSwitchMode() {
    wx.showActionSheet({
      alertText: '切换扫码枪模式',
      itemList: this.data.modeOption.map(ele => { return ele.text }),
      itemColor: '#0C92E0',
      success: (res) => {
        const { tapIndex } = res
        this.data.mode = this.data.modeOption[tapIndex].value
        this.setData({
          modeTitle: this.data.modeOption[tapIndex].text,
          inputFocus: true,
          inputValue: ""
        })
      }
    })
  },
  onClickInputFocus() {
    this.setData({
      inputFocus: true,
    })
  },
  onInputBlur() {
    console.error('==扫码 输入框焦点失去==')
    this.setData({
      inputFocus: false,
    })
  },
  goBack() {
    wx.navigateBack()
  }
})