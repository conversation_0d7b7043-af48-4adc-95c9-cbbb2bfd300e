/* pages_sub/pages/ccs/scanPDA/retail/index.wxss */
.top-img{
  position: relative;
  width: 100%;
  height: 640rpx;
}
.content-layout{
  position: absolute;
  top: 504rpx;
  left: 0rpx;
  right: 0rpx;
  bottom: 98rpx;
  border-top-left-radius: 48rpx;
  border-top-right-radius: 48rpx;
  background-color: white;
  box-shadow: 0 0 12rpx 0 rgba(45,45,75,0.04);
  padding-top: 32rpx;
  padding-left: 24rpx;
  padding-right: 24rpx;
  box-sizing: border-box;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.search-inner-layout{
  height: 64rpx;
  background: rgba(0,0,0,0.04);
  border-radius: 40rpx;
  border-radius: 40rpx;
  padding: 0 24rpx;
}
.search-inner-img{
  width: 48rpx;
  height: 48rpx;
}

.search-inner-input{
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  margin-left: 8rpx;
}
.search-inner-input-holder{
  color: rgba(0,0,0,0.25);
}
.table-layout{
  margin: 52rpx 16rpx 0rpx;
  box-sizing: border-box;
}
.table-index{
  width: 48rpx;
  text-align: center;
}
.table-count{
  width: 120rpx;
  text-align: center;
}
.row-layout{
  margin-top: 40rpx ;
}
.m-h-48{
 margin-left: 48rpx;
 margin-right: 48rpx;
}
.bottom-layout{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
  box-shadow: 0 -2px 6px 0 rgba(45,45,75,0.06);
  padding: 18rpx 24rpx 0 24rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.bt-layout{
  background: #0C92E0;
  border-radius: 12rpx;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
  height: 80rpx;
}

.modal-detail {
  height: 298rpx;
  padding: 0 48rpx;
  box-sizing: border-box;
}

.modal-title {
  font-family: PingFangSC-Regular;
  font-size: 36rpx;
  color: rgba(0, 0, 0, 0.75);
  text-align: center;
  line-height: 52rpx;
}

.modal-mid {
  text-align: left;
  margin-top: 40rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
}
.modal-s {
  color: #F73D47;
}
.modal-b {
  text-align: left;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
}

.header-layout{
  position: absolute;
  left: 24rpx;
  right: 24rpx;
  height: 48rpx;
 
}
.header-back{
  width: 48rpx;
  height: 48rpx;
  color: #FFFFFF !important;
  padding-right: 40px;
  
}
.header-content{
  font-family: PingFangSC-Medium;
  font-size: 36rpx;
  color: #FFFFFF;
  text-align: left;
  height: 36rpx;
  line-height: 36rpx;
}
.header-content .switch_icon{
  width: 36rpx;
  height: 36rpx;
  margin-left: 10rpx;
}