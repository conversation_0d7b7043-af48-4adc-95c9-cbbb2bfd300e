// pages_sub/pages/ccs/scanPDA/retail/index.js
const App = getApp()
var eventChannel = null
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dataList: [],
    inputValue: '',
    inputFocus: false,
    mbbcRectTop: '24px',
    scrollH: '',
    supInfo: null,
    curCust: null,
    headId: null,
    billNo: null,
    mode: 'add',
    modeTitle: '扫码抢模式-添加',
    modeOption: [
      { text: '扫码抢模式-扣减', value: 'reduce', url: '' },
      { text: '扫码抢模式-添加', value: 'add', url: '' },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    eventChannel = this.getOpenerEventChannel()
    // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('acceptDataFromOpenerPage', (event) => {
      this.setData({
        dataList: event.dataList
      })
    })
    const findModeOption = this.data.modeOption.find(ele => ele.value === options.mode)
    this.setData({
      supInfo: wx.getStorageSync("supInfo"),
      curCust: wx.getStorageSync("curCust"),
      inputValue: '',
      mode: options.mode,
      modeTitle: findModeOption.text,
      headId: options.id,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const that = this
    const mbbcRect = wx.getMenuButtonBoundingClientRect()
    if (mbbcRect)
      that.setData({
        mbbcRectTop: mbbcRect.top + 'px'
      })
    wx.getSystemInfo({
      success: (res) => {
        const query = wx.createSelectorQuery()
        query.select('#scorllview').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function (exceRes) {
          let scrollH2 = res.windowHeight - exceRes[0].top - 49
          that.setData({
            scrollH: scrollH2 + 'px',
            inputFocus: true
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    eventChannel.emit('acceptDataFromScanPDAPage', {
      refresh: true,
      dataList: this.data.dataList
    });
  },
  //数据处理中心
  getOderDetail(barCode) {
    App.getHttp()._postScanPDA('/api/psi/barCodeFlow/scanShimgeGroupBarCode', { barCode: barCode, businessType: 6 }).then((goodsInfo) => {
      const httpResult = goodsInfo.map(info => {
        return {
          itemCode: info.itemCode,
          itemId: info.itemId,
          itemName: info.itemName,
          itemUrl: info.itemUrl,
          specs: info.specs,
          price: 0,
          applyQty: info.barCodeFlowResponseDTOList.length,
          qtyAvi: info.qtyAvi,
          barCodeFlowResponseDTOList: info.barCodeFlowResponseDTOList
        }
      })
      if (this.data.mode === 'add') {
        if (this.data.dataList.length === 0) {
          this.setData({
            dataList: httpResult
          })
        } else {
          for (let index = 0; index < httpResult.length; index++) {
            const element = httpResult[index]
            const findIndex = this.data.dataList.findIndex(find => find.itemId === element.itemId)
            // 存在一样的商品编码
            if (findIndex > -1) {
              const findItem = this.data.dataList[findIndex]
              if (findItem.barCodeFlowResponseDTOList && findItem.barCodeFlowResponseDTOList.length > 0) {
                let exitDiff = false
                //过滤原来条码与新扫码相同的扫码
                element.barCodeFlowResponseDTOList.forEach(barForEach => {
                  if (findItem.barCodeFlowResponseDTOList.findIndex(barCodeFind => barCodeFind.barCode === barForEach.barCode) === -1) {
                    exitDiff = true
                    this.data.dataList[findIndex].applyQty = this.data.dataList[findIndex].applyQty + 1
                    this.data.dataList[findIndex].barCodeFlowResponseDTOList.push(barForEach)
                    wx.showToast({
                      title: '扫码，成功！',
                      icon: "success",
                    })
                  }
                })
                if (!exitDiff) {
                  wx.showToast({
                    title: '条码重复!',
                    icon: 'none'
                  })
                }
              } else {
                this.data.dataList[findIndex].applyQty = this.data.dataList[findIndex].applyQty + 1
                this.data.dataList[findIndex].barCodeFlowResponseDTOList = element.barCodeFlowResponseDTOList
              }
              this.setData({
                dataList: this.data.dataList
              })
            } else {
              this.data.dataList.push(element)
              this.setData({
                dataList: this.data.dataList
              })
            }
          }
        }
      } else {
        for (let index = 0; index < httpResult.length; index++) {
          const element = httpResult[index]
          const findIndex = this.data.dataList.findIndex(find => find.itemId === element.itemId)
          if (findIndex > -1) {
            const findGood = this.data.dataList[findIndex]
            if (findGood.barCodeFlowResponseDTOList && findGood.barCodeFlowResponseDTOList.length > 0) {
              element.barCodeFlowResponseDTOList.forEach(eleForItem => {
                const findBarCodeIndex = findGood.barCodeFlowResponseDTOList.findIndex(findItem => findItem.barCode === eleForItem.barCode)
                if (findBarCodeIndex > -1) {
                  this.data.dataList[findIndex].applyQty = this.data.dataList[findIndex].applyQty - 1
                  this.data.dataList[findIndex].barCodeFlowResponseDTOList.splice(findBarCodeIndex, 1)
                  wx.showToast({
                    title: '扫码，成功！',
                    icon: "success",
                  })
                }else{
                  wx.showToast({
                    title: '条码不存在退货商品中!',
                    icon: 'none'
                  })
                }
              })
            }else{
              wx.showToast({
                title: '条码不存在退货商品中!',
                icon: 'none'
              })
            }
            this.setData({
              dataList: this.data.dataList
            })
          } else {
            wx.showToast({
              title: '条码不存在退货商品中!',
              icon: 'none'
            })
          }
        }
      }
      this.setData({
        inputFocus: true,
        inputValue: ""
      })
    }).catch((errData) => {
      const errMsg = errData.chnDesc || '扫码，失败!'
      if (errMsg.length > 14) {
        wx.showModal({
          title: '异常提示',
          content: errMsg,
          showCancel: false,
          complete: () => {
            this.setData({
              inputFocus: true,
              inputValue: ''
            })
          }
        })
      } else {
        wx.showToast({
          title: errMsg,
          icon: 'none',
          duration: 2000
        })
        setTimeout(() => {
          this.setData({
            inputFocus: true,
            inputValue: ''
          })
        }, 2000);
      }

    })
  },
  onClickConfirm() {
    this.onInputConfirm({
      detail: {
        value: this.data.barCode
      }
    })
  },
  onInputChange(event) {
    const { value, keyCode } = event.detail
    if (value.indexOf('\n')>-1) {
      this.data.barCode = value.split('\n')[0]
    } else {
      this.data.barCode = value
    }
    // 删除键不触发
    if (keyCode === 8) return
    if (keyCode === 10 || value.length > 8) {
      this.onInputBlur()
      this.onClickConfirm()
    }
  },
  onInputConfirm(e) {
    if (!e.detail.value) {
      wx.showToast({
        title: '未检测到条码！',
        icon: "error",
      })
      return
    }
    let param = {
      headId: this.data.headId,
      billNo: this.data.billNo,
      barCode: e.detail.value
    }
    this.getOderDetail(e.detail.value)

  },
  onClickOperateDel(e){
    this.data.dataList.splice(e.currentTarget.dataset.index , 1)
    this.setData({
      dataList: this.data.dataList
    })
  },
  showDetail: function (e) {
    wx.showModal({
      title: '商品信息',
      content: e.currentTarget.dataset.itemName + "/" + e.currentTarget.dataset.specs,
      showCancel: false,
      complete: () => {
        this.setData({
          inputFocus: true,
          inputValue: ''
        })
      }
    })
  },
  onSwitchMode() {
    wx.showActionSheet({
      alertText: '切换扫码枪模式',
      itemList: this.data.modeOption.map(ele => { return ele.text }),
      itemColor: '#0C92E0',
      success: (res) => {
        const { tapIndex } = res
        this.data.mode = this.data.modeOption[tapIndex].value
        this.setData({
          modeTitle: this.data.modeOption[tapIndex].text,
          inputFocus: true,
          inputValue: ""
        })
      }
    })
  },
  onClickInputFocus() {
    this.setData({
      inputFocus: true,
    })
  },
  onInputBlur() {
    this.setData({
      inputFocus: false,
    })
  },
  goBack() {
    wx.navigateBack()
  }
})