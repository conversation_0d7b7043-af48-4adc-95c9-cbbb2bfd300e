<!--pages_sub/pages/ccs/scanPDA/retail/index.wxml-->
<view class="page" bindtap="onClickInputFocus">
 	<image class="top-img" src="/asset/imgs/scan-bg.png"></image>
 	<view class="header-layout flex align-items-center" style="top:{{mbbcRectTop}}">
 		<image class="header-back" src="/asset/svgs/backImg.svg" catchtap="goBack"></image>
 		<view class="header-content flexbox flex align-items-center" catchtap="onSwitchMode" >
      <view>{{modeTitle}}</view>
     <image class="switch_icon" src="/asset/imgs/drop_down_icon.png" mode="aspectFit" />
    </view>
 	</view>
 	<view class="content-layout">
    <view class="search-inner-layout flex align-items-center" style="background-color: {{inputFocus?'#0c92e021':'#FFC9C4'}}">
 			<image class="search-inner-img" src="/asset/imgs/scan.png" bindtap="onClickConfirm"></image>
 			<input value="{{inputValue}}" focus="{{inputFocus}}" class="flexbox search-inner-input" placeholder-class="search-inner-input-holder" placeholder="{{inputFocus?'请扫描商品条码':'请点击此处,开始'}}" bindinput='onInputChange' bindconfirm="onInputConfirm" bindtap="onClickInputFocus" bindblur="onInputBlur"  confirm-hold='true'></input>
 		</view>
 		<view class="table-layout">
 			<view class="flex font-sub">
 				<view class="table-index">序号</view>
 				<view class="flexbox m-h-48">商品名称</view>
         <view class="table-count">审核数</view>
 				<view class="table-count">已扫码数</view>
 			</view>
 			<scroll-view id="scorllview" scroll-y  style='height: {{scrollH}}' enable-flex>
 				<block wx:if="{{dataList.length>0}}">
 					<view class="row-layout flex font-sub" wx:for="{{dataList}}" wx:key="itemId">
 						<view class="table-index">{{index+1}}</view>
 						<view class="flexbox m-h-48 single-line-ellipsis" bindtap="showDetail" data-specs="{{item.specs}}" data-item-name="{{item.itemName}}">{{item.itemName}}</view>
             <view class="table-count">{{item.confirmReqQty}}</view>
 						<view class="table-count">{{item.scanCount}}</view>
 					</view>
 				</block>
 				<noneView wx:else></noneView>
 			</scroll-view>
 		</view>
 	</view>
 	<view class="bottom-layout">
 		<view class="bt-layout" catchtap='goBack'>返回</view>
 	</view>
 </view>
 <modalView show="{{!modalHidden}}" bindcancel="Modalcancel" bindconfirm="Modalconfirm">
 	<view class='modal-detail flex column justify-content-center'>
 		<view class="modal-title">温馨提示</view>
 		<view class='modal-mid m-t-32'>
 			<text>扫码进度： </text>
 			<text class='modal-s'>{{precent}}</text>
 			(<text class='modal-s'>{{order.totalBcsQty}}</text>/{{order.totalBillQty}})
 		</view>
 		<view class='modal-b m-t-16'>
 			请确认是否扫码上传？(只能上传一次)
 		</view>
 	</view>
 </modalView>
