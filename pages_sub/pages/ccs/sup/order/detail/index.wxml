<!-- pages/ccs/orderDetail/index.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="page-full root-layout">
  <!-- <view class="addr-content">
    <van-field value="{{ wxsUtil.formatShipMode(detailInfo.shipMode, shipModeDict) }}" label="运送方式" readonly input-align="right" />
    <van-field value="{{ detailInfo.addr.contactAddr }}" readonly left-icon="/asset/imgs/purchase/location.png" autosize border="{{false}}" type="textarea" input-align="left" />
    <van-cell value-class="addr-phone" border="{{false}}" value="{{(detailInfo.addr.contactPerson || '') + '  ' + (detailInfo.addr.telPhone || '')}}"></van-cell>
  </view> -->
  <!-- 订单号，供应商等数据 -->
  <view class="order-title flex">
    <view class="title-order-no">
        <view>{{detailInfo.orderNo}}</view>
        <br/>
        <view>{{detailInfo.saleOrgName}}</view>
    </view>
    <view wx:if="{{detailInfo.state === 99}}" class="title-status">
        已取消
    </view>
    <view wx:else class="title-status">
        {{detailInfo.deliveryStatName || '已发货'}}
    </view>
  </view>
  <view class="order-cust">
    <view class="cust-name">
        {{detailInfo.invoiceCustName}}
    </view>
  </view>
  
  <!-- 收货地址 -->
  <view class="addr-layout ">
    <view class="flex align-items-center">
      <view class="flexbox">
        <view class="flex justify-content-end">
          <image class="address-icon" src="/asset/imgs/purchase/location.png"></image>
          <view class="flexbox text-left ">
            <view>{{detailInfo.addr.contactAddr}}</view>
            <view class="tel">{{detailInfo.addr.contactPerson}} {{detailInfo.addr.telPhone}}</view>
          </view>
        </view>
      </view>
    </view>
    <van-cell-group border="{{false}}">
      <van-field label="备注" value="{{ detailInfo.remark }}" readonly autosize border="{{false}}" type="textarea" input-align="right" />
    </van-cell-group>
  </view>
  <view>
    <goodsCardNew showState="{{false}}" showAuxiliary="{{true}}" headTitle="订单号" showQty="{{false}}" goodData="{{detailInfo}}"  dataType="{{2}}"/>
    
  </view>
  <van-cell-group class="cell-group-layout block-layout" border="{{false}}">
    <van-field value="{{detailInfo.totalQty}}" label="订单数量" readonly input-align="right" />
    <van-field value="{{detailInfo.totalOriginalAmount}}" label="订单金额" readonly input-align="right" />
    <van-field value="{{detailInfo.disTotalAmount}}" label="优惠金额" readonly input-align="right" />
    <van-field value="{{detailInfo.totalPayAmount}}" label="实付金额" readonly input-align="right" />
  </van-cell-group>
  <van-cell-group class="cell-group-layout block-layout" border="{{false}}">
    <van-field wx:if="{{detailInfo.stat != 2}}" value="{{detailInfo.statName}}" label="取消申请状态" readonly input-align="right" />
    <van-field wx:if="{{detailInfo.stat != 2}}" value="{{detailInfo.message}}" label="申请结果信息" readonly input-align="right" />
    <!-- <van-field label="备注" value="{{ detailInfo.remark }}" readonly autosize border="{{false}}" type="textarea" input-align="right" /> -->
  </van-cell-group>
  <view wx:if="{{detailInfo.deliveryStat != 3 && detailInfo.deliveryStat != 2 && detailInfo.state != 4 && detailInfo.state != 99 && detailInfo.stat != 0 && detailInfo.stat != 3 && detailInfo.stat != 1}}" class="footer-slot">
    <view class="footer-btn" data-item="{{item}}" catchtap="onClickSubmitOrder">
      申请取消
    </view>
  </view>
</view>