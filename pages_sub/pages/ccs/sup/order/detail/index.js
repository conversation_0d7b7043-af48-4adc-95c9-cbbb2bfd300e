// pages_sub/pages/ccs/sup/order/detail/index.js
const App=getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    stat:'',
    id:'',
    detailInfo: {},
    shipModeDict: wx.getStorageSync('dictMap').shipMode,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.id){
      this.data.stat = options.stat
      this.data.id = options.id
      this.getOrderDetail()
    }
  },
  getOrderDetail(){
    App.getHttp()._get('/api/vcs/mobile-web/myx/supOrder/getOrderDetailById/' + this.data.id).then(res=>{
      res.addLineList = res.lines
      res.disTotalAmount = (res.totalOriginalAmount - res.totalPayAmount).toFixed(2)
      res.totalPayAmount = res.totalPayAmount.toFixed(2)
      this.setData({
        detailInfo:res
      })
    })
  },
  // 点击提交订单事件
  onClickSubmitOrder(e) {
    wx.showModal({
      title: '确定是否申请取消订单？',
      content: '',
      success: (resM) => {
        if (resM.confirm) {
          this.executeSubmit()
        }
      }
    })
  },
  // 提交订单方法
  executeSubmit(item) {
    const url = '/api/vcs/mobile-web/myx/supOrder/closeByIds'
    const params = {
      ids: [this.data.detailInfo.id]
    }
    App.getHttp()._post(url,params).then(res => {
      wx.showToast({
        title: '订单取消成功',
        icon: 'success'
      })
      this.setData({
        pageIndex: 1,
        dataList: []
      })
      setTimeout(() => {
        this.getOrderDetail()
      }, 1000);
    })
  }
})