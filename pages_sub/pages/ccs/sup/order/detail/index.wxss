/* pages_sub/pages/ccs/sup/order/detail/index.wxss */
.root-layout {
  background-color: rgb(245, 245, 245);
  padding-bottom: 40rpx;
}
.order-title {
  padding: 40rpx;
  padding-left: 60rpx;
  background-color: #4ca0ff;
  color: #fff;
  font-size: 32rpx;
  justify-content: space-between; /* 两端对齐 */
  /* align-items: center; */
}
.order-cust {
  padding: 20rpx 40rpx;
  padding-bottom: 0;
  background-color: #fff;
  color: #808080;
  font-size: 26rpx;
}
.addr-layout {
  padding: 24rpx 32rpx 24rpx 0;
  position: relative;
  background-color: #fff;
  font-size: 28rpx;
  color: #000;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 600;
  padding-left: 20rpx;
}
.addr-layout .address-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 4rpx;
}
.addr-layout .tel {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  text-align: left;
  line-height: 44rpx;
  margin-top: 8rpx;
}
.placeholder-layout {
  height: 116rpx;
}
.stat-layout {
  padding: 0 24rpx;
  height: 168rpx;
  box-sizing: border-box;
  background-image: linear-gradient(-55deg, #00b9c3 0%, #D0005A 100%);
}

.stat-layout .stat {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 48rpx;
  font-weight: 500;
}

.stat-layout .no {
  margin-top: 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
  font-weight: 400;
}

.stat-layout .stationc {
  width: 64rpx;
  height: 64rpx;
}
.item-layout{
  margin-top: 16rpx;
  background-color: #fff;
  padding: 24rpx;
  border-radius: 8rpx;
}
.top-layout {
  padding: 24rpx 0;
  background-color: #FFFFFF;
}

.take-goods-detail {
  padding: 0 24rpx 20rpx;
  font-size: 24rpx;
  line-height: 30rpx;
  background: #ddd;
  padding-top: 20rpx;
  margin-bottom: 20rpx;
}
.take-goods-detail .contacts-detail {
  padding: 20rpx 0 0;
  font-size: 20rpx;
  color: #999;
}

.serial-box {
  height: 44rpx;
  padding: 0 24rpx 0 16rpx;
  border-left: 8rpx solid #00b9c3;
}

.serial-box .serial {
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 500;
}

/* .good-layout {
  padding: 24rpx;
  background-color: #FFFFFF;
}

.good-layout .itemname {
  margin: 0 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}

.good-layout .img {
  width: 160rpx;
  height: 160rpx;
}
.delivery-layout {
  padding: 16rpx 24rpx;
  background-color: #FFFFFF;
}
.delivery-layout .inerbox{
  padding: 0 24rpx;
  background: rgba(208,0,13,0.05);
  border-radius: 8rpx;
  height: 80rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.75);
  line-height: 80rpx;
  font-weight: 400;
}
.good-layout .count-box {
  width: 160rpx;
}

.good-layout .count-box .itemprice {
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: right;
  line-height: 44rpx;
  font-weight: 500;
}

.good-layout .count-box .itemqty {
  margin-top: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.75);
  text-align: right;
  line-height: 40rpx;
  font-weight: 400;
}
.gift-layout{
  background-color: #FFFFFF;
}
.gift-layout .bottom-layout{
  padding:  24rpx 24rpx  20rpx 60rpx;
}
.gift-layout .bottom-layout .type{
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #00b9c3;
  border-radius: 4rpx;
  text-align: center;
  line-height: 36rpx;
  color: #00b9c3;
  font-family: PingFangSC-Regular;
  font-size: 22rpx;
  font-weight: 400;
  margin-right: 16rpx;
}
.gift-layout .bottom-layout .itemname{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 400;
  margin-right: 16rpx;
}
.gift-layout .bottom-layout .count{
  font-family: SanFranciscoText-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.gift-layout .up-layout{
  height: 80rpx;
  padding: 0 24rpx;
}
.gift-layout .up-layout .icon{
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
} */

.total-layout {
  padding: 26rpx 24rpx;
  background-color: #FFFFFF;
  text-align: right;
}
.total-layout .totaltxt {
  margin-left: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}
.total-layout .totalprice {
  font-family: SanFranciscoText-Medium;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: right;
  line-height: 48rpx;
  font-weight: 500;
}
.cell-group-layout .cell-value-class{
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 500;
}
.cell-group-layout .cell-price-class{
  font-family: SanFranciscoText-Medium;
font-size: 28rpx;
color: #F97D4E;
line-height: 44rpx;
font-weight: 500
}
.txt {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
  font-weight: 400;
}
.placeholder-layout {
  height: 116rpx;
}
.foot-layout {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  padding: 0 24rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}

.foot-layout .total {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}

.foot-layout .total .uom {
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: #F97D4E;
  line-height: 44rpx;
  font-weight: 500;
}

.foot-layout .cancel {
  width: 136rpx;
  height: 56rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.15);
  border-radius: 8rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 56rpx;
  font-weight: 400;
}
.block-layout {
  margin: 24rpx;
}
.item-layout {
  justify-content: space-around;
}
.state-item {
  flex-direction: column;
  align-items: center;
}
.state-qty {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  font-size: 44rpx;
  line-height: 60rpx;
}
.state-text {
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #9E9E9E;
  font-size: 28rpx;
  line-height: 44rpx;
}
.activeState {
  color: #00b9c3;
}
.copy {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #707070;
  line-height: 44rpx;
}


.footer-slot {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}
.footer-btn {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
  width: 70%;
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  padding: 14rpx 32rpx;
  margin: auto;
  text-align: center;
}