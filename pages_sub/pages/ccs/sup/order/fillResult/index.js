// pages_sub/pages/ccs/sup/order/fillResult/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    totalMoney:0,
    totalPayAmount:0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.totalMoney){
      this.setData({
        totalMoney:options.totalMoney,
        totalPayAmount:options.totalPayAmount
      })
    }
  },
  onClickCheck(){
   wx.redirectTo({
     url: '/pages_sub/pages/ccs/sup/order/list/index',
   })
  },
  onClickBack(){
    wx.switchTab({
      url: '/pages/ccs/purchase/index',
    })
  }
 
})