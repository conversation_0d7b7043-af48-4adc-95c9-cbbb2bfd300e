// pages_sub/pages/ccs/sup/order/fill/index.js
const App = getApp()
const dayjs = require('dayjs')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showDatePicker: false,
    submitLoading: false,
    shipModeOptions: [],
    shipMode: '1',
    shipModeName: '汽运',
    planDeliveryDate: '',
    policyTotalAmount:0,
   
    fillsInfo: {},
    addrInfo: {},
    policyLines: [],
    costProporPercent: '',
    costAmount: '',
    creditAmount: '',
    note: '',
    showCostAction:false,
    costActions:[{name: '是',value:2}, {name: '否',value:1}],
    isUsedCost:1,
    isUsedCostName:'否',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getDefaultAddress()
    this.setData({
      showPicker: true,
      shipModeOptions: App.getDict('shipMode', []),
      fillsInfo: wx.getStorageSync('fillsList')[0] //目前只能一个供应商 产品组 渠道下单,不能合并下单 ,先这样写了
    })
    this.getCustQuota()
    // 政策订单查优化额度
    if (this.data.fillsInfo.orderType != 0&&this.data.fillsInfo.orderType != 5) {this.checPolicyRule()}
  },

  onShow: function () {
    const selectAddr = wx.getStorageSync('address')
    if (selectAddr) {
      this.setData({
        addrInfo: selectAddr
      })
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    wx.removeStorageSync('fillsList')
    wx.removeStorageSync('address')
  },

  onClickAddress() {
    wx.navigateTo({
      url: '/pages/ccs/moreFeatures/addr/index?action=order',
    })
  },
  onClickDate() {
    this.setData({ showDatePicker: true })
  },
  onDatePickerClose() {
    this.setData({ showDatePicker: false })
  },
  onDatePickerConfirm(event) {
    this.setData({
      planDeliveryDate: event.detail ? dayjs(event.detail).format('YYYY-MM-DD') : ''
    })
    this.onDatePickerClose()
  },
  onShipRadioChange(e) {
    this.setData({
      shipMode: e.detail,
      shipModeName: this.data.shipModeOptions.filter(res => res.value == e.detail)[0].name
    })
  },
  getCustQuota: function () {
    // 只有普通订单才可用费用
    if (this.data.fillsInfo.orderType == 0) {
      App.getHttp()._post('/api/vcs/baseProportion/queryByParam', {
        channelCode: this.data.fillsInfo.channelCode,
        saleOrgCode: this.data.fillsInfo.saleOrgCode,
        custId: this.data.fillsInfo.invoiceCustId,
        state: 2,
        orderType: this.data.fillsInfo.orderType,
        ssetsOfBooksId: this.data.fillsInfo.vendorSetsOfBooksId,
      }).then(res => {
        if (res && res.costProporPercent) {
          this.setData({
            costProporPercent: res.costProporPercent
          })
        }
      })
      App.getHttp()._post('/api/vcs/costPool/page', {
        pageIndex: 1,
        pageSize: 1,
        param: {
          saleChannel: this.data.fillsInfo.channelCode,
          saleOrgCode: this.data.fillsInfo.saleOrgCode,
          agencyId: this.data.fillsInfo.invoiceCustId,
          agencyCode: this.data.fillsInfo.invoiceCustCode,
          isDel: 1,
          setsOfBooksId: this.data.fillsInfo.vendorSetsOfBooksId,
        }
      }).then(recordList => {
        if (recordList && recordList.length > 0) {
          this.setData({
            costAmount: recordList[0].availableAmount
          })
        }
      })
    }
    App.getHttp()._post('/api/mmd/common/bsSup/page', { pageIndex: 1, pageSize: 1, param: { id: this.data.fillsInfo.supId } }).then(recordList => {
      if (recordList && recordList.length > 0) {
        this.data.fillsInfo.creditGrade = recordList[0].creditGrade
        this.data.fillsInfo.companyCode = recordList[0].companyCode
        App.getHttp()._post('/api/vcs/creditPool/queryCustPoolSum', {
          channelCode: this.data.fillsInfo.channelCode,
          saleOrgCode: this.data.fillsInfo.saleOrgCode,
          custId: this.data.fillsInfo.invoiceCustId,
          custCode: this.data.fillsInfo.invoiceCustCode,
          ssetsOfBooksId: this.data.fillsInfo.vendorSetsOfBooksId,
          creditGrade: this.data.fillsInfo.creditGrade,//不知道哪来
          companyCode: this.data.fillsInfo.companyCode,//不知道哪来
        }).then(result => {
          if (result && result.availableAmount) {
            this.setData({
              creditAmount: result.availableAmount
            })
          }
        })
      }
    })
  },
  getDefaultAddress: function () {
    App.getHttp()._post('/api/mmd/baseCustAddr/myx/page', {
      pageIndex: 1,
      pageSize: 1,
      param: { isDefault: 2, isUsable: 2 ,sourceId: wx.getStorageSync('custInfo').custId}
    }).then(res => {
      if (res &&res && res.length > 0) {
        this.setData({
          addrInfo: res[0]
        })
      }
    })
  },
  checPolicyRule() {
    const policyRuleReqDTOs = this.data.fillsInfo.policyCartList.map((mapCart) => {
      const policyRuleItemReqDTOList = [];
      mapCart.shoppingCartList.forEach((item) => {
        // 组合要继续到明细商品
        if (item.groupItems && item.groupItems.length > 0) {
          item.groupItems.forEach((groupItem) => {
            policyRuleItemReqDTOList.push({
              configItemId: item.configItemId,
              groupId: groupItem.itemGroupHeadId,
              groupLineId: groupItem.id,
              itemId: groupItem.itemId,
              itemName: groupItem.itemName,
              originalPrice: groupItem.promotionPrice,
              purchaseQty: item.itemCombQty,//套数
              shoppingCartId:item.id//购物车id,提交订单删除需要
            });
          });
        } else {
          policyRuleItemReqDTOList.push({
            configItemId: item.configItemId,
            itemId: item.itemId,
            itemName: item.itemName,
            originalPrice: (this.data.fillsInfo.orderType==3)?item.standardPrice:item.applyPrice,//一口价给原价,其他申请价
            purchaseQty: item.purchaseQty,
            shoppingCartId:item.id//购物车id,提交订单删除需要
          });
        }
      });
      return {
        configHeadId: mapCart.policyId,
        orderType: this.data.fillsInfo.orderType,
        policyRuleItemReqDTOList,
        policyGiftItemReqDTOList:[],
        vendorSetsOfBooksId: this.data.fillsInfo.vendorSetsOfBooksId,
        vendorId: this.data.fillsInfo.vendorId,
        invoiceSetsOfBooksId: this.data.fillsInfo.invoiceSetsOfBooksId,
        invoiceCustId: this.data.fillsInfo.invoiceCustId,
      };
    });
    this.data.policyLines = policyRuleReqDTOs;
    // 查政策优惠
    App.getHttp()._post('/api/vcs/policy/checkMultiplePolicyRule',policyRuleReqDTOs).then(res=>{
      let policyTotalAmount =0
      let totalPayAmount = 0
      Object.keys(res).forEach(key=>{
        policyTotalAmount = policyTotalAmount+res[key].policyTotalAmount
        totalPayAmount = totalPayAmount+res[key].totalPayAmount
      })
      this.data.fillsInfo.totalPayAmount = totalPayAmount
      // 赠品政策,插入赠品结果
      if(this.data.fillsInfo.orderType==4){
        this.insertGiftItems(res)
        this.setData({
          policyTotalAmount:policyTotalAmount,
        })
      }else{
        this.setData({
          policyTotalAmount:policyTotalAmount,
          fillsInfo:this.data.fillsInfo
        })
      }
    });
  },
  insertGiftItems(insertGiftObj){
    this.data.fillsInfo.policyCartList.forEach((ele,index)=>{
      if(insertGiftObj[ele.policyId]){
        this.data.fillsInfo.policyCartList[index].giftType=insertGiftObj[ele.policyId].giftType
        this.data.fillsInfo.policyCartList[index].giftItems = insertGiftObj[ele.policyId].policyGiftItemResponseDTOList.map((gift,giftIndex)=>{
          gift.checked = insertGiftObj[ele.policyId].giftType==1// 固定赠1, 任意赠2
          if (giftIndex === 0 && !gift.checked) gift.checked = true;//默认选中一个
          return gift
        })
      }
    })
    this.setData({
      fillsInfo:this.data.fillsInfo
    })
  },
  onClickChecked(e){
    const {policyIndex,itemIndex} = e.currentTarget.dataset
    // // 固定赠1, 任意赠2.  任意赠才可自由选择一个组号
    if(this.data.fillsInfo.policyCartList[policyIndex].giftType==2){
      const tempGiftItem = this.data.fillsInfo.policyCartList[policyIndex].giftItems[itemIndex]
      this.data.fillsInfo.policyCartList[policyIndex].giftItems[itemIndex].checked = !tempGiftItem.checked
      const findGroupCheckedIndex = this.data.fillsInfo.policyCartList[policyIndex].giftItems.findIndex(
        (ele) => ele.checked && ele.giftGroupId === tempGiftItem.giftGroupId,
      );
      // 任意增,同组必须选一个
      if (findGroupCheckedIndex === -1) {
        this.data.fillsInfo.policyCartList[policyIndex].giftItems[itemIndex].checked = true
      } else {
        // 同组其他设为false
        this.data.fillsInfo.policyCartList[policyIndex].giftItems.forEach((each,eachIndex) => {
          if (each.giftGroupId === tempGiftItem.giftGroupId && each.giftItemId !== tempGiftItem.giftItemId) {
            this.data.fillsInfo.policyCartList[policyIndex].giftItems[eachIndex].checked = false
          }
        });
      }
      this.setData({
        fillsInfo: this.data.fillsInfo
      })
    }
  },
  onBindinput(e) {
    this.data.note = e.detail.value
  },
  doSubmit(isConfirm) {
    const { addrInfo, fillsInfo } = this.data
    if (!this.data.shipMode) {
      wx.showToast({
        title: '请选择配送方式!',
        icon: 'error'
      })
      return
    }
    if (!this.data.planDeliveryDate) {
      wx.showToast({
        title: '请选择期望交货时间!',
        icon: 'error'
      })
      return
    }
    if (this.data.shipMode == 1 &&  !this.data.addrInfo.provinceId) {
      wx.showToast({
        title: '汽运，必须选地址!',
        icon: 'error'
      })
      return
    }
    let lines = []
    let breakInfo = null
    let breakMsg = null
    for (let index = 0; index < fillsInfo.policyCartList.length; index++) {
      const cartType = fillsInfo.policyCartList[index] 
       // 处理政策赠品
       if (cartType.giftItems && cartType.giftItems.length > 0) {
        // 自然遍历是保证个集合准确的保障
        this.data.policyLines[index].policyGiftItemReqDTOList = cartType.giftItems.filter(
          (giftItem) => giftItem.checked,
        );
      }
      // 处理普通商品
      const cartsList = cartType.shoppingCartList;
      const tempLine ={
        policyId: cartType.policyId,
        policyCode: cartType.policyCode,
        orderType: fillsInfo.orderType,
        orderLineList: [],
      }
      for (let iindex = 0; iindex < cartsList.length; iindex++) {
        const cartItem = cartsList[iindex];
        if (cartItem.groupItems && cartItem.groupItems.length>0) {
          if (!cartItem.groupAmount) {
            breakInfo = cartItem
            breakMsg = "组合商品的组合价格未设置"
            break
          }
          if (!!cartItem.leastQty&&Number(cartItem.leastQty) >Number(cartItem.itemCombQty)) {
            //未达到最低起订量
            breakInfo = cartItem
            breakMsg = cartItem.itemCombName+"未达到最低起订量!"
            break
          }
          if (!!cartItem.singleCustLimit&&Number(cartItem.singleCustLimit)>0&&Number(cartItem.singleCustLimit) < Number(cartItem.itemCombQty)) {
            //超过单客户限定量
            breakInfo = cartItem
            breakMsg = cartItem.itemCombName+",超过单客户限定量!"
            break
          } 
          for (let iiindex = 0; iiindex < cartItem.groupItems.length; iiindex++) {
            const groupItem = cartItem.groupItems[iiindex];
            tempLine.orderLineList.push({
                vendorSetsOfBooksId: fillsInfo.vendorSetsOfBooksId,
                invoiceSetsOfBooksId: fillsInfo.invoiceSetsOfBooksId,
                orderType: fillsInfo.orderType,
                policyCode: cartType.policyCode,
                policyName: cartType.policyName,
                policyId: cartType.policyId,
                configItemId: cartItem.configItemId,
                groupLineId: groupItem.id,
                groupId: groupItem.itemGroupHeadId,
                applyPrice: cartItem.groupAmount,
                applyQty: cartItem.itemCombQty,
                shoppingCartId: cartItem.id,//提交订单,删除购物车有用
                itemCombName: cartItem.itemCombName,
                itemCombCode: cartItem.itemCombCode,
                itemCombId: cartItem.itemCombId,
                itemCode: groupItem.itemCode,
                itemName: groupItem.itemName,
                itemId: groupItem.itemId,
                uomId: groupItem.uomId,
                proportioningQty: groupItem.proportioningQty,
            })
          }
        } else {
          if (!cartItem.applyPrice && !cartItem.promotionPrice) {
            //存在成品,价格为0的不让条订单
            breakInfo = cartItem
            breakMsg = cartItem.itemName+"价格为0，请联系上游维护价格!"
            break
          } 
          if (!!cartItem.leastQty&&Number(cartItem.leastQty) >Number(cartItem.purchaseQty)) {
            //未达到最低起订量
            breakInfo = cartItem
            breakMsg = cartItem.itemName+"未达到最低起订量!"
            break
          }
          if (!!cartItem.singleCustLimit&&Number(cartItem.singleCustLimit)>0&&Number(cartItem.singleCustLimit) < Number(cartItem.purchaseQty)) {
            //超过单客户限定量
            breakInfo = cartItem
            breakMsg = cartItem.itemName+"超过单客户限定量!"
            break
          } 
          // 订单是applyQty
          cartItem.applyQty = cartItem.purchaseQty,
          cartItem.shoppingCartId=cartItem.id,//提交订单,删除购物车有用
          cartItem.id="",//后端干扰id置空
          tempLine.orderLineList.push(cartItem)
        }
      }
      lines.push(tempLine)
    }
    if (breakInfo) {
      wx.showToast({
        title: `${breakMsg}`,
        icon: 'none'
      })
      return
    }
    const params = {
      supId: fillsInfo.supId,
      vendorCode: fillsInfo.vendorCode,
      vendorId: fillsInfo.vendorId,
      vendorName: fillsInfo.vendorName,
      vendorSetsOfBooksId: fillsInfo.vendorSetsOfBooksId,
      invoiceCustCode: fillsInfo.invoiceCustCode,
      invoiceCustId: fillsInfo.invoiceCustId,
      invoiceCustName: fillsInfo.invoiceCustName,
      invoiceSetsOfBooksId: fillsInfo.invoiceSetsOfBooksId,
      channelCode: fillsInfo.channelCode,
      channelId: fillsInfo.channelId,
      channelName: fillsInfo.channelName,
      saleOrgCode: fillsInfo.saleOrgCode,
      saleOrgName: fillsInfo.saleOrgName,
      custGroupCode: fillsInfo.custGroupCode,
      saleRegionCode: fillsInfo.saleRegionCode,
      companyCode: fillsInfo.companyCode,
      creditGrade: fillsInfo.creditGrade,
      projectCode: fillsInfo.projectCode,
      projectName: fillsInfo.projectName,
      demandDate: this.data.planDeliveryDate,
      shipMode: this.data.shipMode,
      remark: this.data.note,
      orderType: fillsInfo.orderType,
      isUsedCost: this.data.isUsedCost,
      sourceSystem: 2, // 来源小程序app
      addr: {
        contactAddr:(addrInfo.provinceName || '') + (addrInfo.cityName || '') + (addrInfo.districtName || '') + (addrInfo.townName || '') + (addrInfo.defineAddr||''),
        contactPerson: addrInfo.name||'',
        telPhone: addrInfo.phone||'',
        provId: addrInfo.provinceId||'', // 后端字段不对齐, 前端遭殃到处要转换对齐
        provinceName: addrInfo.provinceName||'',
        cityId: addrInfo.cityId||'',
        cityName: addrInfo.cityName||'',
        districtId: addrInfo.districtId||'',
        districtName: addrInfo.districtName||'',
        townId: addrInfo.townId||'',
        townName: addrInfo.townName||'',
      },
      lines: lines,
      policyLines: this.data.policyLines,
    }
    if (isConfirm == 2) {
      this.executeSubmit(params)
    } else {
      wx.showModal({
        title: '确定要提交订单吗?',
        content: '',
        success: (resM) => {
          if (resM.confirm) {
            this.executeSubmit(params)
          }
        }
      })
    }
  },
  executeSubmit(params) {
    // api/psi/myx/suppurOrder/submit
    App.getHttp()._post('/api/vcs/policy/createMultipleOrder', params).then(res => {
      wx.redirectTo({
        url: '/pages_sub/pages/ccs/sup/order/fillResult/index?totalMoney=' + (this.data.fillsInfo.total)+'&totalPayAmount='+(this.data.fillsInfo.totalPayAmount||this.data.fillsInfo.total)
      })
    })
  },
  onClickCostAction(){
    this.setData({
      showCostAction:true
    })
  },
  onCloseCostAction(){
    this.setData({
      showCostAction:false
    })
  },
  onSelectCostAction(e){
    console.log('=onSelectCostAction=',e.detail);
    this.data.isUsedCost =  e.detail.value
    this.setData({
      isUsedCostName:e.detail.name
    })
  },
})