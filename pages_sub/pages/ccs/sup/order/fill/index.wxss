/* pages_sub/pages/ccs/sup/order/fill/index.wxss */
.root-layout {
  background: #F2F2F2
}

.root-layout .ship-addr-layout {
  padding-left: 32rpx;
  background-color: white;
  border-top: 1rpx solid #E6E6E6;
}

.root-layout .ship-addr-layout .radio-group-layout {
  padding: 24rpx 32rpx 24rpx 0;
  border-bottom: 1rpx solid #E6E6E6;
}

.root-layout .ship-addr-layout .radio-group-layout .radio-group-label {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
}

.root-layout .ship-addr-layout .radio-group-layout .van-radio-group {
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.root-layout .ship-addr-layout .radio-group-layout .van-radio-group .van-radio {
  margin-left: 32rpx;
}

.addr-layout {
  padding: 24rpx 32rpx 24rpx 0;
  position: relative;
}

.addr-layout .address-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 4rpx;
}

.addr-layout .tel {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  text-align: right;
  line-height: 44rpx;
  margin-top: 8rpx;
}

.addr-layout .more {
  width: 40rpx;
  height: 40rpx;
  margin-left: 16rpx;
}

.warining-layout {
  padding: 16rpx 32rpx;
  background: #FFFBE6;
}

.warining-layout .warining-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}
.warining-layout .warining-txt{
  color:#FAAE16 !important
}

.item-layout {
  margin-top: 24rpx;
  padding: 0 24rpx;
}
.adapter-layout{
  background-color: white;
  border-radius: 8rpx;
}
.item-layout   .card-layout .policy-info-layout .item-com-type {
  padding: 0 8rpx;
  height: 40rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  border-radius: 8rpx;
  line-height: 40rpx;
  margin-right: 8rpx;
}

.item-layout  .card-layout .policy-info-layout .hint {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 32rpx;
}

.item-layout  .card-layout .policy-info-layout .des-red {
  color: #FF4A4D;
}
.item-layout  .card-layout .policy-info-layout{
  margin-bottom: 32rpx;
}
.item-layout .card-layout{
  padding: 32rpx 24rpx;
  position: relative;
}

.item-layout .card-layout:after {
  position: absolute;
  bottom: 1rpx;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #E5E5E5;
 }
 .item-layout .card-layout:last-child:after{  
  height:0;
}
.item-layout .card-layout .good-layout {
  height: 176rpx;
  box-sizing: border-box;
  margin-bottom: 32rpx;
}

.item-layout .card-layout .good-layout .img-layout {
  position: relative;
  width: 176rpx;
  height: 176rpx;
  margin-right: 24rpx;
}
.item-layout .card-layout .good-layout .img-layout .itemurl {
  width: 176rpx;
  height: 176rpx;
}
.item-layout .card-layout .good-layout .img-layout .noqty {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  width: 128rpx;
  height: 128rpx;
}

.item-layout .card-layout .good-layout .itemname {
  height: 72rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #242424;
  line-height: 36rpx;
  font-weight: 400;
}
.item-layout .card-layout .good-layout .price-des-layout{
 margin-top: 32rpx;
 height: 72rpx;
}
.item-layout .card-layout .good-layout .price-des-layout .txt{
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 32rpx;
}
.item-layout .card-layout .good-layout .price-des-layout .count{
  margin-left: 2rpx;
}
.item-layout .card-layout .good-layout .des-box {
  height: 32rpx;
}
.item-layout .card-layout .good-layout .des-box .txt{
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 32rpx;
}
.item-layout .card-layout .good-layout .des-box .price{
  margin-left: 2px;
}
.item-layout .card-layout .good-layout .des-box .standprice{
  text-decoration: line-through;
}

.item-layout .card-layout .good-layout .itemprice {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #FF4A4D;
  font-weight: 600;
}

.item-layout .card-layout .good-layout .uom {
  font-family: PingFangSC-Medium;
  font-size: 20rpx;
  color: #F97D4E;
  font-weight: 500;
}
.item-layout .card-layout .gift-layout{
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 44rpx;
}
.item-layout .card-layout .gift-layout .num{
  width: 80rpx;
  margin-right: 16rpx;
}
.item-layout .card-layout .gift-layout .itemcode{
  color: #707070;
}
.item-layout .card-layout .gift-layout  .qty{
  margin-left: 16rpx;
}
.item-layout .card-layout  .limit-layout{
  margin-top: 24rpx;
  margin-left: 64rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 40rpx;
}
.item-layout .card-layout .group-layout{
  margin: 24rpx 0;
}
.item-layout .card-layout .group-layout  .hint {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 40rpx;
  margin-left: 64rpx;
  margin-right: 16rpx;
}

.item-layout .card-layout .group-layout  .itemprice {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #FF4A4D;
  font-weight: 600;
}

.item-layout .card-layout .group-layout  .uom {
  font-family: PingFangSC-Medium;
  font-size: 20rpx;
  color: #F97D4E;
  font-weight: 500;
}

.top-layout {
  padding: 24rpx ;
 
}



.placeholder-clazz {
  color: rgba(0, 0, 0, 0.15);
}
.cell-group-layout .cell-value-class {
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 44rpx;
  font-weight: 500;
}

.cell-group-layout .cell-value-top-class {
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 44rpx;
  font-weight: 500;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
}
.cell-group-layout .cell-value-bottom-class {
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 44rpx;
  font-weight: 500;
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}

.cell-group-layout .cell-price-class {
  font-family: PingFangSC-Medium, PingFang SC;
  font-size: 28rpx;
  color: #242424;
  line-height: 44rpx;
  font-weight: 500
}

.cell-group-layout .cell-hint-class {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.25);
  line-height: 44rpx;
  font-weight: 400
}

.cell-group-layout {
  padding: 0 24rpx;
}
/* components/footerButton/index.wxss */

.btn-layout {
  padding: 0 12rpx;
}
.btn-submit {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 8rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}
.btn-submit .uom {
  font-size: 18rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FF4A4D;
  line-height: 40rpx;
}
.btn-submit .price{
  font-size: 24rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FF4A4D;
  line-height: 40rpx;
  margin-right: 32rpx;
}
.custom-clazz{
  border-radius: 12rpx;
}
.box {
  height: 116rpx;
  opacity: 0;
}

.radio-img {
  width: 40rpx;
  height: 40rpx;
}