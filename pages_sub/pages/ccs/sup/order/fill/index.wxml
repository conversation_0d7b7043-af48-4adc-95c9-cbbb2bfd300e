<!--pages_sub/pages/ccs/sup/order/fill/index.wxml-->
<view class=" root-layout">
  <view class="ship-addr-layout">
    <!-- 发运方式 -->
    <view class="flex justify-content-between radio-group-layout">
      <view class="radio-group-label">发运方式</view>
      <van-radio-group value="{{ shipMode }}" bind:change="onShipRadioChange">
        <van-radio custom-class="radio-group-label" wx:for="{{shipModeOptions}}" wx:key="index" name="{{item.value}}">{{item.name}}</van-radio>
      </van-radio-group>
    </view>
    <!-- 收货地址 -->
    <view class="addr-layout " catchtap="onClickAddress">
      <view class="flex align-items-center">
        <view class="flexbox">
          <view class="flex justify-content-end">
            <image class="address-icon" src="/asset/imgs/purchase/location.png"></image>
            <view class="flexbox text-right font-s32-lh48 two-line-ellipsis">{{addrInfo.provinceName}}{{addrInfo.cityName}}{{addrInfo.districtName||''}}{{addrInfo.townName||''}}{{addrInfo.defineAddr}}</view>
          </view>
          <view class="tel">{{addrInfo.name}} {{addrInfo.phone}}</view>
        </view>
        <image class="more" src="/asset/svgs/left.svg"></image>
      </view>
    </view>
  </view>
  <!-- 黄色警告提示 -->
  <view class="warining-layout flex align-items-center">
    <image class="warining-icon" src="/asset/imgs/purchase/warning.png"></image>
    <text class="warining-txt font-s24-lh44">您选择的地址可能会造成商品库存变化，请提前确认再付款</text>
  </view>
  <!-- 商品明细 -->
  <view class="item-layout">
    <view class="adapter-layout">
      <view class="top-layout">
          <view class=" font-s32-lh48">{{fillsInfo.vendorName}}</view>
      </view>
      <view class="line"></view>
      <!-- 政策, 商品区域 -->
      <view class="card-layout" wx:for="{{fillsInfo.policyCartList}}" wx:for-item="cartTypeItem" wx:key="cartTypeIndex" wx:for-index="cartTypeIndex">
        <!-- 政策标题编码 -->
        <view class="policy-info-layout flex" wx:if="{{cartTypeItem.policyCode}}">
          <view class="flexbox">
            <view class="flex align-items-center">
              <view class="item-com-type" style="background-color:{{cartTypeItem.itemCombType==1?'#FAAE16':'#FF4A4D'}}">{{cartTypeItem.itemCombTypeName}}</view>
              <view class="flexbox font-s32-lh48 ">{{cartTypeItem.policyName}}</view>
            </view>
            <view class="hint m-t-16">政策描述 <span class="des-red">{{cartTypeItem.note}}</span></view>
          </view>
        </view>
        <block wx:for="{{cartTypeItem.shoppingCartList}}"  wx:for-item="cartItem" wx:key='cartId' wx:for-index="cartIndex">
          <!-- 组合商品 -->
          <block wx:if="{{cartTypeItem.itemCombType==2}}">
            <block wx:for="{{cartItem.groupItems}}" wx:for-item="groupItem" wx:key='groupItemIndex' wx:for-index="groupItemIndex">
              <!-- 商品区域 -->
              <view class="good-layout flex align-items-center">
                <view class="img-layout">
                  <image class="itemurl" src="{{cartItem.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                  <image class="noqty" wx:if="{{cartItem.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
                </view>
                <view class="flexbox">
                  <view class="itemname two-line-ellipsis">{{groupItem.itemName}}</view>
                  <view class="price-des-layout">
                    <view class="flex">
                     <view class="flexbox itemprice"><text class="uom">¥</text>{{groupItem.promotionPrice||groupItem.applyPrice}}</view>
                     <view class="txt flexbox text-right">配比数<text class="count">{{groupItem.proportioningQty}}</text></view>
                    </view>
                    <view class="des-box flex align-items-end justify-content-between">
                      <view class="txt flexbox" wx:if="{{groupItem.qtyOnhand}}">库存 {{groupItem.qtyOnhand}}</view>
                      <view class="txt flexbox text-right">数量<text class="price">{{groupItem.purchaseQty}}</text></view>
                    </view>
                  </view>
                </view>
              </view>
            </block>
            <!-- 组合商品的组合价 -->
            <view class="group-layout flex align-items-center justify-content-between">
              <view>
                <text class="hint">组合政策价</text>
                <text class="uom">¥</text>
                <text class="itemprice">{{cartItem.groupAmount}}</text>
              </view>
              <view class="hint">x <text class="itemprice">{{cartItem.itemCombQty}}</text></view>
            </view>
          </block>
          <!-- 单品 -->
          <block wx:else>
            <!-- 商品区域 -->
            <view class="good-layout flex align-items-center">
              <view class="img-layout">
                <image class="itemurl" src="{{cartItem.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                <image class="noqty" wx:if="{{cartItem.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
              </view>
              <view class="flexbox">
                <view class="itemname two-line-ellipsis">{{cartItem.itemName}}</view>
                <view class="price-des-layout">
                  <view class="itemprice"><text class="uom">¥</text>{{cartItem.promotionPrice||cartItem.applyPrice}}</view>
                  <view class="des-box flex align-items-end justify-content-between">
                    <view class="txt standprice flexbox " wx:if="{{cartItem.standardPrice}}">¥{{cartItem.standardPrice}}</view>
                    <view class="txt flexbox" wx:if="{{cartItem.qtyOnhand}}">库存 {{cartItem.qtyOnhand}}</view>
                    <view class="txt flexbox text-right">x<text class="price">{{cartItem.purchaseQty}}</text></view>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </block>
        <block wx:for="{{cartTypeItem.giftItems}}" wx:for-item="giftItem" wx:key='giftId' wx:for-index="giftIndex">
            <view class="flex align-items-center gift-layout" catchtap="onClickChecked" data-policy-index="{{cartTypeIndex}}" data-item-index="{{giftIndex}}" >
              <image wx:if="{{cartTypeItem.giftType&&cartTypeItem.giftType==2}}" class="radio-img" src="{{giftItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}"  />
              <view class="num">赠品{{giftItem.giftGroupId}}</view>
              <view class="flexbox single-line-ellipsis">{{giftItem.itemName}}<text class="itemcode"> ({{giftItem.itemCode}})</text></view>
              <view class="qty"><text class="itemcode">x</text> {{giftItem.purchaseQty}}</view>
            </view>
        </block>
      </view>
    </view>
  </view>

  <view style="padding: 0 24rpx;">
    <!-- 订单附加参数区域 -->
    <van-cell-group class="cell-group-layout m-t-24" border="{{false}}" >
      <van-cell title="商品总额" value="{{'￥'+fillsInfo.total}}" border="{{false}}" custom-class="cell-value-top-class" value-class="cell-price-class" />
      <van-cell title="优惠金额"  value="{{policyTotalAmount}}"  border="{{false}}" custom-class="cell-value-class" value-class="cell-price-class" />
      <van-cell wx:if="{{fillsInfo.orderType==0}}" title="费用比例" value="{{costProporPercent}}" border="{{false}}" custom-class="cell-value-class" value-class="cell-price-class" />
      <van-cell wx:if="{{fillsInfo.orderType==0}}" title="费用余额" value="{{costAmount}}" border="{{false}}" custom-class="cell-value-class" value-class="cell-price-class" />
      <van-cell title="信用额度" value="{{creditAmount}}" border="{{false}}" custom-class="cell-value-bottom-class" value-class="cell-price-class" />
    </van-cell-group>

    <!-- 合计金额 备注 -->
    <van-cell-group class="cell-group-layout m-t-24" border="{{false}}" >
      <van-cell title="期望交货时间" value="{{planDeliveryDate}}" is-link border="{{false}}" custom-class="cell-value-top-class" value-class="cell-value-top-class" bind:click="onClickDate" />
      <van-cell wx:if="{{fillsInfo.orderType==0}}" title="是否使用费用" value="{{isUsedCostName}}" is-link border="{{false}}" custom-class="cell-value-class" value-class="cell-price-class" bind:click="onClickCostAction" />
      <van-cell title="备注" border="{{false}}" custom-class="cell-value-bottom-class" value-class="cell-hint-class">
        <input placeholder="请输入" placeholder-class="placeholder-clazz" class="txt" bindinput="onBindinput"></input>
      </van-cell>
    </van-cell-group>
  </view>
  <!-- 底部按钮 -->
  <!-- <footer-button btnWord="提交订单" bind:mainClick="doSubmit" /> -->
  <view class="btn-layout">
    <view class="btn-submit">
      <view class="flex justify-content-end align-items-center">
        <view class="font-s24-lh44">支付金额：<text class="uom">￥</text><text class="price">{{fillsInfo.totalPayAmount||fillsInfo.total}}</text></view>
        <van-button custom-style="width:200rpx;height:80rpx" block type="primary" loading="{{submitLoading}}" loading-text="提交中,请稍后..." bind:click="doSubmit" color="#00b9c3" custom-class="custom-clazz">
          提交订单
        </van-button>
      </view>
    </view>
    <view class="box" />
  </view>
  <van-calendar show="{{ showDatePicker }}" bind:close="onDatePickerClose" bind:confirm="onDatePickerConfirm" color="#00b9c3"/>
  <van-action-sheet show="{{ showCostAction }}" actions="{{ costActions }}" cancel-text="取消" bind:close="onCloseCostAction" bind:select="onSelectCostAction" bind:cancel="onCloseCostAction" />
</view>