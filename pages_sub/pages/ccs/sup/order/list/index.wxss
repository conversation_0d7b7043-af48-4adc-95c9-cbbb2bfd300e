.root-layout {
  width: 100vw;
  height: 100vh;
}

.tab-layout {
  background-color: white;
}

.scroll-layout {
  box-sizing: border-box;
}
.item-layout{
  margin-top: 16rpx;
}
.top-layout {
  padding: 24rpx 0;
  background-color: #FFFFFF;
}

.serial-box {
  height: 44rpx;
  padding: 0 24rpx 0 16rpx;
  border-left: 8rpx solid #00b9c3;
}

.serial-box .serial {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 44rpx;
  font-weight: 400;
}

.serial-box .status {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: right;
  line-height: 44rpx;
  font-weight: 400;
}

.good-layout {
  padding: 24rpx;
  background-color: #FFFFFF;
}

.good-layout .itemname {
  margin: 0 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}

.good-layout .img {
  width: 160rpx;
  height: 160rpx;
}
.delivery-layout {
  padding: 16rpx 24rpx;
  background-color: #FFFFFF;
}
.delivery-layout .inerbox{
  padding: 0 24rpx;
  background: rgba(208,0,13,0.05);
  border-radius: 8rpx;
  height: 80rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.75);
  line-height: 80rpx;
  font-weight: 400;
}
.good-layout .cart-title {
  height: 80rpx;
  margin-bottom: 20rpx;
}
.good-layout .count-box {
  width: 100%;
}
.justify-content-between {
  justify-content: space-between;
}
.good-layout .count-box .itemprice {
  display: inline-block;
  font-family: SanFranciscoText-Medium;
  font-size: 36rpx;
  color: #ee0a24;
  line-height: 44rpx;
  font-weight: 500;
}

.good-layout .count-box .itemqty {
  display: inline-block;
  /* margin-top: 32rpx; */
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.75);
  text-align: right;
  line-height: 40rpx;
  font-weight: 400;
}

.total-layout {
  padding: 26rpx 24rpx;
  background-color: #FFFFFF;
  text-align: right;
}

.total-layout .totaltxt {
  margin-left: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}

.total-layout .totalprice {
  font-family: SanFranciscoText-Medium;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: right;
  line-height: 48rpx;
  font-weight: 500;
}

.btn-layout {
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #FFFFFF;
}

.btn-layout .logistics {
  width: 136rpx;
  height: 56rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.15);
  border-radius: 8rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 56rpx;
  font-weight: 400;
}

.txt {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.search-right {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
  margin: 0 24rpx;
}
.amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
}
.amount-title {
  font-size: 24rpx;
  line-height: 24rpx;
}
.amount-num {
  font-size: 32rpx;
  color: #ee0a24;
}
.footer-slot {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}
.footer-btn {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 36rpx;
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  padding: 14rpx 32rpx;
}
/* 重写vant样式 */
.van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}