<!-- pages_sub/pages/ccs/sup/order/list/index.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
  <view class="filter-search" id="filterSearch">
    <van-search value="{{ keyword }}" placeholder="订单号/商品编码/名称/规格" use-action-slot bind:change="onChangeKeyword" bind:search="onClickSearch">
      <view slot="action" class="search-right" bind:tap="onClickSearch">搜索</view>
    </van-search>
  </view>
    <van-tabs custom-class="custom-self-class" active="{{ deliveryStat }}" nav-class="navStyle" bind:click="onChangeTab">
      <van-tab title="全部" name="all"></van-tab>
      <van-tab title="未发货" name="{{1}}"></van-tab>
      <van-tab title="部分发货" name="{{2}}"></van-tab>
      <van-tab title="已发货" name="{{3}}"></van-tab>
      <van-tab title="已取消" name="{{99}}"></van-tab>
    </van-tabs>
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <block wx:if="{{dataList.length > 0}}">
      <goodsCardNew wx:for="{{dataList}}" isSup="{{true}}" data-stat="{{item.state}}" data-head-id="{{item.id}}" bind:clickOrder="onCheckOrder" headTitle="" goodData="{{item}}" showExpandBlock="{{true}}"  dataType="{{2}}" wx:key="index" >
        <view slot="amount" class="amount">
          <view>
            <text class="amount-title">共计</text>
            <text class="amount-title">{{item.totalQty}}</text>
            <text class="amount-title">台</text>
          </view>
          <view>
            <text class="amount-title">合计:</text>
            <text class="amount-num">￥{{item.totalAmount}}</text>
          </view>
        </view>
        <!-- detailInfo.state != 99 && detailInfo.stat != 0  && detailInfo.stat != 3     item.state == 1 || item.state == 5-->
        <view slot="footer-btn" wx:if="{{item.deliveryStat != 3 && item.deliveryStat != 2 && item.state != 4 && item.state != 99 && item.stat != 0  && item.stat != 3 && item.stat != 1}}" class="footer-slot">
          <view class="footer-btn" data-item="{{item}}" catchtap="onClickSubmitOrder">
            申请取消
          </view>
        </view>
      </goodsCardNew>
    </block>
    <view class="m-t-25p" wx:else>
      <noneView ></noneView>
    </view>
  </listView>
</view>