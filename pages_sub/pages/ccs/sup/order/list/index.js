// pages_sub/pages/ccs/sup/order/list/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dataList: [],
    deliveryStat: 'all',
    sourceBilltype: '',
    pageIndex: 1,
    pageSize: 10,
    listViewH: '',
    keyword: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.deliveryStat) {
      this.setData({
        deliveryStat: options.deliveryStat - 0
      }, () => {
        this.getOrderList() // tapNavi会自动触发一次
      })
      return
    } 
    this.getOrderList() // tapNavi会自动触发一次
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#filterSearch').boundingClientRect()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].height
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

   /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getOrderList()
  },
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getOrderList()
  },

  // ------------------------------methods------------------------------
  // 搜索条件触发查询
  onConfirm(e) {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList(e.detail)
  },
  getOrderList(){
    const supInfo = wx.getStorageSync('supInfo')
    let url = '/api/vcs/mobile-web/myx/supOrder/pageSupOrder'
    let deliveryStat = this.data.deliveryStat
    let state = deliveryStat !== 'all' && deliveryStat != '99' ? '5' : deliveryStat == '99' ? 99 : deliveryStat
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        invoiceSetsOfBooksId: supInfo.setsOfBooksId,
        appSearchKey: this.data.keyword,
        deliveryStat: deliveryStat !== 'all' && deliveryStat != '99' ? deliveryStat : undefined,
        state: state === 'all' ? undefined : state
      }
    }
    App.getHttp()._post(url,params).then(res=>{
      let dataList = this.data.pageIndex === 1 ? [] : this.data.dataList
      if(res&&res.length>0){
        
        res.forEach(item => {
          item.addLineList = item.lines
        })
        this.setData({
          dataList: dataList.concat(res)
        })
      } else {
        this.setData({
          dataList
        })
      }
    })
  },
  onCheckOrder(e){
    const dataset= e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/order/detail/index?stat=${dataset.stat}&id=${dataset.headId}`,
    })
  },
  // 输入框改变
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 确认搜索事件
  onClickSearch() {
    this.setData({
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  onChangeTab(event) {
    this.setData({
      pageIndex: 1,
      dataList: [],
      deliveryStat: event.detail.name
    })
    this.getOrderList()
  },
  // 点击提交订单事件
  onClickSubmitOrder(e) {
    const item = e.currentTarget.dataset.item
    wx.showModal({
      title: '确定是否申请取消订单？',
      content: '',
      success: (resM) => {
        if (resM.confirm) {
          this.executeSubmit(item)
        }
      }
    })
  },
  // 提交订单方法
  executeSubmit(item) {
    const url = '/api/vcs/mobile-web/myx/supOrder/closeByIds'
    const params = {
      ids: [item.id]
    }
    App.getHttp()._post(url,params).then(res => {
      wx.showToast({
        title: '订单取消成功',
        icon: 'success'
      })
      this.setData({
        pageIndex: 1,
        dataList: []
      })
      this.getOrderList()
    }).catch(error => {
      wx.showToast({
        title: '订单取消成功',
        icon: 'success'
      })
    })
  }
})