// pages_sub/pages/ccs/sup/order/receipt/detail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    detailData: {},
    showPicker: false,
    warehouseColumns: [],
    warehouseName: '',
    warehouseId: '',
    warehouseCode: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.setData({
        id: options.id
      })
      this.checkkWarehouse()
      this.getOderDetail()
    }
  },
  // 获取仓库列表
  checkkWarehouse() {
    // 选择仓库
    App.getHttp()._post('/api/psi/baseWarehouse/page', {
      pageIndex: 1,
      pageSize: 200,
      param: {
        state: 2
      }
    }).then(res => {
      let warehouseColumns = []
      for (let r = 0; r < res.length; r++) {
        warehouseColumns.push({
          code:res[r].code,
          text: res[r].name,
          id: res[r].id
        })
        if(res[r].isDefault===2){
          this.setData({
            warehouseName: res[r].name,
            warehouseId: res[r].id,
            warehouseCode: res[r].code
          })
        }
      }
      this.setData({
        warehouseColumns: warehouseColumns,
      })
    })
  },

  // 获取单个单据号信息
  getOderDetail() {
    const url = `/api/psi/sapOrderHead/myx/${this.data.id}`;
    App.getHttp()
      ._get(url)
      .then((res) => {
        if (res) {
          this.setData({
            detailData: res
          });
        }
      });
  },
  // 复制单号
  onClickCopy(e) {
    const no = e.currentTarget.dataset.no
    wx.setClipboardData({
      data: no,
      success(res) {
        wx.showToast({
          icon: 'success',
          title: '复制单号成功'
        })
      }
    })
  },
  onShowPicker(e) {
    if(this.data.detailData.state!==1 ){
      this.setData({
        showPicker: !this.data.showPicker
      })
    }
  },
   // 确认选择仓库
   onConfirmWarehouse(event) {
    let warehouseId = event.detail.value.id
    const findWarehouser =  this.data.warehouseColumns.find(res=>res.id === warehouseId)
    this.setData({
      showPicker: false,
      warehouseName: findWarehouser.text,
      warehouseId: findWarehouser.id,
      warehouseCode: findWarehouser.code
    })
  },
  // 关闭选择仓库
  onCancelWarehouse() {
    this.setData({
      showPicker: false
    })
  },
  // 标题关闭选择仓库
  onClose(){
    this.onCancelWarehouse()
  },
  // 确认签收
  onClickConfirm(){
    if(!this.data.warehouseId){
      wx.showToast({
        icon: "none",
        title: '请先选择仓库',
      })
      return
    }
    App.getHttp()._post('/api/psi/sapOrderHead/myx/update', {
      id: [this.data.id],
      warehouseId:this.data.warehouseId,
      warehouseCode:this.data.warehouseCode,
      warehouseName:this.data.warehouseName,
      version:this.data.detailData.version
    }).then(res => {
      wx.redirectTo({
        url: `/pages_sub/pages/ccs/sup/order/receipt/result/index?id=${this.data.detailData.id}&billNo=${this.data.detailData.deliverNo}`
      })
    })
  }
})