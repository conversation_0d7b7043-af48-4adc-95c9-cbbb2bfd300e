<!--pages_sub/pages/ccs/sup/order/receipt/detail/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<image class="bg-img" src="/asset/imgs/mine-bg.png"></image>
<view class="root-layout">
    <view class="head-layout flex">
        <view class="bill-state">{{detailData.stateName}}</view>
        <view class="bill-no flex align-items-center">
            <view>送货单号:</view>
            <view class="no-text">{{detailData.deliverNo}}</view>
            <van-icon size="12" bindtap="onClickCopy" data-no="{{detailData.deliverNo}}" name="/asset/imgs/purchase/copy.png"></van-icon>
        </view>
    </view>
    <block wx:if="{{detailData.lines && detailData.lines.length > 0}}">
        <view class="good-layout" wx:for="{{detailData.lines}}" wx:key="index">
            <view class="good-item">
                <image class="good-img" mode="aspectFit" src="{{item.skuPic}}"></image>
                <view class="good-info">
                    <view class="good-name-box">
                      <view class="name">{{item.skuName}}</view>
                      <view class="specs">{{item.skuSpec}}</view>
                    </view>
                    <view class="good-price">
                        <view class="price-text">￥{{item.skuPrice}}</view>
                        <view class="price-qty">x{{item.skuNum}}</view>
                    </view>
                </view>
            </view>
            <view class="total-block">
                <text class="total-label">合计:</text>
                <text class="total-text">￥{{wxsUtil.moneyFormatInt(item.skuPrice*item.skuNum,'int')}}</text>
            </view>
        </view>
    </block>
    <van-field input-align="right" readonly label="客户" value="{{detailData.custName}}"></van-field>
    <van-field readonly input-align="right" right-icon="arrow" placeholder="请选择仓库" label="入库仓库" value="{{warehouseName}}"  bind:click-icon="onShowPicker" bind:click-input="onShowPicker" />
    <van-field input-align="right" readonly label="入库时间" value="{{detailData.updateTime}}"></van-field>
    <view class="footer-block">
        <view class="footer-content ">
           <view class="footer-amount-qty">数量:{{detailData.totalSkuNum}}</view>
          <view class="footer-amount-price">
              <text class="amount-price-label">折扣金额:</text>
              <text class="amount-price-text">￥{{detailData.totalDiscountAmount}}</text>
          </view>
        </view>
        <view class="footer-content m-l-24">
           
          <view class="footer-amount-price">
              <text class="amount-price-label">金额:</text>
              <text class="amount-price-text">￥{{detailData.totalSkuPrice}}</text>
          </view>
        </view>
        <view class="footer-btn" bind:tap="onClickConfirm" wx:if="{{detailData.state === 0}}">确认签收</view>
    </view>
</view>
<van-popup show="{{ showPicker }}" round position="bottom" custom-style="height: 60%" bind:close="onClose">
    <van-picker columns="{{ warehouseColumns }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
  </van-popup>
