<!--pages_sub/pages/ccs/sup/order/receipt/index.wxml-->
<view class="root-layout bg-04">
  <view class="filter-search" id="filterSearch">
    <van-search value="{{keyword}}" bind:search="onSearch" use-action-slot bind:change="onChangeKeyword" bind:clear="onSearch" placeholder="搜索送货单">
      <view slot="action" class="search-right" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>
  <van-tabs id="tabsView" class="tab-layout" active="{{queryState}}" bind:change="tapNavi" title-active-color="#0278ff">
    <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
      <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
    </block>
  </van-tabs>
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <block wx:if="{{dataList.length > 0}}">
      <view class="item-content"  wx:for="{{dataList}}" wx:key="id" data-id="{{item.id}}"  bindtap="onClickItem" >
        <view class="item-header">
            <view class="item-bill">送货单号: {{item.deliverNo}}</view>
            <view>
              <text class="source-sys" >{{ item.stateName}}</text>
            </view>
          </view>
          <view class="item-good">
              <image class="good-img" mode="aspectFit" src="{{item.lines[0].skuPic}}"></image>
              <view class="good-info">
                <view class="info-name">{{item.custName}}</view>
                <view class="flex">
                  <view class="item-name-box">
                    <view class="name">整单数量：{{item.totalSkuNum || ''}}</view>
                    <view class="specs">整单金额：{{item.totalSkuPrice || ''}}</view>
                  </view>
                </view>
              </view>
            </view>
        <view class="audit-btn" slot="footer" wx:if="{{item.state === 0}}">
            <van-button type="primary" color="#00b9c3" size="small" data-id="{{item.id}}" catchtap="onClickAudit">入库签收</van-button>
          </view>
      </view>
    </block>
    <view class="m-t-25p" wx:else>
      <noneView></noneView>
    </view>
  </listView>
  <van-popup show="{{ show }}" round position="bottom" custom-style="height: 60%" bind:close="onClose">
    <van-picker columns="{{ warehouseColumns }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
  </van-popup>
</view>