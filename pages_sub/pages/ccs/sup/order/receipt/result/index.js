// pages_sub/pages/ccs/down/order/result/result/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id:'',
    billNo: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.billNo) {
      this.setData({
        id: options.id,
        billNo: options.billNo
      })
    }
  },
  onClickCheckDetail(){
    // 跳转到入库签收详情 关闭所有页面
   wx.redirectTo({
     url: '/pages_sub/pages/ccs/sup/order/receipt/detail/index?id=' + this.data.id,
   })
  },
  onClickBack(){
    // 跳转到入库签收 关闭所有页面
    wx.redirectTo({
      url: '/pages_sub/pages/ccs/sup/order/receipt/index',
    })
  }
 
})