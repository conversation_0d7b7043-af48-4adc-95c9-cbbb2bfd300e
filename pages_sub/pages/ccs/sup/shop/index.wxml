<!--pages_sub/pages/ccs/down/shop/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" id="page-layout">
  <!-- 顶部标题,返回 -->
  <view class="navigation-layout" style="padding-top: {{navigationTop}}px; height: {{navigationHeight}}px; padding-right: {{navigationPaddingRight}}px">
    <view class="flex  justify-content-between">
      <view class="flexbox flex align-items-center" bindtap="onClickBack">
        <image class="back" src="/asset/imgs/arrow-back.png"></image>
        <!-- <image class="back" src="/asset/svgs/free.svg"></image> -->
        <view class="title">购物车</view>
      </view>
      <view class="edit flex align-items-center" catchtap="onClickOpenEdit">
        <image class="manage" src="/asset/imgs/manage.png" mode="aspectFit" />
        {{shopSup.openEdit?"退出管理":"管理"}}
      </view>
    </view>
  </view>
  <view class="line"></view>
  <!-- 搜索区域 -->
  <search showMsgImg="{{false}}" showSearchTxt="{{true}}" disabled="{{false}}" bind:onSearchTxtClick="onSearchClick" placeholder="请输入商品名称/商品编码" />
  <!-- 页签选择区域 -->
  <van-tabs class="tab-layout" active="{{orderType}}" bind:change="onTabsChange" title-active-color="#00b9c3" line-height="3" line-width="24">
    <block wx:for="{{tabs}}" wx:for-item="tabItem" wx:key="name">
      <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}" />
    </block>
  </van-tabs>
  <!-- 提示区域 -->
  <view class="warining-layout flex align-items-center">
    <image class="warining-icon" src="/asset/imgs/purchase/warning.png"></image>
    <text class="warining-txt">只能选择同一供应商下的商品进行下单！</text>
  </view>
  <!-- 滑动区域 -->
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullRefresh" id="list-layout">
    <view class="list-layout" wx:if="{{listData.length>0}}">
      <view class="adapter-layout" wx:for="{{listData}}" wx:for-item="rootItem" wx:key="rootIndex" wx:for-index="rootIndex">
        <!-- 供应商 -->
        <view class="vendor-layout font-s32-lh48 flex align-items-center">
          <image class="radio-img m-r-24" src="{{rootItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="1" data-root-index="{{rootIndex}}" />
          <view class=" flexbox">{{wxsUtil.formatShopVendorInfo(rootItem.vendorName,rootItem.saleOrgName,rootItem.channelName)}}</view>
        </view>
        <!-- 政策,商品内容区域 -->
        <view class="card-layout" wx:for="{{rootItem.policyCartList}}" wx:for-item="cartTypeItem" wx:key="cartTypeIndex" wx:for-index="cartTypeIndex">
          <!-- 政策标题编码 -->
          <view class="policy-info-layout flex" wx:if="{{cartTypeItem.policyCode}}">
            <image class="radio-img m-r-24" src="{{cartTypeItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="2" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" />
            <view class="flexbox">
              <view class="flex">
                <view class="item-com-type" style="background-color:{{cartTypeItem.itemCombType==1?'#FAAE16':'#FF4A4D'}}">{{cartTypeItem.itemCombTypeName}}</view>
                <view class="flexbox font-s28-lh44 ">{{cartTypeItem.policyName}}</view>
              </view>
              <view class="hint m-t-16">政策编码 {{cartTypeItem.policyCode}}</view>
              <view class="hint m-t-16 flex align-items-center">
                <timer-count timelong="{{ wxsUtil.dataDiff(cartTypeItem.now , cartTypeItem.policyEndDate) }}" />
              </view>
              <view class="hint m-t-16">政策描述 <span class="des-red">{{cartTypeItem.note}}</span></view>
            </view>
          </view>
          <block wx:for="{{cartTypeItem.shoppingCartList}}" wx:for-item="cartItem" wx:key='cartId' wx:for-index="cartIndex">
            <!-- 组合商品 -->
            <block wx:if="{{cartItem.groupItems}}">
              <block wx:for="{{cartItem.groupItems}}" wx:for-item="groupItem" wx:key='groupItemIndex' wx:for-index="groupItemIndex">
                <!-- 商品区域 -->
                <van-swipe-cell right-width="{{ 160 }}">
                  <view class="good-layout flex align-items-center" data-sup-id="{{cartItem.supId}}" data-item-id="{{groupItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickGood">
                    <view class="radio-img">
                      <image class="radio-img" src="{{cartItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="3" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" data-group-item-index="{{groupItemIndex}}" />
                    </view>
                    <view class="img-layout">
                      <image class="itemurl" src="{{cartItem.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                      <image class="noqty" wx:if="{{cartItem.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
                    </view>
                    <view class="flexbox">
                      <view class="itemname two-line-ellipsis">{{groupItem.itemName}}</view>
                      <view class="itemcode single-line-ellipsis">商品编码 {{groupItem.itemCode}}</view>
                      <view class="itemprice-box flex align-items-end justify-content-between">
                        <view class="itemprice"><text class="uom">¥</text>{{groupItem.promotionPrice}}</view>
                        <view class="des flex">
                          <view class="m-r-8">配比数</view>
                          <view class="m-r-16">{{groupItem.proportioningQty}}</view>
                          <view class="m-r-8">数量</view>
                          <view class="count">{{groupItem.purchaseQty}}</view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view slot="right" class="swipe-right flex">
                    <view class=" flexbox collect-button" data-item-id="{{groupItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickCollect">收藏</view>
                    <view class="flexbox delete-button" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}"  bindtap="onClickDelete">删除</view>
                  </view>
                </van-swipe-cell>
              </block>
              <!-- 起订量,单客限量, 已购数量 -->
              <view class="limit-layout flex justify-content-between" wx:if="{{cartItem.singleCustLimit&&cartItem.singleCustLimit>1}}">
                <view class="flexbox">起订量 {{cartItem.leastQty}}</view>
                <view class="flexbox">单客户限订量 {{cartItem.singleCustLimit}}</view>
                <view class="flexbox text-right">已购数量 {{cartItem.useSingleCustLimit}}</view>
              </view>
              <!-- 组合商品的组合价 -->
              <view class="group-layout flex align-items-center justify-content-between" catchtap="onCatchNull">
                <view>
                  <text class="hint">组合政策价</text>
                  <text class="uom">¥</text>
                  <text class="itemprice">{{cartItem.groupAmount}}</text>
                </view>
                <van-stepper plus-class="plusclss" minus-class="plusclss" input-class="inputclss" integer step="1" min="1" button-size="24" input-width="48" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" value="{{ cartItem.itemCombQty }}" catch:change="onPkgChange" />
              </view>
            </block>
            <!-- 单品 -->
            <block wx:else>
              <!-- 商品区域 -->
              <van-swipe-cell right-width="{{ 160 }}" >
                <view class="good-layout flex align-items-center" data-sup-id="{{cartItem.supId}}" data-item-id="{{cartItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickGood">
                  <view class="radio-img">
                    <image class="radio-img" src="{{cartItem.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="3" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" />
                  </view>
                  <view class="img-layout">
                    <image class="itemurl" src="{{cartItem.itemUrl}}" mode="aspectFit" lazy-load="{{true}}"></image>
                    <image class="noqty" wx:if="{{cartItem.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit" lazy-load="{{true}}"></image>
                  </view>
                  <view class="flexbox">
                    <view class="itemname two-line-ellipsis">{{cartItem.itemName}}</view>
                    <view class="itemcode single-line-ellipsis">商品编码 {{cartItem.itemCode}}</view>
                    <view class="itemprice-box flex align-items-end justify-content-between" catchtap="onCatchNull">
                      <view class="itemprice">
                        <text class="uom">¥</text>{{cartItem.promotionPrice||cartItem.applyPrice}}
                        <text class="standprice" wx:if="{{cartItem.standardPrice}}">¥{{cartItem.standardPrice}}</text>
                      </view>
                      <van-stepper plus-class="plusclss" minus-class="plusclss" input-class="inputclss" integer step="1" min="1" button-size="24" input-width="48" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" value="{{ cartItem.purchaseQty }}" catch:change="onStepChange" />
                    </view>
                  </view>
                </view>
                <view slot="right" class="swipe-right flex">
                  <view class="flexbox collect-button" data-item-id="{{cartItem.itemId}}" data-cust-info="{{rootItem}}" bindtap="onClickCollect">收藏</view>
                  <view class="flexbox delete-button" data-root-index="{{rootIndex}}" data-policy-index="{{cartTypeIndex}}" data-item-index="{{cartIndex}}" bindtap="onClickDelete">删除</view>
                </view>
              </van-swipe-cell>
              <!-- 起订量,单客限量, 已购数量 -->
              <view class="limit-layout flex justify-content-between" wx:if="{{cartItem.singleCustLimit&&cartItem.singleCustLimit>1}}">
                <view class="flexbox">起订量 {{cartItem.leastQty}}</view>
                <view class="flexbox">单客户限订量 {{cartItem.singleCustLimit}}</view>
                <view class="flexbox text-right">已购数量 {{cartItem.useSingleCustLimit}}</view>
              </view>
            </block>
          </block>
        </view>
      </view>
    </view>
    <view class="m-t-25p" wx:else>
      <no-product noneTxt="快去添加商品吧" />
    </view>
  </listView>
  <!-- 底部结算按钮区域 -->
  <view class="foot-layout flex align-items-center" id="foot-layout">
    <view class="all flex align-items-center">
      <image class="radio-img" src="{{shopSup.checked?'/asset/svgs/radioS.svg':'/asset/svgs/radioN.svg'}}" catchtap="onClickChecked" data-checked-layer="0" />全选
    </view>
    <view class="flexbox total">
      <block wx:if="{{!shopSup.openEdit}}">
        <text class="txt">合计：</text><text class="uom">¥ </text><text class="amount">{{shopSup.totalMoney}}</text>
      </block>
    </view>
    <view wx:if="{{shopSup.openEdit}}" class="end-active-red" catchtap="onClickFillOrDeleteOrder">{{'删除'}}</view>
    <view wx:else class="{{shopSup.totalMoney>0 ? 'end-active':'end'}}" catchtap="onClickFillOrDeleteOrder">{{'去结算('+shopSup.totalCount+')'}}</view>
  </view>
</view>
<van-action-sheet show="{{ showDelActionView }}" actions="{{ delActions }}" cancel-text="取消" bind:select="onCloseDelActionView" bind:cancel="onCloseDelActionView" description="确定要删除商品吗?" />