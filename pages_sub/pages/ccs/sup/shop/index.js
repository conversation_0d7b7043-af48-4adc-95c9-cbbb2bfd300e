// pages_sub/pages/ccs/sup/shop/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   * 整个购物车模型,  分组(供应方+产品组+渠道)-->类型(满赠,特价,套餐,常规等)-->商品
   * rootIndex分组角标 , tIndex:订单类型角标   cIndex商品角标
   */
  data: {
    listViewH: 300,
    tabs: [
      { title: '常规', name: "0" },
      { title: '满减', name: "1" },
      { title: '满折', name: "2" },
      { title: '一口价', name: "3" },
      { title: '满赠', name: "4" },
      { title: '众筹', name: "5" }],
    orderType: '0',
    navigationTop: '',
    navigationHeight: '',
    shopSup: {
      openEdit: false,
      checked: false,
      totalMoney: '0.00',
      totalCount: 0,
    },
    listData: [],
    keywords:'',
    showDelActionView: false,
    preDeleteIds:[],
    delActions:[{name:'删除',color: '#FF4A4D'}]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.setData({
      navigationTop: App.globalData.menuButtonClientReact.top,
      navigationPaddingRight: App.globalData.windowWidth - App.globalData.menuButtonClientReact.left,
      navigationHeight: App.globalData.menuButtonClientReact.height + 6 //设计的6px
    })
    this.initCartList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#list-layout').boundingClientRect()
    query.select('#foot-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH: res[1].top - res[0].top
      })
    })
  },
  /**
   * 当小程序从前台进入后台，会触发 onHide
   */
  onHide: function () {
    this.data.shopSup.openEdit = false
    this.setData({
      shopSup: this.data.shopSup
    })
  },
  onClickBack() {
    wx.navigateBack({
      delta: 1
    })
  },
  onTabsChange(e) {
    this.setData({
      orderType: e.detail.name,
      listData: []
    })
    this.initCartList()
   
  },
  onSearchClick(e) {
    this.data.keywords = e.detail
    this.initCartList()
   },
  initCartList() {
    const params = {
      orderType:this.data.orderType,
      invoiceSetsOfBooksId: wx.getStorageSync('checkSetsOfBooksId'),
      keywords:this.data.keywords
    }
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/getCartList', params).then(res => {
      let tempList = []
      if (res) {
        tempList = res
      }
      this.data.shopSup.totalMoney = '0.00'
      this.data.shopSup.totalCount = 0
      this.data.shopSup.checked = false
      this.setData({
        listData: tempList,
        shopSup: this.data.shopSup
      })
    })
  },
  onPullRefresh() {
    this.onShow()
  },
  onClickOpenEdit() {
    this.data.shopSup.openEdit = !this.data.shopSup.openEdit
    this.setData({
      shopSup: this.data.shopSup
    })
  },
  onClickFillOrDeleteOrder() {
    // isPersent 2是赠品  1非赠品, null非赠品 后端搞得什么飞机玩意
    // 控制下结算状态下选中价钱大于0的商品
    const list = this.data.listData
    let fillsInfo = []
    let preDeleteIds = []
    for (let index = 0; index < list.length; index++) {
      const groupObj = list[index]
      const info = {
        supId: groupObj.supId,
        channelCode: groupObj.channelCode,
        channelId: groupObj.channelId,
        channelName: groupObj.channelName,
        saleOrgCode: groupObj.saleOrgCode,
        saleOrgName: groupObj.saleOrgName,
        invoiceCustCode: groupObj.invoiceCustCode,
        invoiceCustId: groupObj.invoiceCustId,
        invoiceCustName: groupObj.invoiceCustName,
        invoiceSetsOfBooksId: groupObj.invoiceSetsOfBooksId,
        vendorName: groupObj.vendorName,
        vendorCode: groupObj.vendorCode,
        vendorId: groupObj.vendorId,
        vendorSetsOfBooksId: groupObj.vendorSetsOfBooksId,
        orderType: this.data.orderType,//表头应该是能放个订单类型, 跟页签保持一致
        policyCartList: [],
        total: 0
      }
      for (let iindex = 0; iindex < groupObj.policyCartList.length; iindex++) {
        const ordTypeObj = groupObj.policyCartList[iindex];
        const orderType = {
          orderType: ordTypeObj.orderType,
          itemCombType:ordTypeObj.itemCombType,
          itemCombTypeName:ordTypeObj.itemCombTypeName,
          sourceBillHeadId:ordTypeObj.sourceBillHeadId,
          sourceBillNo:ordTypeObj.sourceBillNo,
          policyName: ordTypeObj.policyName,
          policyId: ordTypeObj.policyId,
          policyCode: ordTypeObj.policyCode,
          policyEndDate: ordTypeObj.policyEndDate,
          note: ordTypeObj.note,
          total: 0,
          giftItems: [],//为结算准备字段
          shoppingCartList: []
        }
        for (let iiindex = 0; iiindex < ordTypeObj.shoppingCartList.length; iiindex++) {
          const cartItem = ordTypeObj.shoppingCartList[iiindex];
          if (cartItem.checked) {
            orderType.shoppingCartList.push(cartItem)
            if (cartItem.id) preDeleteIds.push(cartItem.id)
            if (cartItem.itemCombQty) {
              orderType.total += cartItem.itemCombQty * cartItem.groupAmount
            } else {
              // 常规,一口价,众筹这些才有原价,其他的都给置空,免得影响后面取值
              if(this.data.orderType==1||this.data.orderType==2||this.data.orderType==4){
                cartItem.standardPrice=''
              }
              // 先用原价, 没有再用政策价, 后期要算优惠金额, 原价才能更好比对
              orderType.total += cartItem.purchaseQty * (cartItem.standardPrice||cartItem.promotionPrice||cartItem.applyPrice )
            }
          }
        }
        if (orderType.shoppingCartList.length > 0) {
          orderType.total = orderType.total.toFixed(2)
          info.policyCartList.push(orderType)
          info.total += Number(orderType.total)
        }
      }
      info.total = info.total.toFixed(2)
      if (info.policyCartList.length > 0) {
        fillsInfo.push(info)
      }
    }
    if (fillsInfo.length > 0) {
      if (this.data.shopSup.openEdit) {
        //去删除
        this.setData({
          showDelActionView:true,
          preDeleteIds: preDeleteIds
        })
      } else {
        //去结算
        if (fillsInfo.length > 1) {
          wx.showToast({
            title: '不支持多供应商一起下单,请选择其中一个!',
            icon: 'none'
          })
        } else if (fillsInfo[0].total=="NaN"||!fillsInfo[0].total) {
          wx.showToast({
            title: '商品价格为空,无法进行!',
            icon: 'none'
          })
        } else {
          wx.setStorageSync('fillsList', fillsInfo)
          wx.navigateTo({
            url: '/pages_sub/pages/ccs/sup/order/fill/index',
          })
        }
      }
    } else {
      wx.showToast({
        title: '请先勾选要结算商品!',
        icon: 'none'
      })
    }
  },
  /**
   * 套数修改,政策类
   */
  onPkgChange(e) {
    const dataset = e.currentTarget.dataset
    const rootIndex = dataset.rootIndex
    const policyIndex = dataset.policyIndex
    const itemIndex = dataset.itemIndex
    this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].itemCombQty = e.detail
    for (let index = 0; index < this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].groupItems.length; index++) {
      this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].groupItems[index].purchaseQty = e.detail * this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].groupItems[index].proportioningQty
    }
    this.calculateMoney()
    this.changeDbShopCount(this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].id, e.detail)
  },
  /**
   * 常规商品数量修改
   */
  onStepChange(e) {
    const dataset = e.currentTarget.dataset
    const rootIndex = dataset.rootIndex
    const policyIndex = dataset.policyIndex
    const itemIndex = dataset.itemIndex
    this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].purchaseQty = e.detail
    this.calculateMoney()
    this.changeDbShopCount(this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].id, e.detail)
  },
  onClickChecked(e) {
    // rootIndex供应商分组, policyIndex:政策分组, itemIndex商品组(普通商品或者分组商品), groupItemIndex分组商品明细组 
    const dataset = e.currentTarget.dataset
    const rootIndex = dataset.rootIndex
    const policyIndex = dataset.policyIndex
    const groupItemIndex = dataset.groupItemIndex
    const itemIndex = dataset.itemIndex
    let checked = false
    switch (dataset.checkedLayer) {
      // 0 全选
      case '0':
        checked = this.data.shopSup.checked
        this.data.shopSup.checked = !checked
        const groupList = this.data.listData
        for (let index = 0; index < groupList.length; index++) {
          const groupInfo = groupList[index]
          this.data.listData[index].checked = !checked
          for (let iindex = 0; iindex < groupInfo.policyCartList.length; iindex++) {
            this.data.listData[index].policyCartList[iindex].checked = !checked
            const orderType = groupInfo.policyCartList[iindex]
            for (let iiindex = 0; iiindex < orderType.shoppingCartList.length; iiindex++) {
              this.data.listData[index].policyCartList[iindex].shoppingCartList[iiindex].checked = !checked
            }
          }
        }
        break;
      // 1 供应商层面
      case '1':
        this.data.shopSup.checked = false
        const rootInfo = this.data.listData[rootIndex]
        checked = rootInfo.checked
        this.data.listData[rootIndex].checked = !checked
        for (let index = 0; index < rootInfo.policyCartList.length; index++) {
          this.data.listData[rootIndex].policyCartList[index].checked = !checked
          const orderType = rootInfo.policyCartList[index]
          for (let iindex = 0; iindex < orderType.shoppingCartList.length; iindex++) {
            this.data.listData[rootIndex].policyCartList[index].shoppingCartList[iindex].checked = !checked
            const groupItems = orderType.shoppingCartList[iindex].groupItems || [] //普通商品没有.groupItems给个默认值防御下
            for (let iiindex = 0; iiindex < groupItems.length; iiindex++) {
              this.data.listData[rootIndex].policyCartList[index].shoppingCartList[iindex].groupItems[iiindex].checked = !checked
            }
          }
        }
        break;
      // 2 政策层面
      case '2':
        this.data.shopSup.checked = false
        const orderType = this.data.listData[rootIndex].policyCartList[policyIndex]
        checked = orderType.checked
        this.data.listData[rootIndex].policyCartList[policyIndex].checked = !checked
        for (let iindex = 0; iindex < orderType.shoppingCartList.length; iindex++) {
          this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[iindex].checked = !checked
          const groupItems = orderType.shoppingCartList[iindex].groupItems || [] //普通商品没有.groupItems给个默认值防御下
          for (let iiindex = 0; iiindex < groupItems.length; iiindex++) {
            this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[iindex].groupItems[iiindex].checked = !checked
          }
        }
        break;
      // 3 商品层面
      case '3':
        this.data.shopSup.checked = false
        checked = this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].checked
        this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].checked = !checked
        const groupItems = this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].groupItems || [] //普通商品没有.groupItems给个默认值防御下
        for (let iiindex = 0; iiindex < groupItems.length; iiindex++) {
          this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex].groupItems[iiindex].checked = !checked
        }
        break;
      default:
        break;
    }
    this.setData({
      listData: this.data.listData,
      shopSup: this.data.shopSup
    })
    this.calculateMoney()
  },
  calculateMoney() {
    let totalMoney = 0
    let totalCount = 0
    const list = this.data.listData
    for (let index = 0; index < list.length; index++) {
      const groupObj = list[index]
      for (let iindex = 0; iindex < groupObj.policyCartList.length; iindex++) {
        const ordTypeObj = groupObj.policyCartList[iindex];
        for (let iiindex = 0; iiindex < ordTypeObj.shoppingCartList.length; iiindex++) {
          const cartItem = ordTypeObj.shoppingCartList[iiindex];
          if (cartItem.checked) {
            if (cartItem.itemCombQty) {
              totalMoney += cartItem.itemCombQty * cartItem.groupAmount
              totalCount += cartItem.itemCombQty *1
            } else {
              totalMoney += cartItem.purchaseQty * (cartItem.promotionPrice||cartItem.applyPrice )
              totalCount += cartItem.purchaseQty*1
            }
          }
        }
      }
    }
    this.data.shopSup.totalMoney = totalMoney.toFixed(2)
    this.data.shopSup.totalCount = totalCount
    this.setData({
      shopSup: this.data.shopSup,
      listData:this.data.listData
    })
  },
  deleteShopCart() {
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/deleteCart', {
      ids:this.data.preDeleteIds
    }).then(res => {
      if(res.failedQty===0){
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        setTimeout(() => {
          this.initCartList()
        }, 2000);
      }else{
        wx.showToast({
          title: res.failedList.join(";"),
          icon: 'none',
          duration:3000
        })
      }
    })
  },
  changeDbShopCount(cartId,qty) {
    let params = {
      id:cartId,
      purchaseQty: qty,
    }
    delete params.policyCartList
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/updateCart', params).then(res => {
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      })
    })
  },
  onCatchNull(){},
  // 跳转详情
  onClickGood(e) {
    const custInfo = e.currentTarget.dataset.custInfo
    const itemId = e.currentTarget.dataset.itemId
    const supId = e.currentTarget.dataset.supId
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/skuDetail/index?itemId=${itemId}&vendorId=${custInfo.vendorId}&vendorCode=${custInfo.vendorCode}&vendorSetsOfBooksId=${custInfo.vendorSetsOfBooksId}&invoiceCustCode=${custInfo.invoiceCustCode}&invoiceCustId=${custInfo.invoiceCustId}&invoiceSetsOfBooksId=${custInfo.invoiceSetsOfBooksId}&channelId=${custInfo.channelId}&saleOrgCode=${custInfo.saleOrgCode}&supId=${supId}`
    })
  },
  // 收藏商品
  onClickCollect(e){
    const custInfo = e.currentTarget.dataset.custInfo
    const itemId = e.currentTarget.dataset.itemId
    const params = {
      vendorName: custInfo.vendorName,
      vendorCode: custInfo.vendorCode,
      vendorSetsOfBooksId: custInfo.vendorSetsOfBooksId,
      invoiceCustCode: custInfo.invoiceCustCode,
      invoiceSetsOfBooksId:custInfo.invoiceSetsOfBooksId,
      supId:custInfo.supId,
      itemId: itemId,
      sourceModule:'up'//向上模块的收藏
    }
    App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect', params).then(res => {
      wx.showToast({
        title: '已收藏',
        icon:'success'
      })
    })
  },
  // 删除商品
  onClickDelete(e){
    const {rootIndex,policyIndex,itemIndex,groupItemIndex} = e.currentTarget.dataset
    const cartItem = this.data.listData[rootIndex].policyCartList[policyIndex].shoppingCartList[itemIndex]
     this.setData({
      showDelActionView:true,
      preDeleteIds: [cartItem.id]
    })
  },
  // 删除所选
  onCloseDelActionView(e){
    if(e.type&&e.type=='select'){
      this.deleteShopCart()
    }
    this.setData({
      showDelActionView:false
    })
  }
})