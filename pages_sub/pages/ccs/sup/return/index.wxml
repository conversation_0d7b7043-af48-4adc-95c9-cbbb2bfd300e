<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout bg-04">
  <view class="filter-search" id="filterSearch">
    <van-search value="{{keyword}}" bind:search="onSearch" use-action-slot bind:change="onChangeKeyword" bind:clear="onSearch" placeholder="搜索订单">
      <view slot="action" class="search-right" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>
  <van-tabs id="tabsView" class="tab-layout" active="{{queryState}}" bind:change="tapNavi" title-active-color="#02a7af">
    <block wx:for="{{typeList}}" wx:for-item="tabItem" wx:key="name">
      <van-tab title="{{tabItem.title}}" name="{{tabItem.name}}"></van-tab>
    </block>
  </van-tabs>
  <listView class="scroll-layout" viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <block wx:if="{{dataList.length > 0}}">
      <view class="listItemLayout" wx:for="{{dataList}}" wx:key="id">
        <supOutItem itemObj="{{item}}" isAudit="{{false}}" data-id="{{item.id}}" bindtap="onClickItem">
          <view class="audit-btn" slot="footer" wx:if="{{item.isConfirm == 1}}">
            <view class="scan-rate">
              <text wx:if="{{item.sunQty}}">扫码进度：{{wxsUtil.formatPercent(item.scanRate,'%')}} ({{item.sunScanQty||0}}/{{item.sunQty}})</text>
            </view>
            <van-button size="small" data-id="{{item.id}}" catchtap="onClickAudit">确认出库</van-button>
          </view>
        </supOutItem>
      </view>
    </block>
    <view class="m-t-25p" wx:else>
      <noneView></noneView>
    </view>
  </listView>
</view>