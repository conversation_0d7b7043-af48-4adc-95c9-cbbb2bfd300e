// pages_sub/pages/ccs/sup/return/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    queryState: '3',
    typeList:[
      {title:'全部',name:'3'},
      {title: '待出库', name: '1'},
      {title: '已出库', name: '2'}
    ],
    keyword: '',
    pageIndex: 1,
    pageSize: 10,
    dataList: [],
    listViewH: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getOrderList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        const query = wx.createSelectorQuery()
        query.select('#tabsView').boundingClientRect()
        query.select('#filterSearch').boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec(function(exceRes){
          let scrollH2 = res.windowHeight - exceRes[0].bottom - App.globalData.deviceBottomOccPx
          that.setData({
            listViewH: scrollH2
          })
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageIndex:1,
      dataList:[],
    })
    this.getOrderList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 加载更多事件
  onLoadMore: function () {
    this.setData({
      pageIndex:(this.data.pageIndex+1)
    })
    this.getOrderList()
  },
  // 搜索框确认事件
  onSearch() {
    this.setData({
      pageIndex: 1
    })
    this.getOrderList()
  },
   // 搜索框内容修改事件
  onChangeKeyword(e) {
    this.setData({
      keyword: e.detail
    })
  },
  // 切换导航栏事件
  tapNavi: function (e) {
    this.setData({
      queryState: e.detail.name,
      pageIndex: 1,
      dataList: []
    })
    this.getOrderList()
  },
  // 请求列表事件
  getOrderList() {
    const url = '/api/psi/invSupInBillHead/myx/itemBackPage'
    const params={
      pageIndex:this.data.pageIndex,
      pageSize:this.data.pageSize,
      param:{
        billNo: this.data.keyword,
        isConfirm: this.data.queryState === '3' ? '' : this.data.queryState,
        state:5
      }
    }
    App.getHttp()._post(url, params).then(res => {
      let dataList = this.data.pageIndex === 1 ? [] : this.data.dataList
      if(res && res.length > 0) {
        this.setData({
          dataList: dataList.concat(res)
        })
      } else {
        this.setData({
          dataList
        })
      }
    })
  },
  // 出库跳转
  onClickAudit(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/sup/return/audit/index?id=' + id
    })
  },
  // 点击块跳转
  onClickItem(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/sup/return/detail/index?id=' + id
    })
  }
})