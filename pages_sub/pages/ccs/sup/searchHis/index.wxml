<!-- pages_sub/pages/ccs/sup/searchHis/index.wxml -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<list-head isSup="{{true}}" id="listHead" showSupplier="{{false}}" customTitle="搜索商品"></list-head>
<view class="page" id="page-layout">
  <!-- 顶部区域 -->
  <van-sticky offset-top="{{offsetTop}}">
    <van-search value="{{ keyword }}" placeholder="请输入搜索关键词" use-action-slot bind:change="onChangeKeyword" bind:search="onConfirm">
      <view slot="action" class="search-right" bind:tap="onConfirm">搜索</view>
    </van-search>
  </van-sticky>
  <view class="good-filter flex-box align-center" wx:if="{{!showHis}}" id="filter-layout">
    <van-icon name="/asset/imgs/purchase/filler-filled.png" bindtap="onClickFilter" size="20"></van-icon>
  </view>
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onPullDownRefresh" bind:loadmore="onLoadMore">
    <view class="history-layout">
      <block wx:if="{{showHis}}">
        <view class="flex align-items-center justify-content-between">
          <view class="title">历史记录</view>
          <view class="clean flex align-items-center" bindtap="onClearAll">
            <image class="icon" src="/asset/imgs/purchase/delete.png"></image>
          </view>
        </view>
        <view class="listbox flex">
          <view class="label" wx:for="{{hisList}}" wx:key="historyId" bindtap="onClickHisKeyWord" data-keyword="{{item.keyWord}}">
            {{item.keyWord}}
          </view>
        </view>
      </block>
      <view wx:else>
        <view class="new-product">
          <block wx:if="{{goodsList.length > 0}}">
            <view class="goodsList flex-box align-center" wx:for="{{goodsList}}" data-item="{{item}}" bindtap="onClickGood" wx:key="index">
              <view class="good-img-layout">
                <image class="good-img" src="{{item.itemUrl || item.picUrl}}" mode="aspectFit"></image>
                <image class="good-noQty" wx:if="{{item.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit"></image>
              </view>
              <view class="good-info">
                <view class="good-name">{{item.itemName}}</view>
                <!-- <view class="good-specs">规格: {{item.specs}}</view> -->
                <view class="">
                  <view class="good-price">
                    <view class="price-info">
                      <text class="price-symbol" space="false" wx:if="{{item.applyPrice}}">¥</text>
                      <text class="price-text" space="false">{{item.applyPrice ? wxsUtil.moneyFormatInt(item.applyPrice, 'int') : '暂无报价'}}</text>
                      <text class="price-rem" space="false" wx:if="{{item.applyPrice}}">{{wxsUtil.moneyFormatInt(item.applyPrice)}} </text>
                    </view>
                    <!-- <text class="onhand-type type-{{item.qtyOnhand == '无货' ? 1 : 2}}">
                      {{item.qtyOnhand}}
                    </text> -->
                    <van-icon name="/asset/imgs/purchase/good-shopCar.png" data-item="{{item}}" catchtap="onClickToShopCar" size="20"></van-icon>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <!-- 缺省 -->
          <view class="none" wx:else>
            <no-product noneTxt="暂无商品" />
          </view>
        </view>
      </view>
    </view>
  </listView>
  <add-carts item="{{addItemInfo}}" bind:onchange="onchange" show="{{showAddCartsPop}}" bind:onClose="onCloseAddCarts" isSup="{{true}}"></add-carts>
  <van-popup show="{{ showFilter }}" position="right" close-on-click-overlay="{{false}}" custom-style="width: 616rpx;height: 100%; padding-top: {{offsetTop}}px" bind:close="onCloseFilter">
    <view class="filter-content">
      <view class="filter-head">
        <view class="filter-title">品类</view>
        <view class="expend-block" bindtap="onClickExpand">
          <block wx:if="{{!isExpand}}">
            <view class="expand-text">收起</view>
            <van-icon size="12" color="#757575" name="arrow-up" />
          </block>
          <block wx:else>
            <view class="expand-text">展开</view>
            <van-icon size="12" color="#757575" name="arrow-down" />
          </block>
        </view>
      </view>
      <van-grid column-num="4" border="{{false}}">
        <van-grid-item use-slot content-class="grid-content" wx:for="{{wxsUtil.formatGoodList(isExpand, typeList, 8)}}" wx:key="index">
          <view class="grid-item {{activeFilterType == item.id ? 'active-filter-type' : ''}}" data-type="{{item.id}}" bindtap="onClickFilterType">
            {{item.name}}
          </view>
        </van-grid-item>
      </van-grid>
    </view>
    <!-- <view class="filter-content">
      <view class="filter-title">价格区间</view>
      <view class="price-interval flex-box align-center">
        <input placeholder="最低价" type="number" bindchange="startChange" class="price-input price-start" value="{{priceStart}}" type="text" />
        —
        <input placeholder="最高价" type="number" bindchange="endChange" value="{{priceEnd}}" class="price-input price-end" type="text" />
      </view>
    </view> -->
    <view class="filter-footer flex-box align-center">
      <view class="filter-cancel" bindtap="onClickReset">重置</view>
      <view class="filter-confirm" bindtap="onClickConfirmFilter">确定</view>
    </view>
  </van-popup>
</view>