<!--pages_sub/pages/ccs/sup/skuDetail/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout" id="page-layout">
<!-- 头部导航 -->
<list-head id="listHead" showSupplier="{{false}}" customTitle="商品详情" isSup></list-head>
<!-- 热卖-新品 -->
<!-- <van-tabs active="{{ scrollTab }}" nav-class="navStyle" bind:click="onTopChangeTab">
  <van-tab title="商品" name="swiper_layout"></van-tab>
  <van-tab title="详情" name="more_layout"></van-tab>
</van-tabs> -->
<scroll-view  scroll-into-view="{{scrollIntoView}}" id="scroll-layout" scroll-y style="height: {{scrollViewHeightPx}}px;">
  <view class="swiper-layout" id="swiper_layout">
    <adSwiper adList="{{mainPicUrl}}" heightRpx="{{windowWidthRpx}}" indicatorActive="#00b9c3" autoHttp="{{false}}" indicatorDots="{{false}}"
      autoplay="{{true}}"  indicatorCount="{{true}}" radiusRpx="0" bind:onClickAd="onClickAdListener" />
  </view>
  <view class="item-layout">
    <view wx:if="{{custAndItemInfo.beAccessory !== '2'}}" class="price-box flex align-items-center justify-content-between">
      <view class="itemprice">
        <text wx:if="{{dInfo.beSell == 2}}" class="uom">促销价 </text>
        <text class="uom">¥</text>
        <text class="current">{{dInfo.applyPrice > 0 ? wxsUtil.moneyFormatInt(dInfo.applyPrice,'int') : '--'}}</text>
        <text class="price-rem" space="false" wx:if="{{dInfo.applyPrice > 0}}">{{wxsUtil.moneyFormatInt(dInfo.applyPrice)}} </text>
      </view>
      <!-- <view class="stock-count">{{dInfo.qtyOnhand}}</view> -->
    </view>
    <view class="name-box">
      <view class="itemname">{{dInfo.itemName}} {{dInfo.itemCode}}</view>
      <view class="good-specs">{{dInfo.specs || ''}}</view>
      <view class="good-specs">
        <text class="good-qty-stock" wx:if="{{custAndItemInfo.beAccessory !== '2'}}">库存：{{dInfo.stockQty}} </text>
        <text wx:if="{{custAndItemInfo.beAccessory !== 2 && item.isBulkOrder === 2}}">整托数：{{dInfo.itemNumber}}</text>
      </view>
      <view wx:if="{{custAndItemInfo.beAccessory === '2'}}" class="good-specs">
        <van-field
          label="序列号"
          value="{{ serialNumber }}"
          placeholder="请输入"
          border="{{ false }}"
          bind:change="onSerialNumberChange"
        />
      </view>
    </view>
  </view>
  <view class="more-layout" id="more_layout">
    <van-tabs active="{{ activeMoreValue }}"  bind:click="onMoreTabChange">
      <van-tab wx:if="{{custAndItemInfo.beAccessory === '2'}}" title="配件列表" name="accessory">
        <view style="margin-top: 34rpx; background: #fff;" wx:if="{{accessoryList.length>0}}">
          <view class="accessory-list" wx:for="{{accessoryList}}" wx:key="index">
            <text>{{item.explodeCode}} {{item.name}}</text>
            {{' ' + '库存:' + item.stockQty}}
            <view style="display: inline-block; float: right;">
              <text class="price-sym">￥</text>
              <text class="price-num">{{item.itemPrice > 0 ? (item.itemPrice || 0) : '--'}}</text>
              <van-stepper disabled="{{item.itemPrice > 0 ? false : true}}" style="display: inline-block; vertical-align: top;" plus-class="plusclss" minus-class="plusclss" input-class="inputclss" value="{{item.qty}}" async-change integer step="1" min="0" button-size="24" input-width="48" data-index="{{index}}" bind:change="onChangeStep"/>
            </view>
          </view>
        </view>
        <view class="m-t-25p" wx:else>
          <no-product noneTxt="未维护配件清单" />
        </view>
      </van-tab>
      <van-tab title="商品参数" name="1">
      <view class="line"></view>
         <!-- 产品参数区域 -->
        <van-cell-group class="cell-group-layout" border="{{false}}">
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="商品编码:" value="{{dInfo.itemCode}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="商品名称:" value="{{dInfo.itemName}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="规格:" value="{{dInfo.specs || ''}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="销售单位:" value="{{dInfo.uomName}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="额定流量(m³/h):" value="{{dInfo.ratedFlowRate || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="额定扬程(m):" value="{{dInfo.ratedHead || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="扬程使用范围(m):" value="{{dInfo.rangeRatedHead || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="出水口径(mm/英寸):" value="{{dInfo.outCaliber || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="进出口径(mm/英寸):" value="{{dInfo.entranceCaliber || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="额定功率(kW):" value="{{dInfo.ratedPower || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />

          <!-- <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="商品大类:" value="{{dInfo.bigClassName}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="重量单位:" value="{{dInfo.weightUomname}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="净重:" value="{{dInfo.netWeight || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="物料事业部:" value="{{dInfo.materialDepartmentName || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="产品系列:" value="{{dInfo.itemSeriesName || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="是否促销:" value="{{dInfo.beSellName || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="是否可下配件:" value="{{dInfo.beAccessoryName || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="物料价格分类:" value="{{dInfo.materialPriceType || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="额定扬程:" value="{{dInfo.ratedHead || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center />
          <van-cell title-width="210rpx" title-style="padding-right: 20rpx" title="税组合:" value="{{dInfo.taxCombination || ' '}}" border="{{true}}" custom-class="cell-value-class" value-class="cell-price-class"  center /> -->
        </van-cell-group>
      </van-tab>
      <van-tab title="图片详情" name="2">
        <view wx:if="{{desPicUrl.length>0}}">
          <image class="pic-card" mode="widthFix" wx:for="{{desPicUrl}}" wx:key="index" src="{{item}}" lazy-load></image>
        </view>
        <view class="m-t-25p" wx:else>
          <no-product noneTxt="" />
        </view>
      </van-tab>
    </van-tabs>
  </view>
  <view class="placeholder-layout"></view>
</scroll-view>

  <!-- <view wx:if="{{dInfo.qtyOnhand=='无货'}}" class="none-stock-layout">该商品已无货，您可以看看别的～</view> -->
  <view class="foot-layout flex">
    <view wx:if="{{custAndItemInfo.beAccessory !== '2' && custAndItemInfo.rcmdType !== '3'}}" class="flexbox flex align-items-center">
      <view class="flexbox flex column align-items-center" bindtap="onClickFocus">
        <image class="img" src="{{dInfo.isFav?'/asset/imgs/focus.png':'/asset/imgs/focus-o.png'}}"></image>
        <view class="icontxt">{{dInfo.isFav?'取消':'收藏'}}</view>
      </view>
    </view>
    <view wx:if="{{custAndItemInfo.beAccessory === '2'}}" class="add accessory-add" catchtap="onClickAdd">加入购物车</view>
    <view wx:if="{{custAndItemInfo.beAccessory !== '2' && dInfo.applyPrice > 0 && !(rcmdType === '3' && dInfo.stockQty<=0)}}" class="add" catchtap="onClickAdd">加入购物车</view>
    <!-- <view class="buy" catchtap="onClickFill">立即下单</view> -->
  </view>
  <add-carts item="{{dInfo}}" isSup="{{true}}" show="{{showAddCartsPop}}" bind:onClose="onCloseAddCarts"></add-carts>
  <pre-view show="{{showPreView}}" list="{{[this.data.mainPicUrl[0].picUrl]}}"></pre-view>
</view>