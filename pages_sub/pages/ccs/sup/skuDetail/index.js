// pages_sub/pages/ccs/sup/skuDetail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    scrollTab:2,
    windowWidthRpx: 375,
    scrollViewHeightPx:500,
    scrollIntoView:'swiper_layout',
    activeMoreValue: '1',
    serialNumber: '',
    custAndItemInfo:{},
    dInfo: {},//商品信息详情
    accessoryList: [], // 配件列表
    mainPicUrl: [],
    preImageUrl: [],
    desPicUrl: [],
    isFocused: false,
    showAddCartsPop: false,
    cartCount: 0,
    showPreView: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.itemId) {
      this.data.custAndItemInfo = {
        ...options,
      }
      
      this.setData({
        custAndItemInfo: this.data.custAndItemInfo,
        activeMoreValue: this.data.custAndItemInfo.beAccessory == '2' ? 'accessory' : '1',
        rcmdType: options.rcmdType
      })
      this.iniData()
    }
    // this.getCartsCount()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#page-layout').boundingClientRect()
    query.select('#scroll-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        scrollViewHeightPx: res[0].height - res[1].top
      })
    })
    wx.getSystemInfo({
      success: (result) => {
        this.setData({
          windowWidthRpx: (result.windowWidth * App.globalData.pxToRpxRatio)
        })
      },
    })
  },
  iniData: function (param) {
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/getPurItemDetail', this.data.custAndItemInfo).then(res => {
      if (res) {
        let mainPicUrl = []
        if (this.data.custAndItemInfo.beAccessory !== '2') {
          if (!res.mainUrl && (!res.bannerUrls || res.bannerUrls.length <= 0)) {
            mainPicUrl = [{
              picUrl: '/asset/imgs/bg_shop.png'
            }]
          } else {
            if(res.mainUrl){
              mainPicUrl.push({
                picUrl: res.mainUrl
              })
            }
            if(res.bannerUrls&&res.bannerUrls.length>0){
              res.bannerUrls.map(v => {
                mainPicUrl.push( {
                  picUrl: v.url
                })
              })
            }
          }
        }
        const descriptionImages = res.specsUrls ? res.specsUrls : []
        this.setData({
          dInfo: {
            ...res,
            itemId:res.id,
            itemName:res.name,
            itemCode:res.code
          },
          mainPicUrl: mainPicUrl,
          preImageUrl: mainPicUrl.map(mainItem => mainItem.picUrl),
          desPicUrl: descriptionImages,
        }, () => {
          if (this.data.custAndItemInfo.beAccessory === '2') {
            this.getAccessoryInfo()
          } else {
            this.initCollect()
          }
        })
      }
    })
  },
  onClickAdListener(event) {
    if (this.data.custAndItemInfo.beAccessory === '2') {
      wx.previewImage({
        current: this.data.mainPicUrl[0].picUrl, // 当前显示图片的http链接
        urls: [this.data.mainPicUrl[0].picUrl] // 需要预览的图片http链接列表
      })
    }
  },
  async initCollect() {
    const queryParam = [];
    if (this.data.custAndItemInfo.beAccessory === '2') {
      this.data.accessoryList.forEach(item => {
        queryParam.push({
          itemCode: item.code,
          orgCode: this.data.custAndItemInfo.saleOrgCode,
          beSell: this.data.dInfo.beSell,
          itemType: '配件',
        });
      })
    } else {
      queryParam.push({
        itemCode: this.data.dInfo.itemCode,
        orgCode: this.data.custAndItemInfo.saleOrgCode,
        beSell: this.data.dInfo.beSell,
        itemType: this.data.custAndItemInfo.rcmdType === '3' ? '促销' : this.data.dInfo.itemTypeName,
      });
    }
    if (!queryParam || queryParam.length <= 0) ruturn
    const content = await App.getHttp()._post(`/api/interface/erp/stock/query`, {query:queryParam})
    if (this.data.custAndItemInfo.beAccessory === '2') {
      this.data.accessoryList.forEach(item => {
        let contentFind = content.find(itemF => itemF.itemCode === item.code)
        item.stockQty = contentFind.stockQty
      })
    } else {
      this.data.dInfo.stockQty = content[0].stockQty || 0;
      this.setData({
        dInfo: this.data.dInfo
      })
    }
  },
  async getAccessoryInfo() {
    const content = await App.getHttp()._post(`/api/interface/erp/accessoryInfo/query`, {
      itemCode: this.data.dInfo.itemCode,
      custCode: this.data.custAndItemInfo.invoiceCustCode,
      orgCode: this.data.custAndItemInfo.saleOrgCode,
      supId: this.data.custAndItemInfo.supId,
    })
    if (content.accessoryInfoLineList.length == 0 || !content.accessoryInfoLineList) {
      wx.showToast({
        title: '未维护配件清单',
      })
    }
    if (content.accessoryInfoLineList) {
      this.setData({
        accessoryList: content.accessoryInfoLineList.map(item => {
          return {
            ...item,
            qty: 0,
            prodCode: content.prodCode,
            prodImage: content.prodImage,
            prodName: content.prodName,
            prodSpc: content.prodSpc
          }
        }),
        mainPicUrl: [{
          picUrl: content.prodImage
        }],
        preImageUrl: content.prodImage,
      }, () => {
        this.initCollect()
      })
    } else {
      this.setData({
        accessoryList: [],
        mainPicUrl: [{
          picUrl: content.prodImage
        }],
        preImageUrl: content.prodImage,
      }, () => {
        this.initCollect()
      })
    }
  },
  onSerialNumberChange(event) {
    this.setData({
      serialNumber: event.detail
    })
  },
  // 去购物车
  onClickShop() {
    wx.navigateTo({
      url: '/pages_sub/pages/ccs/down/shop/index',
    })
  },
  // 关注回调
  onClickFocus() {
    const params = {
      vendorName: wx.getStorageSync('vendorInfo').bvendorName,
      vendorCode: wx.getStorageSync('vendorInfo').bvendorCode,
      vendorSetsOfBooksId: wx.getStorageSync('vendorInfo').ssetsOfBooksId,
      invoiceCustCode: wx.getStorageSync('vendorInfo').scustCode,
      invoiceSetsOfBooksId: wx.getStorageSync('vendorInfo').bsetsOfBooksId,
      itemId: this.data.dInfo.itemId,
      sourceModule:'up'//向上模块的收藏
    }
    if (this.data.isFocused) {
      App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect/not', params).then(res => {
        this.setData({
          isFocused: false
        })
      })
    } else {
      App.getHttp()._post('/api/mms/itemCollectRelation/myx/collect', params).then(res => {
        wx.showToast({
          title: '已关注',
          icon:'success'
        })
        this.setData({
          isFocused: true
        })
      })
    }
  },
  // 配件数据调整
  onChangeStep(event) {
    let accessoryList = this.data.accessoryList
    let index = event.currentTarget.dataset.index
    let detail = event.detail
    accessoryList[index].qty =detail
    this.setData({
      accessoryList: accessoryList
    })
  },
  // 购物车添加卡片调出
  onClickAdd() {
    if (this.data.custAndItemInfo.beAccessory === '2') {
      this.onCloseAddCarts({
        detail: this.data.accessoryList
      })
      return
    } 
    this.setData({
      showAddCartsPop: true,
      btnType: 'add',
    })
  },
  // 立即下单
  onClickFill() {
    this.setData({
      showAddCartsPop: true,
      btnType: 'buy'
    })
  },
  // 添加到购物车
  onCloseAddCarts(v) {
    this.setData({
      showAddCartsPop: false
    })
    if (!v.detail) return
    const reault = v.detail
    const params = {
      ...this.data.custAndItemInfo,
      orderType: 0, // 订单类型
      qtyModel: 2,//增加
      serialNumber: this.data.serialNumber,
      items: []
    }
    if (this.data.custAndItemInfo.beAccessory === '2') {
      // 配件参数
      let priceZero = false
      let itemName = ''
      let items = []
      reault.forEach(item => {
        if (item.qty > 0) {
          if (!priceZero) {
            priceZero = (item.itemPrice > 0) ? false : true
            itemName = item.explodeCode + ' ' + item.name
          }
          let itemObj = {
            itemId: item.id, // 商品ID
            itemName: item.name, // 商品名称
            itemCode: item.code, // 商品编码
            itemCount: item.qty, // 购买数量
            orderType: 0,
            shoppingCartItemType: 2,
            explodeCode: item.explodeCode,
            hasRemake: item.hasRemake,
            prodSpc: item.prodSpc,
            prodCode: item.prodCode,
            prodImage:item.prodImage,
            prodName: item.prodName,
            serialNumber: this.data.serialNumber,
          }
          items.push(itemObj)
        }
      })
      if (!items || items.length <= 0) {
        wx.showToast({
          title: '无配件数量！',
          icon: 'none'
        })
        return
      }
      if (priceZero) {
        wx.showToast({
          title: `${itemName} 无价格`,
          icon: 'none'
        })
        return
      }

      params.items = items
      delete params.itemId
      delete params.beAccessory
      delete params.qtyModel
      delete params.orderType
    } else {
      // 成品促销参数
      if (reault.qty == 0) {
        wx.showToast({
          title: "购买数量不能为0",
          icon: "error",
        });
        return;
      }
      if (reault.isBulkOrder === 2 && !reault.itemNumber) {
        wx.showToast({
          title: "缺少商品整托数量，请联系管理员!",
          icon: "none",
        });
        return;
      }
      if (reault.isBulkOrder === 2 && reault.qty % reault.itemNumber > 0) {
        wx.showToast({
          title: "整托商品下单数量必须是整托数的整数倍!",
          icon: "none",
        });
        return;
      }
      if (reault.beSell === 2 && reault.qty > reault.stockQty) {
        wx.showToast({
          title: "促销商品下单数量超出了库存数量!",
          icon: "none",
        });
        return;
      }
      params.items = [{
        itemId: reault.itemId, // 商品ID
        itemName: reault.itemName, // 商品名称
        itemCode: reault.itemCode, // 商品编码
        itemCount: reault.qty, // 购买数量
        orderType: 0,
      }]
    }
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/addToCart', params).then(res => {
      console.log('提醒查看0000')
      this.getCartsCount()
      wx.showToast({
        title: '加购成功',
        icon: 'success',
        duration: 2000
      })
    })
  },
  // 数量调整回调
  getCartsCount() {
    this.selectComponent("#listHead").getCartsCount();
  },
  // 回到顶部
  onTopChangeTab(e){
    this.setData({
      scrollIntoView:e.detail.name
    })
  }
})