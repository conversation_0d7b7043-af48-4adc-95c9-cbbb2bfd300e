// pages_sub/pages/ccs/sup/policy/policySearchHis/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showHis: true,
    listViewH: 500,
    active: '0',
    tabList: [],
    showList: [],
    serchValue: '',
    showPop: false,
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    navTop: 24,
    navHeight: 44,
    url: '',
    itemInfo: {},
    busCode: '',
    promotionType: '',
    busModule: 'vcs',
    showDialog:false,
    itemInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let url = ''
    let busCode = ''
    let promotionType = ''
    let busModule = ''
    let type = options.type
    console.log('type',options.type)
    // promotionType 1满减 2满折 3一口价 4满赠 5众筹
    if (type == 'crowdfunding') {
      url = "/api/vcs/policyCrowdConfigOrder/getPageCrowdOrders"
      busCode = 'crowdfunding'
      promotionType = 5
      busModule = 'vcs'
    }else if(type== 'buyoutPrice'){
      url = "/api/vcs/policy/policyShoping/page"
      busCode = 'buy_it_now'
      promotionType = 3
      busModule = 'vcs'
    }else if(type== 'reduction'){
      url = "/api/vcs/policy/policyShoping/page"
      busCode = 'full_reduction'
      promotionType = 1
      busModule = 'vcs'
    }else if(type== 'discount'){
      url = "/api/vcs/policy/policyShoping/page"
      busCode = 'full_discount'
      promotionType = 2
      busModule = 'vcs'
    }else if(type== 'salesGift'){
      url = "/api/vcs/policy/policyShoping/page"
      busCode = 'full_gift'
      promotionType = 4
      busModule = 'vcs'
    }
    console.log('url',url)
    this.setData({
      type: options.type,
      url,
      busCode,
      promotionType,
      busModule
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  onSearchCancel() {
    this.setData({ searchValue: '' })
  },
  onSearchClick(e) {
    this.setData({
      searchValue: e.detail,
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.selectComponent('#searchHistory').addSearchHist(e.detail)
    this.loadMore()
  },
  onClickHisKeyWord(e){
    console.log('e',e)
    this.setData({
      searchValue: e.detail,
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.selectComponent('#search').setData({
      searchValue: e.detail
    })
    this.loadMore()
  },
  onSearchChange(e) {
    this.setData({ searchValue: e.detail })
    console.log('searchValue', this.data.searchValue)
  },
  onRefresh() {
    console.log('6666')
    this.setData({
      pageIndex: 1,
      pages: 0,
      showList: [],
    })
    this.loadMore()
  },
  loadMore() {
    if (this.data.pages && this.data.pages < this.data.pageIndex + 1) {
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    let param ={
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        keywords: this.data.searchValue,
        channelCode: vendorInfo.channelCode,
        channelId: vendorInfo.channelId,
        custId: vendorInfo.scustId,
        saleOrgCode: vendorInfo.saleOrgCode,
        supId: vendorInfo.supId,
        "promotionType": this.data.promotionType, // 1满减 2满折 3一口价 4满赠 5众筹
        "publishState": 2, // 已发布
        "state": 2, // 状态生效
        "addType": 1 // 1 :厂家新增 2: 客户新增
      }
    }
    let newParam = {
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        keywords: this.data.searchValue,
        vendorSetsOfBooksId: vendorInfo.ssetsOfBooksId,
        supId: vendorInfo.supId,
        crowdType: 1,
        isRelease: 2,
        isUsable: 2,
      }
    }
    App.getHttp()._post(this.data.url, this.data.promotionType == 5 ? newParam : param, true).then(res => {
      // console.log('itemList', res)
      this.setData({
        showList: this.data.promotionType == 5 ? this.data.showList.concat(res.recordList ||[]) : this.data.showList.concat(this.handleItemUrl(res.recordList ||[])),
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
      console.log('showList', this.data.showList)
    })
  },
  onItemClick(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let item = this.data.showList[index]
    let url = item.itemCombType == 1 ? `/pages_sub/pages/ccs/sup/policy/single/index?id=${item.id}&type=${this.data.type}` : `/pages_sub/pages/ccs/sup/policy/group/index?id=${item.id}&type=${this.data.type}`
    wx.navigateTo({
      url: url,
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  addToCarts(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param={
      "orderType": 5, // 众筹
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  vendorInfo.scustId,
      "invoiceCustCode":  vendorInfo.scustCode,
      policyCode: this.data.showList[index].code,
      policyId: this.data.showList[index].id,
      saleOrgCode: vendorInfo.saleOrgCode, //销售组织
      policyName: this.data.showList[index].name,
      purchaseQty: 1,
      items:this.handleItemList(index),
      sourceSystem:'2',
      supId: vendorInfo.supId,
      channelId: vendorInfo.channelId,
      channelCode: vendorInfo.channelCode,
    }
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/addToCart', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  // 过滤前三张图片
  handleItemUrl(list){
    list.find(item=>{
      // 单品
      item.itemUrlList = []
      item.totalItemCount = 0
      if(item.itemCombType == 1){
        item.totalItemCount = item.policyConfigItemResponseDTOList.length
        item.policyConfigItemResponseDTOList.find(item2=>{
          if(item2.itemUrl){
            item.itemUrlList.push(item2.itemUrl)
          }
          return item.itemUrlList.length == 3
        })
      }else{
        // 组合
        item.policyConfigItemResponseDTOList.forEach((item2,index2)=>{
          item2.policyItemGroupLineResponseDTOList.find((item3,index3)=>{
            if(index3 == 0){
              item.totalItemCount += item2.policyItemGroupLineResponseDTOList.length
            }
            if(item3.itemUrl){
              item.itemUrlList.push(item3.itemUrl)
            }
            return item.itemUrlList.length == 3
          })
        })
      }
    })
    console.log('过滤长度3list', list)
    return list
  },
  handleItemList(index){
    let item1 = this.data.showList[index]
    return [item1].map(item=>{
      let newItem = {}
      newItem.itemId = item.itemId
      newItem.itemCode = item.itemCode
      newItem.itemName = item.itemName
      newItem.itemCount = 1
      newItem.purchaseQty = 1
      newItem.configItemId = item.id
      return newItem;
    })
  },
  showDialog(e){
    this.setData({itemInfo: e.detail, showDialog: true})
  },
  goBack() {
    wx.navigateBack({
      delta: 1
    })
  },
})