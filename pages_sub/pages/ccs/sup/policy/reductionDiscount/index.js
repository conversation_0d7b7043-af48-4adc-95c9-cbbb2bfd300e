// pages_sub/pages/ccs/sup/policy/reductionDiscount/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH:500,
    showList:[],
    serchValue: '',
    showPop: false,
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    steps:[],
    navTop: 24,
    navHeight: 24,
    vendorInfo: {},
    custInfo:{}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      promotionType: options.promotionType
    })
    this.loadMore();
  },
  onShow: function () {
    const vendorInfo = wx.getStorageSync("vendorInfo");
    const custInfo = wx.getStorageSync("custInfo");
    this.setData({vendorInfo, custInfo})
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.initPage()
  },
  onSearchClick(e){
    // console.log(11)
    // this.setData({
    //   searchValue: e.detail,
    //   pageIndex: 1,
    //   pages: 0,
    //   showList: [],
    // })
    // this.loadMore()
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/policy/policySearchHis/index?type=${this.data.promotionType == 1 ? 'reduction': 'discount'}`,
    })
  },
  onRefresh(){
    this.setData({
      showList: [],
      pageIndex: 1,
      pages: 0,
    })
    this.loadMore()
  },
  loadMore(){
    if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    let param ={
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        // receiveUserSetsId: userInfo.setsOfBooksId,
        keywords: this.data.searchValue,
        channelCode: vendorInfo.channelCode,
        channelId: vendorInfo.channelId,
        custId: vendorInfo.scustId,
        saleOrgCode: vendorInfo.saleOrgCode,
        supId: vendorInfo.supId,
        "promotionType": this.data.promotionType, // 1满减 2满折 3一口价 4满赠 5众筹
        "publishState": 2, // 已发布
        "state": 2, // 状态生效
        "addType": 1 // 1 :厂家新增 2: 客户新增
      }
    }
    console.log(param)
    // /api/mms/news/myx/page
    App.getHttp()._post('/api/vcs/policy/policyShoping/page', param, true).then((res)=>{
      console.log('itemList',res)
      this.setData({
        showList: this.data.showList.concat(this.handleItemUrl(res.recordList ||[])),
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
    })
  },
  // 过滤前三张图片
  handleItemUrl(list){
    list.find(item=>{
      // 单品
      item.itemUrlList = []
      item.totalItemCount = 0
      if(item.itemCombType == 1){
        item.totalItemCount = item.policyConfigItemResponseDTOList.length
        item.policyConfigItemResponseDTOList.find(item2=>{
          if(item2.itemUrl){
            item.itemUrlList.push(item2.itemUrl)
          }
          return item.itemUrlList.length == 3
        })
      }else{
        // 组合
        item.policyConfigItemResponseDTOList.forEach((item2,index2)=>{
          item2.policyItemGroupLineResponseDTOList.find((item3,index3)=>{
            if(index3 == 0){
              item.totalItemCount += item2.policyItemGroupLineResponseDTOList.length
            }
            if(item3.itemUrl){
              item.itemUrlList.push(item3.itemUrl)
            }
            return item.itemUrlList.length == 3
          })
        })
      }
    })
    console.log('过滤长度3list', list)
    return list
  },
  onItemClick(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let item = this.data.showList[index]
    let url = item.itemCombType == 1 ? `/pages_sub/pages/ccs/sup/policy/single/index?id=${item.id}&type=${this.data.promotionType == 1 ? 'reduction': 'discount'}` : `/pages_sub/pages/ccs/sup/policy/group/index?id=${item.id}&type=${this.data.promotionType == 1 ? 'reduction': 'discount'}`
    wx.navigateTo({
      url: url,
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})