<!--pages_sub/pages/ccs/sup/policy/reductionDiscount/index.wxml-->
<view class="root-layout" id="root-layout">
  <van-sticky class="top-layout" id="top-layout">
    <list-head isShowType="{{false}}" showSupplier="{{false}}" showSupplierText="{{false}}" customTitle="{{promotionType == 1 ? '满减' : '满折'}}" isSup="{{true}}"/>
    <policy-search bind:onSearchClick="onSearchClick"/>
  </van-sticky>
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onListPullRefresh" bind:loadmore="onLoadmore">
    <view class="policy-bvendorName">{{custInfo.custType == 5 ? '经销渠道 |' : ''}} {{vendorInfo.bvendorName}}</view>
    <block wx:if="{{showList.length>0}}">
      <view wx:for="{{showList}}" wx:for-item="item" wx:key="index" data-value="{{index}}" bindtap="onItemClick">
        <policy-list-item itemInfo="{{item}}" />
      </view>
    </block>
    <view class="m-t-25p" wx:else>
      <no-product noneTxt="暂无数据" />
    </view>
  </listView>
</view>