// pages_sub/pages/ccs/sup/policy/crowdfundingDetail/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    navTop: 24,
    navHeight: 44,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({id: options.id})
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  getData(){
    const vendorInfo = wx.getStorageSync('vendorInfo')
    let param={}
    App.getHttp()._get(`/api/vcs/policyCrowdConfigOrder/queryCrowdOrderById/${this.data.id}/${vendorInfo.supId}`, param, true).then((res)=>{
      this.setData({dataInfo: res})
      console.log('list',this.data.dataInfo)
    })
  },
  addToCarts(){
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param={
      "orderType": 5, // 众筹
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  custInfo.custId,
      "invoiceCustCode":  custInfo.custCode,
      "policyId": this.data.dataInfo.id,
      purchaseQty: 1,
      items:this.handleItemList(),
      sourceSystem:'2',
    }
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/addToCart', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  handleItemList(){
    let item1 = this.data.dataInfo
    return [item1].map(item=>{
      let newItem = {}
      newItem.itemId = item.itemId
      newItem.itemCode = item.itemCode
      newItem.itemName = item.itemName
      newItem.itemCount = 1
      newItem.purchaseQty = 1
      newItem.configItemId = item.id
      return newItem;
    })
  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
})