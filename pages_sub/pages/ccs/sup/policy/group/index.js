// pages_sub/pages/ccs/sup/policy/group/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dataInfo:{},
    id: '',
    type: '',
    title: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let type = options.type
    let title = ''
    if(type== 'buyoutPrice'){
      title = '一口价组合'
    }else if(type== 'reduction'){
      title = '满减组合'
    }else if(type== 'discount'){
      title = '满折组合'
    }else if(type== 'salesGift'){
      title = '满赠组合'
    }
    this.setData({id: options.id, type: options.type,title})
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
  onSearchClick(){
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/policy/policySearchHis/index?type=${this.data.type}`,
    })
  },
  getData(){
    const vendorInfo = wx.getStorageSync('vendorInfo')
    // let param={
    //   id: this.data.id,
    //   custId: custInfo.custId,
    //   custCode: custInfo.custCode
    // }
    App.getHttp()._get(`/api/vcs/policy/queryPolicyShopingByIdAndSupId/${this.data.id}/${vendorInfo.supId}`, {}, true).then((res)=>{
      this.setData({dataInfo: res})
      console.log('list', res, this.data.dataInfo)
    })
  },
  addToCarts(e){
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let policyConfigItemResponseDTOItem = this.data.dataInfo.policyConfigItemResponseDTOList[index]
    console.log('storage', vendorInfo, supInfo, custInfo)
    let param={
      "orderType": this.data.dataInfo.promotionType,
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  vendorInfo.scustId,
      "invoiceCustCode":  vendorInfo.scustCode,
      "policyId": this.data.dataInfo.id,
      "policyCode": this.data.dataInfo.policyCode,
      saleOrgCode: vendorInfo.saleOrgCode, //销售组织
      policyName: this.data.dataInfo.policyName,
      items:this.handleItemList(index),
      sourceSystem:'2',
      supId: vendorInfo.supId,
      channelId: vendorInfo.channelId,
      channelCode: vendorInfo.channelCode,
      itemCombQty: 1,
    }
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/addToCart', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  handleOrderType(){
    // orderType
    // COMMON(0, "普通"),
    // REDUCE(1, "满减政策订单"),
    // DISCOUNT(2, "满折政策订单"),
    // SP_PRICE(3, "一口价政策订单"),
    // GIFT(4, "满赠政策订单"),
    // CROWDFUND(5, "众筹订单");
    // promotionType 字典值
    // 1满减 2满折 3一口价 4满赠 5众筹
    switch(dataInfo.policyType){
      case 1: return 1;
      case 2: return 2;
      case 3: return 3; 
      case 4: return 4; 
      case 5: return 5; 
    }
  },
  handleItemList(index){
    let policyConfigItemResponseDTOItem = this.data.dataInfo.policyConfigItemResponseDTOList[index]
    let itemList = [policyConfigItemResponseDTOItem].map(item=>{
      let newItem = {}
      // 子集
      newItem.purchaseQty = 1;
      newItem.configItemId = item.id
      newItem.itemCombId= item.groupId
      newItem.itemCombCode= item.groupCode
      newItem.itemCombName= item.groupName
      newItem.itemCombQty= item.groupQty
      newItem.groupAmount= item.groupAmount
      newItem.combItems= item.policyItemGroupLineResponseDTOList
      return newItem;
    })
    return itemList
  }

})