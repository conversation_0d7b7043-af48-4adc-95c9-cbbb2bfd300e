<!--pages_sub/pages/ccs/sup/policy/crowdfunding/index.wxml-->
<view class="root-layout" id="root-layout">
  <van-sticky class="top-layout" id="top-layout">
    <list-head isShowType="{{false}}" showSupplier="{{false}}" showSupplierText="{{false}}" customTitle="众筹" isSup="{{true}}"/>
    <policy-search bind:onSearchClick="onSearchClick"/>
  </van-sticky>
  <listView viewHeightPx="{{listViewH}}" bind:pullRefresh="onListPullRefresh" bind:loadmore="onLoadmore">
    <block wx:if="{{showList.length>0}}">
      <view wx:for="{{showList}}" wx:for-item="item" wx:key="index" data-value="{{index}}">
        <policy-list-item itemInfo="{{item}}" promotionType="{{5}}" bind:addToCarts="addToCarts" data-value="{{index}}" isSup="{{true}}" bind:showDialog="showDialog"/>
      </view>
    </block>
    <view class="m-t-25p" wx:else>
      <no-product noneTxt="暂无数据" />
    </view>
  </listView>
  <crowdfunding-dialog itemInfo="{{itemInfo}}" showDialog="{{showDialog}}"/>
</view>