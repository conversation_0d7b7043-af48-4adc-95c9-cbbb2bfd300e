// pages_sub/pages/ccs/sup/policy/crowdfunding/index.js
const App = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    listViewH:500,
    showList:[],
    serchValue: '',
    showPop: false,
    pageIndex: 1,
    pageSize: 20,
    pages: 0,
    steps:[],
    navTop: 24,
    navHeight: 24,
    vendorInfo: {},
    custInfo: {},
    showDialog:false,
    itemInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadMore()
  },
  onShow: function () {
    const vendorInfo = wx.getStorageSync("vendorInfo");
    const custInfo = wx.getStorageSync("custInfo");
    this.setData({vendorInfo, custInfo})
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      this.setData({
        listViewH: res[0].height - res[1].height - App.globalData.deviceBottomOccPx
      })
    })
  },
  onSearchClick(e){
    // console.log(11)
    // this.setData({
    //   searchValue: e.detail,
    //   pageIndex: 1,
    //   pages: 0,
    //   showList: [],
    // })
    // this.loadMore()
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/policy/policySearchHis/index?type=crowdfunding`,
    })
  },
  onRefresh(){
    this.setData({
      showList: [],
      pageIndex: 1,
      pages: 0,
    })
    this.loadMore()
  },
  loadMore(){
    if(this.data.pages && this.data.pages < this.data.pageIndex + 1){
      wx.showToast({
        title: '没有更多数据了...',
        duration: 3000,
        icon: 'none'
      })
      return;
    }
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    // let param ={
    //   "pageIndex": this.data.pageIndex,
    //   "pageSize": this.data.pageSize,
    //   "param": {
    //     keywords: this.data.searchValue,
    //     channelCode: vendorInfo.channelCode,
    //     channelId: vendorInfo.channelId,
    //     custId: custInfo.custId,
    //     saleOrgCode: vendorInfo.saleOrgCode,
    //     supId: vendorInfo.supId,
    //     "promotionType": 5, // 1满减 2满折 3一口价 4满赠 5众筹
    //     "publishState": 2, // 已发布
    //     "state": 2, // 状态生效
    //     "addType": 1 // 1 :厂家新增 2: 客户新增
    //   }
    // }
    // console.log(param)
    // // /api/mms/news/myx/page
    // App.getHttp()._post('/api/vcs/policy/policyShoping/page', param, true).then((res)=>{
    //   console.log('itemList',res)
    //   this.setData({
    //     showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
    //     pageIndex: this.data.pageIndex + 1,
    //     pages: res.pages
    //   })
    // })
    // 列表返回价格区间
    let newParam = {
      "pageIndex": this.data.pageIndex,
      "pageSize": this.data.pageSize,
      "param": {
        vendorSetsOfBooksId: vendorInfo.ssetsOfBooksId,
        crowdType: 1,
        supId: vendorInfo.supId,
        isRelease: 2,
        isUsable: 2,
      }
    }
    App.getHttp()._post('/api/vcs/policyCrowdConfigOrder/getPageCrowdOrders', newParam, true).then((res)=>{
        this.setData({
        showList: res.recordList ? this.data.showList.concat(res.recordList) : [],
        pageIndex: this.data.pageIndex + 1,
        pages: res.pages
      })
    })
  },
  initPage() {
    const query = wx.createSelectorQuery()
    query.select('#root-layout').boundingClientRect()
    query.select('#top-layout').boundingClientRect()
    query.exec((res) => {
      console.log('listViewH', res[0].height, res[1].height, res[0].height - res[1].height)
      this.setData({
        listViewH: res[0].height - res[1].height,
      })
    })
  },
  goBack(){
    wx.navigateBack({
      delta: 1
    })
  },
  onItemClick(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    let item = this.data.showList[index]
    wx.navigateTo({
      url: `/pages_sub/pages/ccs/sup/policy/crowdfundingDetail/index?id=${item.id}`,
    })
  },
  addToCarts(e){
    let index = e.currentTarget.dataset.value // 获取传入的参数
    const custInfo = wx.getStorageSync('custInfo')
    const vendorInfo = wx.getStorageSync('vendorInfo')
    const supInfo = wx.getStorageSync('supInfo')
    let param={
      "orderType": 5, // 众筹
      "vendorSetsOfBooksId": vendorInfo.ssetsOfBooksId,
      "vendorId": vendorInfo.bvendorId,
      "vendorCode": vendorInfo.bvendorCode,
      "invoiceSetsOfBooksId": supInfo.setsOfBooksId,
      "invoiceCustId":  vendorInfo.scustId,
      "invoiceCustCode":  vendorInfo.scustCode,
      policyCode: this.data.showList[index].code,
      "policyId": this.data.showList[index].id,
      policyName: this.data.showList[index].name,
      saleOrgCode: vendorInfo.saleOrgCode, //销售组织
      purchaseQty: 1,
      items:this.handleItemList(index),
      sourceSystem:'2',
      supId: vendorInfo.supId,
      channelId: vendorInfo.channelId,
      channelCode: vendorInfo.channelCode,
    }
    App.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/addToCart', param, true).then((res)=>{
      wx.showToast({
        icon: "none",
        title: "加入购物车成功",
      });
    })
  },
  handleItemList(index){
    let item1 = this.data.showList[index]
    return [item1].map(item=>{
      let newItem = {}
      newItem.itemId = item.itemId
      newItem.itemCode = item.itemCode
      newItem.itemName = item.itemName
      newItem.itemCount = 1
      newItem.purchaseQty = 1
      newItem.configItemId = item.id
      return newItem;
    })
  },
  showDialog(e){
    this.setData({itemInfo: e.detail, showDialog: true})
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})