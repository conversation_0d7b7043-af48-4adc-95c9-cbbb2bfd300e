// custom-tab-bar/index.js
const App = getApp();
Component({
  /**
   * 组件的初始数据
   */
  data: {
    selected: 0,
    color: "#424242",
    selectedColor: "#00b9c3",
    list: [],
    isShow: true //控制显示隐藏tabbar 
  },

  attached() {
    this.initPageAuth()
  },

  /**
   * 组件的方法列表
   */
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index
      getApp().tabbarIndex = index
      this.setData({
        selected: index
      }, function () {
        wx.switchTab({ url })
        // 如果跳转到首页，重新刷新首页数据
        // if (url === '/pages/ccs/home/<USER>') {
          // EventBus.emit('refresh-home-data')
        // }
      })
    },
    initSelected(index) {
      this.setData({
        selected: index || 0
      })
    },

    // 查询页面权限
    initPageAuth() {
      // 字典对应customerType:1经销,2分销商, 3门店, 3
      const custType = wx.getStorageSync('custInfo').custType
      const custMenus = wx.getStorageSync('custMenus')
      let tabBarPage = [{
        "pagePath": "/pages/ccs/workbench/index",
        "text": "工作台",
        "iconPath": "/asset/imgs/tab/bench.png",
        "selectedIconPath": "/asset/imgs/tab/bench_s.png"
      }]
      // 这部分还是保持原样，把path换成统一的
      if (custType === 5) {
        const tabBar1Path = '/pages/ccs/sup/purchase/index'
        if(custMenus.findIndex(menu=>menu.perms&&menu.perms==tabBar1Path)>-1){
          tabBarPage.push({
            // "pagePath": tabBar1Path,
            "pagePath": "/pages/ccs/purchase/index",
            "text": "采购",
            "iconPath": "/asset/imgs/tab/purchase.png",
            "selectedIconPath": "/asset/imgs/tab/purchase_s.png"
          })
        }
      }
      if (custType == 6||custType == 7||custType == 8) {
        const tabBar2Path = '/pages/ccs/down/purchaseNew/index'
        if(custMenus.findIndex(menu=>menu.perms&&menu.perms==tabBar2Path)>-1){
          tabBarPage.push({
            // "pagePath": tabBar2Path,
            "pagePath": "/pages/ccs/purchase/index",
            "text": "采购",
            "iconPath": "/asset/imgs/tab/purchase.png",
            "selectedIconPath": "/asset/imgs/tab/purchase_s.png"
          })
        }
      }
      if(custType&&custMenus.findIndex(menu=>menu.perms&&menu.perms=='/pages/ccs/retail/index')>-1){
        tabBarPage.push({
          "pagePath": "/pages/ccs/retail/index",
          "text": "零售",
          "iconPath": "/asset/imgs/tab/sale.png",
          "selectedIconPath": "/asset/imgs/tab/sale_s.png"
        })
      }
      tabBarPage.push({
        "pagePath": "/pages/ccs/mine/index",
        "text": "我的",
        "iconPath": "/asset/imgs/tab/mine.png",
        "selectedIconPath": "/asset/imgs/tab/mine_s.png"
      })
      this.setData({
        list:tabBarPage
      })
    }
  }
})