/* custom-tab-bar/index.wxss */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  box-shadow: 0px -2px 4px 0px rgba(0,0,0,0.05);
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: #F5F5F5;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1rpx;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
}
.tab-bar-badge {
  position: absolute;
  top: 4rpx;
  left: 50%;
  font-size: 20rpx;
  background-color: red;
  color: #fff;
  width: 36rpx;
  height: 32rpx;
  line-height:32rpx;
  text-align: center;
  border-radius: 32rpx;
  z-index: 2;
}

.tab-bar-item .img {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.tab-bar-item .txt {
  font-family: PingFang SC-Regular, PingFang SC;
  font-size: 18rpx;
  font-weight: 400;
  color: #424242;
}

