<!--custom-tab-bar/index.wxml-->
<cover-view class="tab-bar" wx:if="{{isShow}}">
  <!-- <cover-view class="tab-bar-border"></cover-view> -->
  <cover-view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <cover-image class="img" src="{{selected === index ? item.selectedIconPath : item.iconPath}}"></cover-image>
    <cover-view class="txt" style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</cover-view>
    <cover-view wx:if="{{item.tabBarBadge}}" class="tab-bar-badge">{{item.tabBarBadge}}</cover-view>
  </cover-view>
</cover-view>
