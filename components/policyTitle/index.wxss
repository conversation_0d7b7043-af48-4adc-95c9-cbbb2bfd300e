/* components/policyTitle/index.wxss */
.policy-top-box{
  display: flex;
  background: #FFF7EE;
}
.policy-top-box-zc{
  background: #E1E6FE;
}
.border-box{
  position: absolute;
  top: 0;
  right: 0;
  width: 248rpx;
  height: 0;
  border-width: 0 0 144rpx 48rpx;
  border-style: solid;
  border-color: transparent transparent #FFEED9 transparent;
}
.border-box-zc{
  border-color: transparent transparent #CEDCFF transparent;
}
.countDown-box{
  position: relative;
  width: 296rpx;
  height: 144rpx;
}
.countDown-box-padding{
  padding-right: 24rpx;
}
.policy-top{
  flex: 1;
  padding-left: 24rpx;
}
.policyName-box{
  margin-top: 24rpx;
  overflow: hidden;
  max-height: 88rpx;
  font-size: 32rpx;
  line-height: 48rpx;
  font-weight: 500;
  color: #242424;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.policy-tag{
  display: inline-block;
  padding: 0 8rpx;
  height: 40rpx;
  font-size: 24rpx;
  text-align: center;
  line-height: 40rpx;
  margin-right: 8rpx;
  border-radius: 8rpx;
  vertical-align: top;
  margin-top: 4rpx;
}
.tag-single{
  color: #fff;
  background: #FAAE16;
}
.tag-group{
  color: #fff;
  background: #FF4A4D;
}
.policy-code{
  display: inline-block;
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #242424;
  white-space: nowrap;
}
.policy-code-txt{
  display: inline-block;
  color: #707070;
}
/* 满减满赠提示语 */
.discount-box{
  display: flex;
  align-items: center;
  height: 72prx;
  line-height: 72rpx;
  font-size: 24rpx;
  background: #FF7573;
  color: #FFFFFF;
}
.discount-box-img{
  width: 32rpx;
  height: 32rpx;
  margin-left: 32rpx;
  margin-right: 16rpx;
  font-size: 0;
  line-height: 0;
}