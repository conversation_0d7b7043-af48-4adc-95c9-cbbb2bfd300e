<!--components/policyTitle/index.wxml-->
<view class="policy-top-box  {{promotionType == 5 ? 'policy-top-box-zc' : '' }}">
  <view class="policy-top">
    <view class="policyName-box">
      <view wx:if="{{promotionType != 5}}" class="policy-tag {{dataInfo.itemCombType == 1 ? 'tag-single': 'tag-group'}}">{{dataInfo.itemCombTypeName}}</view>{{dataInfo.policyName || dataInfo.name}}
    </view>
    <view class="policy-code">
      <view class="policy-code-txt">政策单号</view> {{dataInfo.policyCode || dataInfo.code}}
    </view>
  </view>
  <view class="countDown-box">
    <view class="countDown-box-padding">
      <countDown endTime="{{dataInfo.endDate || dataInfo.endTime}}"  startTime="{{dataInfo.startDate || dataInfo.startTime}}" isWrap="{{true}}" />
    </view>
    <view class="border-box {{promotionType == 5 ? 'border-box-zc' : ''}}"></view>
  </view>
</view>
<view class="discount-box" wx:if="{{dataInfo.promotionType != 3 && dataInfo.note}}">
  <image class="discount-box-img" src="/asset/imgs/discount.png"/>
  {{dataInfo.note}}
</view>