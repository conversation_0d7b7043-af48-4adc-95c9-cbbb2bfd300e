<!--components/benchGrid/index.wxml-->
<block wx:if="{{title}}">
  <view class="title">{{ title }}</view>
  <view class="line"></view>
</block>
<view class="content">
  <van-grid border="{{false}}" column-num="{{columnNum}}" icon-size="{{iconSize}}">
    <van-grid-item wx:for="{{layoutList}}" content-class="content-grid-item-class" wx:key="index" link-type="navigateTo" url="{{item.to}}" badge="{{showBadge?item.badge:''}}" use-slot>
    <view class="img-layout">
      <image style="width: {{iconSize}}; height: {{iconSize}};" src="{{item.icon}}" mode="aspectFit" />
      <image wx:if="{{!!operate}}" class="operate-img" src="{{operate=='add'?'/asset/imgs/bench/add.png':'/asset/imgs/bench/del.png'}}" mode="aspectFit" catchtap="onClickOperate" data-item="{{item}}" data-index="{{index}}"/>
    </view>
      <view class="van-item-txt">{{item.text}}</view>
    </van-grid-item>
  </van-grid>
</view>