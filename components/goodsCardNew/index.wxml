<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="good-card" wx:if="{{goodData.id}}" bindtap="onClickGood">
    <view wx:if="{{showState}}" class="good-head flex align-items-center justify-content-between">
        <view class="title">
            <text class="custom-title">{{headTitle}}</text>
            <text>{{goodData.orderNo}}</text>
        </view>
        <view wx:if="{{goodData.state === 99}}" class="good-status good-status-close">
            已取消
        </view>
        <view wx:else class="good-status good-status-{{goodData.deliveryStat || 3}} {{goodData.deliveryStatName == '部分发货' ? 'toDeliver' : ''}}">
            {{goodData.deliveryStatName || '已发货'}}
        </view>
    </view>
    <view wx:if="{{showState}}" class="supInfo">
        <view class="supplier">{{goodData.invoiceCustName}}</view>
        <view class="supplier">{{goodData.saleOrgName}}</view>
    </view>
    <view class="good-content"  wx:for="{{goodData.addLineList}}" wx:key="index">
      <block>
        <view class="flex">
          <image class="good-img" wx:if="{{item.itemUrl}}" mode="aspectFit" src="{{item.itemUrl}}"></image>
          <image class="good-img"  wx:else src="/asset/imgs/bg_shop.png" mode="aspectFit"></image>
          <view class="content-right flexbox flex">
            <view class="supplier">
              {{item.itemCode}} {{item.itemName}}
              <!-- / {{item.itemCode}} -->
            </view>
            <view class="good-info">
              <view class="good-name-box">
                <!-- <view class="good-name">{{item.itemName}}</view> -->
                <view class="good-specs">规格：{{item.specs || ''}}</view>
              </view>
              <view class="good-price flex">
                <view>
                  <text class="price-symbol" space="false">¥</text>
                  <text class="price-text" space="false">{{item.applyPrice}}</text>
                  <!-- <text class="price-rem" space="false">{{wxsUtil.moneyFormatInt(item.applyPrice)}}</text> -->
                  <view class="price-delete" wx:if="{{item.applyPrice!=item.originalPrice}}">
                    <text class="price-symbol price-delete" space="false">¥</text>
                    <text class="price-text price-delete" space="false">{{item.originalPrice}}</text>
                    <!-- <text class="price-rem price-delete" space="false">{{wxsUtil.moneyFormatInt(item.originalPrice)}}</text> -->
                  </view>
                </view>
                <view class="flex align-items-center">
                  <van-icon size="12" color="#9E9E9E" name="cross" />
                  <view class="good-qty">{{item.applyQty}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{showAuxiliary}}" class="item-auxiliary">
          <view class="auxiliary-row">
            <text class="auxiliary-text">已发货：{{item.deliveredQty || 0}}</text>
            <text class="auxiliary-text">缺货：{{item.outStockQty || 0}}</text>
            <text class="auxiliary-text">取消：{{item.cancelQty || 0}}</text>
          </view>
        </view>
      </block>
    </view>
    <slot name="amount"></slot>
    <slot name="footer-btn"></slot>
</view>