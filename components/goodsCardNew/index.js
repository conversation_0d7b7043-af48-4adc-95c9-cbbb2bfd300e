// components/goodsCardNew/index.js
const App = getApp();
Component({
  /**
   * 组件的属性列表
   */
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },
  properties: {
    headTitle: {
      type: String,
      value: "单号",
    },
    // 数据格式
    dataType: {
      type: Number,
      value: 1, // 1为 orderHead orderLines, 1传各自字段 2为 {addLineList} 2取goodData
    },
    goodData: {
      type: Object,
      value: {},
    },
    orderHead: {
      type: Object,
      value: {},
    },
    orderLines: {
      type: Object,
      value: {},
    },
    showAllInfo: {
      type: Boolean,
      value: true,
    },
    showExpandBlock: {
      type: Boolean,
      value: false,
    },
    showQty: {
      type: Boolean,
      value: false,
    },
    showState: {
      type: Boolean,
      value: true,
    },
    showReturnQty: {
      type: Boolean,
      value: false
    },
    showAuxiliary: {
      type: Boolean,
      value: false
    },
    isSup: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showExpand: true,
    showToSign: false,
    signType: 1, // 1 差异签收 2 签收
    outItemInfo: {},
    outItemHead: {},
    changeSignQty: 0,
    showPickWarehouse: false,
    curWarehouseName: "",
    curWarehouseId: "",
    warehouseColumns: [],
    stateDict: wx.getStorageSync('dictMap').stat
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClickExpand() {
      this.setData({
        showExpand: !this.data.showExpand,
      });
    },
    onClickGood() {
      this.triggerEvent("clickOrder");
    },
    // 签收跳转
    onClickToSign(e) {
      const signType = e.currentTarget.dataset.type
      const id = e.currentTarget.dataset.id;
      const outHead = e.currentTarget.dataset.outHead;
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/down/order/signed/index?id=${id}&signType=${signType}&outHeadId=${outHead.outHeadId}`,
      })
    },
  },
});
