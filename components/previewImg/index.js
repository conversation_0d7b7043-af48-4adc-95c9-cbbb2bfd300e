// components/modal/modal.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    //是否显示modal弹窗
    show: {
      type: Boolean,
      value: false
    },
    //控制底部是一个按钮还是两个按钮，默认两个
    list: {
      type: Array,
      value: [] 
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    preCurrentIndex:1,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onSwiperChange(e){
      this.setData({
        preCurrentIndex:(e.detail.current+1)
      })
    },
    // 点击modal的回调函数
    clickMask() {
      // 点击modal背景关闭遮罩层，如果不需要注释掉即可
      this.cancel()
    },
   // 点击取消按钮的回调函数
    cancel() {
      this.setData({ show: false })
      this.triggerEvent('cancel')  //triggerEvent触发事件
    },
    // 拦截掉不让传递,防止长按会关闭
    catchClick() {
    }
  }
})
