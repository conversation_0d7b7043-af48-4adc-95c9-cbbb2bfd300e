/* components/modal/modal.wxss */
/*遮罩层*/
.modal-mask{
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0,0,0,1);
  z-index: 999;
}
/*遮罩内容*/
.modal-content{
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  height: 100%;
}
.img-close{
  color: white;
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  top: 32rpx;
  left: 26rpx;
}
.count-layout{
  position: absolute;
  top: 32rpx;
  left: 26rpx;
  right: 26rpx;
  color: white;
  text-align: center;
}
.full-swiper{
 width: 100vw;
 height: 100vw;
}
.ad-image{
  width: 100%;
}