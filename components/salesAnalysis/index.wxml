<!-- components/salesAnalysis/index.wxml -->
<view class="root-layout">
    <view class="analysis-head">销售分析</view>
    <view class="analysis-block" data-type="sales" bindtap="onClickToDetail">
        <view class="head-layout">
            <view class="head-title">整体销售</view>
            <view class="head-right"  >
                查看明细
                <van-icon size="16" color="#707070" name="arrow"></van-icon>
            </view>
        </view>
        <view class="analysis-content">
            <view class="analysis-item">
                <view class="analysis-item-num">
                    {{detailInfo.sumYesterdayMoney ? detailInfo.sumYesterdayMoney + '万元' : 0}}
                </view>
                <view class="analysis-item-text">昨日销售额</view>
            </view>
            <view class="analysis-item">
                <view class="analysis-item-num">{{detailInfo.sumYesterdayQty || 0}}</view>
                <view class="analysis-item-text">昨日销量</view>
            </view>
            <view class="analysis-item">
                <view class="analysis-item-num">
                    {{detailInfo.sumYearMoney ? detailInfo.sumYearMoney + '万元' : 0}}
                </view>
                <view class="analysis-item-text">年累计销售额</view>
            </view>
        </view>
    </view>
    <view class="analysis-block">
        <view class="head-layout" data-type="store" bindtap="onClickToDetail">
            <view class="head-title">门店销售</view>
            <view class="head-right" >查看明细<van-icon size="16" color="#707070" name="arrow"></van-icon></view>
        </view>
        <scroll-view class="analysis-scroll" scroll-with-animation="true" scroll-x="true">
            <view class="scroll-item" wx:for="{{storeList}}" wx:key="index">
                <view class="scroll-item-head" data-item="{{item}}" bindtap="onClickToAnalysis">
                    <view class="scroll-item-head-title">{{item.custName}}</view>
                    <van-icon size="16" color="#242424" name="arrow"></van-icon>
                </view>
                <view class="scroll-content" data-item="{{item}}" bindtap="onClickToAnalysis">
                    <view class="analysis-item">
                        <view class="analysis-item-num">{{item.sumYesterdayMoney ? item.sumYesterdayMoney + '万元' : 0}}</view>
                        <view class="analysis-item-text">昨日销售额</view>
                    </view>
                    <view class="analysis-item">
                        <view class="analysis-item-num">{{item.sumYesterdayQty || 0}}</view>
                        <view class="analysis-item-text">昨日销量</view>
                    </view>
                    <view class="analysis-item">
                        <view class="analysis-item-num">{{item.sumYearMoney ? item.sumYearMoney + '万元' : 0}}</view>
                        <view class="analysis-item-text">年累计销售额</view>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</view>