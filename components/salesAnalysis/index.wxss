/* components/salesAnalysis/index.wxss */
.head-layout {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 32rpx;
}
.head-title, .head-right {
    font-size: 28rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 40rpx;
}
.head-right {
    color: #707070;
}
.analysis-head {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 44rpx;
    padding: 26rpx 32rpx;
    border-bottom: 1px solid #E6E6E6;
}
.analysis-content {
    background: #F9F9F9;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 32rpx 16rpx;
    margin: 0 32rpx;
    margin-bottom: 24rpx;
}
.analysis-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
}
.analysis-item-num {
    font-size: 32rpx;
    color: #242424;
    line-height: 32rpx;
}
.analysis-item-text {
    font-size: 24rpx;
    color: #707070;
    line-height: 24rpx;
    margin-top: 16rpx;
}
.analysis-scroll {
    width: 100%;
    white-space: nowrap;
    height: 234rpx;
    overflow: hidden;
    padding-left: 32rpx;
    padding-bottom: 24rpx;
}
.scroll-item {
    width: 600rpx;
    display: inline-block;
    margin-right: 24rpx;
    background-color: #F9F9F9;
    border-radius: 8rpx;
}
.scroll-item-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    border-bottom: 1px solid #E6E6E6;
}
.scroll-item-head-title {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #242424;
    line-height: 32rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.scroll-content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 32rpx;
}