// components/salesAnalysis/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {

  },
  /**
   * 组件的初始数据
   */
  data: {
    detailInfo: {},
    storeList: []
  },
  pageLifetimes: {
    show() {
      this.init()
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    init() {
      this.getAnalysisData()
      this.getStoreList()
    },
    // 点击跳转明细
    onClickToDetail(e) {
      const type = e.currentTarget.dataset.type
      let url = ''
      if(type == 'store') {
        url = '/pages/ccs/analysis/storeSales/index'
      } else if (type == 'sales') {
        wx.setStorageSync('storeInfo', { custId: 0, custName: '整体销售' })
        url = '/pages/ccs/analysis/salesAnalysis/index'
      }
      wx.navigateTo({
        url
      })
    },
    // 跳转销售分析
    onClickToAnalysis(e) {
      const item = e.currentTarget.dataset.item
      wx.setStorageSync('storeInfo', { custId: item.custId, custName: item.custName })
      wx.navigateTo({
        url: '/pages/ccs/analysis/salesAnalysis/index'
      })
    },
    // 获取数据
    getAnalysisData() {
      const url = '/api/psi/salesAnalysis/myx/query/total'
      const supInfo = wx.getStorageSync('supInfo')
      App.getHttp()._post(url, {
        setsOfBooksId: supInfo.setsOfBooksId
      }).then(res => {
        this.setData({
          detailInfo: res
        })
      })
    },
    // 获取门店数据
    getStoreList() {
      const url = '/api/psi/salesAnalysis/myx/query/cust'
      const supInfo = wx.getStorageSync('supInfo')
      App.getHttp()._post(url, {
        setsOfBooksId: supInfo.setsOfBooksId,
        custType: 7
      }).then(res => {
        this.setData({
          storeList: res
        })
      })
    }
  }
})
