// components/listView/index.js
const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  /**
   * 组件的属性列表
   */
  properties: {
    viewHeightPx: {
      type: Number,
      value: 600
    },
    refresherBackground: {
      type: String,
      value: '#00b9c3'
    },
    //是否匹配安全区域
    isSafeArea: {
      type: Boolean,
      value: false
    },
    //是否存在空状态
    isEmpty: {
      type: Boolean,
      value: false
    },
    //空状态文字
    emptyString: {
      type: String,
      value: ''
    },
    //是否开启下拉
    isRefresherEnabled: {
      type: Boolean,
      value: true
    }
  },

  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的初始数据
   */
  data: {
    triggered: false,
    _freshing: false,
    iosIphoneXClass: '',
    calculateViewHeightPx: ''
  },

  /**
   * 数据监听
   */
  watch: {
    viewHeightPx: function () {
      this.hadleCalculateViewHeightPx()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onRefresh() {
      if (this._freshing) return
      this._freshing = true
      this.triggerEvent('pullRefresh')
      setTimeout(() => {
        this.setData({
          triggered: false,
        })
        this._freshing = false
      }, 3000)
    },
    onLoadMore() {
      this.triggerEvent('loadmore')
    },
    hadleCalculateViewHeightPx() {
      if(this.data.viewHeightPx){
        this.setData({
          calculateViewHeightPx: Number(this.data.viewHeightPx) -  (this.data.isSafeArea?app.globalData.deviceBottomOccPx:0)
        })
      }
    }
  },
  attached() {
    this.hadleCalculateViewHeightPx()
  },
})