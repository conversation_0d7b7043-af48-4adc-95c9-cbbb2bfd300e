// components/navBar/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title:{
      type: String,
      value: '进销存'
    },
    imgTxt: {
      type: String,
      value: ''
    },
    imgUrl:{
      type: String,
      value: ''
    },
    info:{
      type: Number|undefined,
      value: undefined
    }

  },

  /**
   * 组件的初始数据
   */
  data: {
    navMarginTop: 24,
    navMarginRight: 100,
    clientRectBottom: 100,
  },
  lifetimes:{
    attached: function () {
      // 获取菜单按钮（右上角胶囊按钮）的布局位置信息。
      const rect = wx.getMenuButtonBoundingClientRect()
      console.log('胶囊', rect);
      this.setData({
        clientRectBottom: (rect.bottom + 24) * App.globalData.pxToRpxRatio,
        navMarginTop: (rect.top) * App.globalData.pxToRpxRatio,
        navMarginRight: (rect.width + 23) * App.globalData.pxToRpxRatio
      })
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClickArrow(){
      wx.navigateBack({
        delta: 1,
      })
    },
    navImgClick(){
      this.triggerEvent('navImgClick')
    }
  }
})
