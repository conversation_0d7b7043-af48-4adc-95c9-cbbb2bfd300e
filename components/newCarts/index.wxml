<!--components/newCarts/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="newsList-item" catchtap="onClickNewsItem">
    <view class="item-dot-box"  wx:if="{{itemInfo.isRead == 1}}">
        <view class="item-dot"></view>
    </view>
    <view wx:if="{{itemInfo.fileRelationUrl}}">
        <image src="{{itemInfo.fileRelationUrl}}"  class="item-img" mode="aspectFit" lazy-load />
    </view>
    <view class="item-right">
        <view class="item-title-box two-line-ellipsis">
            <view class="news-tag tag-sys" wx:if="{{itemInfo.topCategory == '1'}}">通知公告</view>
            <view class="news-tag tag-news" wx:if="{{itemInfo.topCategory == '2'}}">新闻资讯</view>
            <view class="news-tag tag-rank" wx:if="{{itemInfo.topCategory == '3'}}">业绩排名</view>
            <view class="news-tag tag-policy" wx:if="{{itemInfo.topCategory == '4'}}">新品推荐</view>
            <view class="news-tag tag-sales" wx:if="{{itemInfo.topCategory == '5'}}">促销信息</view>
            {{itemInfo.newsTitle}}
        </view>
        <view class="item-date">
            {{itemInfo.publishTime}}
        </view>
    </view>
</view>