// components/addCarts/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    item: {
      type: Object,
      value: {}
    },
    isSup: {
      type: Boolean,
      value: false
    }
  },

  observers: {
    'show': function (value) {
    }
  },
  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onChangeStep(v) {
      this.data.item.qty = v.detail
      this.setData({
        item: this.data.item
      });
    },
    onConfirm() {
      if (this.data.item.qty == undefined || this.data.item.qty == null) this.data.item.qty = 1
      this.triggerEvent('onClose', this.data.item)
    },
    onClose() {
      this.triggerEvent('onClose')
    }
  }
})