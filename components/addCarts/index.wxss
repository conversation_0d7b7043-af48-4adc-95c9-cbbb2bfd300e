/* components/addCarts/index.wxss */
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
/* 垂直 */
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}
.justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.content-layout{
  padding: 112rpx 24rpx 0 24rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}
.content-layout .right{
  position: relative;
  
}
.content-layout .img{
  width: 192rpx;
  height: 192rpx;
  margin-right: 16rpx;
}
.content-layout .itembox{
  height: 80rpx;
}
.content-layout .pkg-layout{
  margin-top: 36rpx;
  margin-bottom: 15rpx;
  height: 88rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.85);
  font-weight: 400;
  background-color: #FFFFFF;

}
.plusclss{
  width: 48rpx !important;
  height: 48rpx !important;
  background-color: #FFFFFF !important;
  font-weight: 600 !important;
}
.inputclss{
  background: rgba(0,0,0,0.04) !important;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85) !important;
  border-radius: 8rpx !important;
}

.good-specs {
  font-size: 24rpx;
  font-weight: 100;
  color: #7f7f7f;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.itemName{
  margin-top: 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  line-height: 40rpx;
  color: rgba(0,0,0,0.85);
  font-weight: 400;
}
.itemPrice{
  /* margin-top: 16rpx; */
  font-family: SanFranciscoText-Semibold;
  font-size: 36rpx;
  color: #F97D4E;
  line-height: 42rpx;
  font-weight: 600;
}
.itemPrice .itemQty{
  /* margin-top: 16rpx;
  margin-left: 24rpx; */
  font-family: SanFranciscoText-Semibold;
  font-size: 24rpx;
  color: #c4ccd3;
  line-height: 42rpx;
  font-weight: 400;
}
.uom{
  margin-right: 4rpx;
  font-family: SanFranciscoText-Medium;
  font-size: 24rpx;
  color: #F97D4E;
  line-height: 44rpx;
  font-weight: 500;
}
.vendor{
  margin-top: 8rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.45);
  line-height: 20px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}