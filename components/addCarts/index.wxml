<!--components/addCarts/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<van-popup
  show="{{ show }}"
  position="bottom"
  bind:close="onClose"
  closeable
  close-icon-position="top-right"
  root-portal
  round
>
<view>
<view class="content-layout">
  <view  class="flex">
    <image wx:if="{{item.itemUrl}}" class="img" src="{{item.itemUrl}}" mode="aspectFit" lazy-load />
    <image wx:else class="img" src="/asset/imgs/bg_shop.png" mode="aspectFit" lazy-load />
    <view class="right flexbox">
      <view class="itemPrice"><text wx:if="{{item.beSell == 2}}" class="uom">促销价 </text><text class="uom">¥</text>{{wxsUtil.moneyFormat(item.applyPrice)}} </view>
      <view  class="itemName itembox two-line-ellipsis">{{item.itemCode}} {{item.itemName}}</view>
      <view class="good-specs">{{item.specs}}</view>
      <view class="itemPrice">
        <text wx:if="{{item.beAccessory !== 2}}" class="itemQty">库存：{{item.stockQty}}</text>
        <text wx:if="{{item.beAccessory !== 2 && item.isBulkOrder === 2}}" class="itemQty">整托数：{{item.itemNumber}}</text>
      </view>
      <!-- <view class="vendor" wx:if="{{!isSup}}">建议零售价：{{item.retailPrice||''}}</view> -->
    </view>
  </view>
  <view class="pkg-layout flex align-items-center justify-content-between">
    <view>数量</view>
    <van-stepper plus-class="plusclss" minus-class="plusclss" input-class="inputclss" value="{{item.qty}}" async-change integer step="1"
      min="1" button-size="24" input-width="48" bind:change="onChangeStep"/>
  </view>
</view>
  <footer-button btnWord="确认" bind:mainClick="onConfirm"></footer-button>
</view>
</van-popup>
