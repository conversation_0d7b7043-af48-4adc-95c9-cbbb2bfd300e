// components/searchHistory/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    busCode:{
      type: String,
      value: ''
    },
    busModule:{
      type: String,
      value: 'psi',
    },
  },

  /**
   * 组件的初始数据
   */
  data: {

  },
  lifetimes:{
    attached(){
    }
  },
  observers: {
    'busCode': function (val) {
      if(val){
        // this.initSearchHis()
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // PURCHASE_SEARCH_ITEM("purchase_search_item", "采购查询商品"),
    // INVENTORY_QUERY("inventory_query", "库存查询"),
    // INVENTORY_CHECK("inventory_check", "库存盘点"),
    // INVENTORY_TRANSFER("inventory_transfer", "库存调拨"),
    // OTHER_OUTBOUND("other_outbound", "其他出库"),
    // OTHER_WAREHOUSING("other_warehousing", "其他入库"),
    initSearchHis() {
      App.getHttp()._post('/api/mms/searchHis/myx/page', {
        pageIndex: 1,
        pageSize: 20,
        param: {
          busModule: this.data.busModule,
          busCode: this.data.busCode,
        }
      }).then(res => {
        this.setData({
          hisList:res,
          showHis:true,
        })
      })
    },
    addSearchHist(keyWord){
      if(!keyWord){
        return
      }
      // 储存搜索历史
      // App.getHttp()._post('/api/mms/searchHis/myx/create', {
      //   keyWord: keyWord,
      //   busModule: 'psi',
      //   busCode: this.data.busCode,
      // })
    },
    onClickHisKeyWord(e){
      this.triggerEvent('onClickHisKeyWord', e.target.dataset.keyword)
    },
    onClearAll(){
      App.getHttp()._post('/api/mms/searchHis/myx/clear', {
        busModule: 'psi',
        busCode: this.data.busCode,
      }).then(res => {
        this.setData({
          hisList:[],
        })
      })
    },
  }
})
