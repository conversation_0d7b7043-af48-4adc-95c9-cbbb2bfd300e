/* components/searchHistory/index.wxss */
.history-layout {
  padding: 0 24rpx;
  /* margin-top: 16rpx; */
}
.history-layout .title {
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
}
.history-layout .clean {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.history-layout .icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}
.history-layout .listbox {
  margin-top: 32rpx;
}
.history-layout .listbox .label {
  height: 64rpx;
  padding: 0 40rpx;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  line-height: 64rpx;
  font-weight: 400;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.flex-box {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.justify-content-between{
  justify-content: space-between;
}
.flex{
  display: flex;
  flex-wrap: wrap;
}