<!--components/warehouseTab/index.wxml-->
<!-- <van-tabs active="{{ active }}" bind:change="chooseTab">
    <van-tab wx:for="{{tabList}}" wx:key="index" title="{{item.name}}" name="{{index}}" data-value="{{index}}">
    </van-tab>
</van-tabs> -->
<view class="tab-box">
    <view wx:for="{{tabList}}" wx:key="index" wx:for-item="item" x:for-key="key" class="tab-item {{ active == index ? 'active' : '' }}" data-index="{{index}}" bindtap="onTabClick">
        <view class="single-line-ellipsis">{{item.name}}</view>
    </view>
    <view class="icon-box" bindtap="onDownClick">
        <van-icon name="/asset/imgs/filter.png" custom-style="height: 40rpx; width: 40rpx;" />
    </view>
</view>
<van-popup show="{{ showPop }}" position="right" custom-style="width: 612rpx;height: 100%;" bind:close="onClose">
    <view class="pop-box">
        <view class="pop-title {{customNav ? 'customNav' : ''}}">筛选</view>
        <view wx:for="{{tabList}}" wx:key="index" wx:for-item="item" wx:for-key="key" class="tab-item-pop {{ active == index ? 'active' : '' }}" data-index="{{index}}"  bindtap="onPopTabClick">
            <view class="single-line-ellipsis">{{item.name}}</view>
        </view>
    </view>
    <view class="btn-box">
        <van-button plain type="default" custom-class="btn-rest" bindtap="onResetClick">重置</van-button>
        <van-button plain type="info"  custom-class="btn-sure" bindtap="onSureClick">确定</van-button>
    </view>
</van-popup>