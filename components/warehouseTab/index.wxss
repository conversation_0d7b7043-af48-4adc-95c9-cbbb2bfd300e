/* components/warehouseTab/index.wxss */
.tab-box{
  position: relative;
  height: 96rpx;
  padding-left: 32rpx;
  overflow: hidden;
  background: #fff;
  white-space: nowrap;
}
.tab-item{
  box-sizing: border-box;
  display: inline-block;
  margin-right: 16rpx;
  margin-top: 20rpx;
  padding: 0 28rpx;
  width: 152rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 56rpx;
  font-size: 24rpx;
  color: #616161;
  background: #F0F0F0;
}
.tab-item-pop{
  box-sizing: border-box;
  display: inline-block;
  margin-right: 16rpx;
  margin-top: 20rpx;
  padding: 0 28rpx;
  width: 172rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 56rpx;
  font-size: 24rpx;
  color: #616161;
  background: #F0F0F0;
}
.active{
  font-size: 24rpx;
  color: #00b9c3;
  background: #E7F2FF;
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.icon-box{
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 4rpx;
  right: 8rpx;
  height: 88rpx;
  width: 108rpx;
  background: #fff;
}
.pop-box{
  box-sizing: border-box;
  padding: 0 0 0 32rpx;
}
.pop-title{
  margin-top: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 32rpx;
  color: #3D3D3D;
}
.btn-box{
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  width: 100%;
  height: 96rpx;
  padding: 0 32rpx;
  bottom: 0;
  left: 0rpx;
  right: -32rpx;
}
.btn-rest{
  font-size: 32rpx !important;
  color: #242424 !important;
  width: 266rpx !important;
  height: 76rpx !important;
  line-height: 76rpx !important;
  text-align: center;
  border-radius: 8rpx !important;
  border: 2rpx solid #DBDBDB !important;
}
.btn-sure{
  font-size: 32rpx !important;
  width: 266rpx !important;
  height: 76rpx !important;
  line-height: 80rpx !important;
  text-align: center !important;
  border-radius: 8rpx !important;
  background: #00b9c3 !important;
  color: #fff !important;
  border: 2rpx solid #DBDBDB !important;
}
.customNav{
  margin-top: 128rpx;
}