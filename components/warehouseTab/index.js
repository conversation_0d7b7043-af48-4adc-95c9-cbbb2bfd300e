// components/warehouseTab/index.js
const App = getApp()
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        customNav: {
            type: Boolean,
            value: false
        },
        item: {
            type: Object,
            value: {}
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        tabList: [],
        active: 0,
        showPop: false,
        active: 0,
    },
    pageLifetimes: {
        show: function () {
            this.getTabList()
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        getTabList() {
            console.log(App)
            const param = {
                "pageIndex": 1,
                "pageSize": 999999,
                "param": {
                    isUsable: 2,
                    state: 2,
                }
            }
            // 查询仓库
            App.getHttp()._post('/api/psi/baseWarehouse/page', param).then(res => {
                this.setData({
                    active: 0,
                    pageIndex: 1,
                    tabList: res
                })
                this.triggerEvent('chooseTab', this.data.tabList[0])
            })
        },
        chooseTab(e) {
            console.log('e.detail.name', e)
            this.setData({
                active: e.detail.name,
            })
            let index = e.detail.name
            console.log('warehouseId666', this.data.tabList[index])
            this.triggerEvent('chooseTab',this.data.tabList[index])
        },
        onDownClick(){
            this.setData({
                showPop: true
            })
        },
        onClose(){
            this.setData({showPop: false})
        },
        onTabClick(e){
            let index = e.currentTarget.dataset.index
            this.setData({active: index})
            this.triggerEvent('chooseTab',this.data.tabList[index])
        },
        onPopTabClick(e){
            let index = e.currentTarget.dataset.index
            this.setData({active: index})
            // this.triggerEvent('chooseTab',this.data.tabList[index])
        },
        onResetClick(){
            this.setData({active: 0})
        },
        onSureClick(){
            this.setData({showPop: false})
            this.triggerEvent('chooseTab',this.data.tabList[this.data.active])
        }
    }
})
