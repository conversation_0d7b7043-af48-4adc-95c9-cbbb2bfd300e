<!--components/form/upload/index.wxml-->
<view class="van-cell">
  <view wx:if="{{config.label}}" class="{{config.required?'van-field__label red-dot':'van-field__label'}}">
    <text>{{config.label}}</text>
  </view>
  <van-uploader max-count="{{config.attrs.max || config.attrs.maxCount || 999}}" accept="{{config.attrs.accept || 'image'}}" capture="{{config.attrs.capture || sourceType}}" file-list="{{fileList}}" image-fit="cover" preview-size="154rpx" multiple bind:after-read="handleAfterRead" preview-full-image='{{false}}' bind:click-preview='previewFile' class="upload-content" upload-icon="{{config.attrs.uploadIcon || 'photograph'}}" bind:delete="checkConfirmDelete" disabled="{{config.attrs.disabled || allDisabled}}" show-upload="{{!config.attrs.disabled && !allDisabled}}" deletable="{{!config.attrs.disabled && !allDisabled}}"/>
  <view class="van-field__error-message" wx:if="{{isErrorMessage}}">
    {{isErrorMessage}}
  </view>
</view>
<image src="{{imgSrc}}" bindload='checkwh' mode='widthFix' hidden/>
<canvas class="image-canvas" canvas-id="imageCanvas" style="width:{{imgW}}rpx;height:{{imgH}}rpx;"></canvas>
