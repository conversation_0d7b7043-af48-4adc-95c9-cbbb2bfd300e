// components/form/upload/index.js
const dayjs = require('dayjs')
const App = getApp()
import { DOMAIN } from '../../utils/server'
const baseUrl = DOMAIN

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    config:{
      type: Object,
      value: {
        key: 'fileIds',
        attrs: {max: 3,}
      }
    },
    initFileIds:{
      type: String,
      value: ''
    },
    allDisabled:{
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    sourceType: ['album', 'camera'],
    fileList: [],
    fileIds: '',
    canvasWidth: 0,
    canvasHeight: 0,
    imgSrc: '', // 需要处理图片地址
    byclear: 1, // 比例，这里将iphon6- 375像素设置为1标准，以便在自适应上的转换
    realWidth: '',
    realHeight: '',
    imgW: '',
    imgH: '',
  },

  /**
   * 在组件实例进入页面节点树时执行
   */
  attached() {
    var that = this
    // 根据屏幕的宽度计算标准比例值。这里讲375作为标准值
    wx.getSystemInfo({
      success: function (res) {
        let byclear = res.screenWidth / 375
        that.setData({
          byclear
        })
      },
    })
  },


  /**
   * 数据监听
   */
  observers: {
    formData: function (data) {
      if (this.data.fileIds === '' && data[this.data.config.key]) {
        const value = data[this.data.config.key]
        this.setData({
          fileIds: value
        })
        this.getFileList(value)
      }
    },
    initFileIds:function (data) {
      if (this.data.fileIds === '' && data) {
        const value = data
        this.setData({
          fileIds: value
        })
        this.getFileList(value)
      }
    },
  },

  /**
   * 计算属性
   */
  computed: {
    inputPlaceholder(data) {
      return data?.config?.attrs?.placeholder != undefined ? data.config.attrs.placeholder : `请上传${data.config.label}`
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //上传图片之前
    async handleAfterRead(event) {
      const files = event.detail.file
      if (this.data?.config?.attrs?.isWatermark) {
        this.watemark(files, [], 0, (list) => this.handleUpLoadFile(list))
      } else {
        this.handleUpLoadFile(files)
      }
    },
    //上传图片
    handleUpLoadFile(files) {
      const self = this
      if (Array.isArray(files) && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          let url = ''
          let isImage = false
          let isVideo = false
          if (typeof (file) === 'object' && file.type === 'image') {
            isImage = true
          }
          if (typeof (file) === 'object' && file.type === 'video') {
            isVideo = true
          }
          //如果是对象就可能存在别的文件，如果是地址则是水印图片
          if (typeof (file) === 'object') {
            url = file.thumb || file.url
          } else {
            url = file
            isImage = true
          }
          //上传附件
          wx.uploadFile({
            filePath: url,
            name: 'file',
            url: this.getUrl('/api/base/file/upload'),
            header: {
              'Sets_of_books_id': wx.getStorageSync('checkSetsOfBooksId'),
              'Authorization': wx.getStorageSync('authorization'),
              'Content-Type': 'multipart/form-data'
            },
            formData: {
              fileReqDTO: {}
            },
            success(res) {
              const list = [...self.data.fileList]
              const { content } = JSON.parse(res.data)
              list.push({
                url: content.fileUrl,
                isImage,
                isVideo,
                fileId: content.id,
                fileType: content.fileType
              })

              let fileIds = self.data.fileIds
              if (fileIds) {
                fileIds = `${fileIds},${content.id}`
              } else {
                fileIds = `${content.id}`
              }
              //把地址放到组件里面
              self.setData({
                fileList: list,
                fileIds
              }, function () {
                //传值id回表单
                self.triggerEvent('change', {
                  key: self.data.config?.key,
                  fileIds,
                })
              })
            }
          })
        }
      }
    },
    getUrl(url){
      if (url.indexOf('://') == -1) {
        // 临时加个, 设置环境有限使用人工设置环境
        const tempBaseUrl = wx.getStorageSync('host')||baseUrl
        url = tempBaseUrl + url;
      }
      return url
    },
    //获取回显图片
    async getFileList(value) {
      // const ars = value.split(',')
      const list = []
      const res = await App.getHttp()._get(`/api/base/fileAttach/listByIds?ids=${value}`)

      if (Array.isArray(res) && res.length) {
        const imgType = ['jpg', 'bmp', 'jpeg', 'png', 'tiff']
        const videoType = ['avi', 'mp4', '3gp', 'wmv']
        res.forEach(item => {
          let isImage = imgType.includes(item.fileType)
          const isVideo = videoType.includes(item.fileType)
          list.push({
            url: item.fileUrl,
            isImage,
            isVideo: isVideo,
            fileId: item.id,
            fileType: item.fileType
          })
        })
        this.setData({
          fileList: list
        })
      }

    },
    //检查是否需要确认
    checkConfirmDelete(event) {
      if (this.data?.config?.deleteConfirm) {
        wx.showModal({
          title: '',
          content: '确认是否删除该图片？',
          success: () => {
            this.handleListDelete(event)
          }
        })
      } else {
        this.handleListDelete(event)
      }
    },
    //图片删除
    handleListDelete(event) {
      const list = [...this.data.fileList]
      const id = event.detail.file.fileId
      const index = event.detail.index
      const fileIds = this.data.fileIds.split(',')
      const idIndex = fileIds.findIndex(item => item == id)
      list.splice(index, 1)
      fileIds.splice(idIndex, 1)
      this.setData({
        fileList: list,
        fileIds: fileIds.join(',')
      }, function () {
        //传值id回表单
        this.triggerEvent('change', {
          key: this.data.config?.key,
          fileIds: fileIds.join(','),
        })
      })
    },
    //加水印
    watemark(ars, newArs, index, cb) {
      const self = this;
      const user = wx.getStorageSync('userInfo')
      const file = ars[index].thumb
      self.setData({
        imgSrc: file
      })
      const arsLength = ars.length
      setTimeout(() => {
        let width = self.data.realWidth
        let height = self.data.realHeight

        let vhsrc = height / width
        let byclear = self.data.byclear
        wx.nextTick(() => {
          setTimeout(() => {
            const query = self.createSelectorQuery().in(self)
            query.select(".image-canvas")
              .context(function (canvas) {
                const ctx = canvas.context
                // self.setData({
                //   canvasWidth: canvasWidth,
                //   canvasHeight: canvasHeight,
                // })
                wx.nextTick(() => {
                  if (width > 375 * byclear) ctx.scale(375 * byclear / width, 375 * byclear / width);
                  ctx.drawImage(file, 0, 0, width, height)
                  ctx.translate(0, 0)
                  ctx.font = 'normal bold 40px sans-serif'
                  ctx.setFontSize(40)
                  ctx.setFillStyle('white')
                  if (self.data.config?.attrs?.watermarkData) { //自定义打印参数
                    ctx.fillText(`${self.data.config.attrs.watermarkDataTitle || ''}：${self.data.config.attrs.watermarkData}`, 20, height - 390)
                  }
                  ctx.fillText(`拍摄时间：${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, 20, height - 310)
                  ctx.fillText(`姓名：${user.name}`, 20, height - 230)
                  ctx.fillText(`账号：${user.loginId}`, 20, height - 150)
                  App.getLocation(() => {
                    ctx.fillText(`当前地址：${App.globalData.locationData.signInAddr}`, 20, height - 70)
                  })
                  ctx.draw(false, () => {
                    wx.canvasToTempFilePath({
                      canvasId: 'imageCanvas',
                      fileType: 'jpg',
                      quality: 0.3,
                      success: (res) => {
                        newArs.push(res.tempFilePath)
                        //清空画布
                        ctx.draw(false, () => {
                          //判断是否需要继续绘制水印
                          if (index + 1 < arsLength) {
                            //wx.nextTick 确保清空之后再画图，如果还是出现问题的话，可以改为延时400毫秒执行
                            wx.nextTick(() => {
                              self.watemark(ars, newArs, index + 1, cb)
                            })
                          } else {
                            //把已经画好水印的图片传给回调
                            cb(newArs)
                          }
                        })
                      },
                      fail: (error) => {
                        self.watemark(ars, newArs, index, cb)
                      }
                    }, self)
                  })
                })
              })
              .exec();
          }, 1000)
        })
      }, 500)
      // wx.getImageInfo({
      //   src: file,
      //   success: (res) => {

      //   },
      //   fail:(error) => {
      //     console.log(error,'拍照失败')
      //   }
      // })
    },
    previewFile(event) { //预览文件
      console.log(event)
      const fileType = event.detail.fileType
      wx.downloadFile({
        url: event.detail.url,
        success: function (res) {
          const filePath = res.tempFilePath
          if (['jpg', 'png', 'jpeg'].includes(fileType)) {
            wx.previewImage({
              current: filePath,
              urls: [filePath]
            })
          } else {
            console.log(fileType, 'opopo')
            wx.openDocument({
              fileType: fileType,
              filePath: filePath,
              success: function (res) {
                console.log("打开文档成功")
                console.log(res);
              },
              fail: function (res) {
                console.log("fail");
                console.log(res)
              }
            })
          }
        },
        fail: function () {
          wx.showToast({
            title: '打开失败',
            icon: "fail"
          })
        },
        complete: function () {
          wx.hideLoading()
        }
      })
    },
    checkwh(e) {

      // 实际宽度 e.detail.width 高度 e.detail.height
      let whsrc = e.detail.height / e.detail.width
      let byclear = this.data.byclear
      this.setData({
        realWidth: e.detail.width,
        realHeight: e.detail.height,
        imgW: e.detail.width > 375 ? 750 : e.detail.width * 2 / byclear,
        imgH: e.detail.width > 375 ? 750 * whsrc : e.detail.height * 2 / byclear
      })
    },

  }
})

