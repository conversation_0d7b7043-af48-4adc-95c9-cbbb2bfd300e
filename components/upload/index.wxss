/* components/form/upload/index.wxss */
.van-field__label{
  color:var(--field-label-color,#646566);
  margin-bottom: 16rpx;
}
.van-cell{
  background-color: var(--cell-background-color,#fff);
  box-sizing: border-box;
  color: var(--cell-text-color,#323233);
  display: flex;
  font-size: var(--cell-font-size,14px);
  line-height: var(--cell-line-height,24px);
  padding: var(--cell-vertical-padding,10px) var(--cell-horizontal-padding,16px);
  position: relative;
  width: 100%;
  flex-direction: column;
}
.upload-content{
  flex:1;
}
.red-dot::before{
  position: absolute;
  content: "*";
  left: 8px;
  left: var(--padding-xs,8px);
  font-size: 14px;
  font-size: var(--cell-font-size,14px);
  color: #ee0a24;
  color: var(--cell-required-color,#ee0a24);
}

.van-field__error-message{
  color: var(--field-error-message-color,#ee0a24);
  font-size: var(--field-error-message-text-font-size,12px);
  text-align: left;
}

.image-canvas {
  position: fixed;
  z-index: -1;
  opacity: 0;
  left: 750rpx;
  top: 0px;
  flex-shrink: 0;
}