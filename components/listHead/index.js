// components/listHead/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isShowType: {
      type: Boolean,
      value: true
    },
    showSupplier: {
      type: Boolean,
      value: true
    },
    customTitle: {
      type: String,
      value: ''
    },
    isSup: {
      type: Boolean,
      value: false
    },
    showArrow:{
      type: Boolean,
      value: false
    },
    showSupplierText:{
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    supplierOptions: [], // 供应商数据源 
    supplier: {}, // 当前供应商
    clientRectTop: 24,
    custInfo: {}
  },
  lifetimes:{
    attached() {
      const rect = wx.getMenuButtonBoundingClientRect()
      this.setData({
        clientRectTop: (rect.top) * App.globalData.pxToRpxRatio,
      })
    },
  },
  pageLifetimes: {
    show(){
      this.getsupplierList() // 加载供应商
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onClickSelectSup() {
      if(!this.properties.showSupplier) {
        return false
      }
      wx.navigateTo({
        url: '/pages/ccs/down/purchaseNew/searchSup/index'
      })
    },
    getsupplierList() {
      const custInfo = wx.getStorageSync('custInfo')
      // 向下的供应商
      let bsRelates = custInfo.bsRelates || []
      // 写入purchaseType字段判断供应商类型 向下/向上
      bsRelates.forEach(item => {
        item.purchaseType = 'down'
      })
      // 向上的供应商
      let supRelates = custInfo.supRelates || []
      supRelates.forEach(item => {
        item.purchaseType = 'sup'
      })
      // 两个类型供应商组合的数据
      let res = []
      // 做优先排序 当前账套为什么类型，什么类型的供应商就排前面
      if(custInfo.custType === 5) {
        res.push(...supRelates)
        res.push(...bsRelates)
      } else {
        res.push(...bsRelates)
        res.push(...supRelates)
      }
      const cacheSupplier = wx.getStorageSync('vendorInfo')
      if (res && res.length > 0) {
        let supplier = {}
        let supplierOptions = [...res]
        if(cacheSupplier) {
          let isHasCache = supplierOptions.find(item => item.bvendorId === cacheSupplier.bvendorId)
          if(isHasCache) {
            supplier = isHasCache
          } else {
            supplier = supplierOptions[0]
            wx.setStorageSync('vendorInfo', supplierOptions[0])
          }
        } else {
          supplier = supplierOptions[0]
          wx.setStorageSync('vendorInfo', supplierOptions[0])
        }
        console.log('supplier', supplier)
        this.setData({
          supplier: supplier,
          custInfo: custInfo ? custInfo : supplierOptions[0] // 默认是第一个供应商,
        })
      } else {
        // 未获取到供应商
        this.setData({
          supplier: {}
        })
      }
    },
    onClickBack() {
      wx.navigateBack({
        delta: 1
      })
    },
    getCartsCount() {
      this.selectComponent('#shopCar').getCartsCount()
    },
  }
})
