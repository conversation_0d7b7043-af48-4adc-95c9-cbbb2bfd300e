<!-- 头部导航 -->
<view class="search-layout flex-box align-center" style="padding-top: {{clientRectTop}}rpx;">
    <view class="head-left flex-box align-center" bindtap="onClickSelectSup">
        <block wx:if="{{showSupplier}}">
            <!-- <view class="supplierType supplierType-{{ custInfo.custType }}" wx:if="{{isShowType}}">
                {{custInfo.custType == 5 ? '厂商采购' : '经销采购'}}
            </view> -->
            <view class="supplierType" wx:if="{{isShowType}}">
                {{supplier.purchaseType == 'sup' ? '经销渠道 |' : ''}}
            </view>
            <van-icon  size="20" class="backArrow" name="arrow-left" catchtap="onClickBack" wx:else />
            <view class="supplierName">
                <view class="supplierName-text">{{supplier.bvendorName}}</view>
            </view>
        </block>
        <block wx:elif="{{showSupplierText}}">
            <van-icon custom-style="back-arrow" size="20" class="backArrow" name="arrow-left" catchtap="onClickBack" />
            <view class="channelName-box single-line-ellipsis">
                {{supplier.channelName}}<text wx:if="{{supplier.channelName}}">|</text>{{supplier.bvendorName}}
            </view>
        </block>
        <block wx:else>
            <van-icon custom-style="back-arrow" size="20" class="backArrow" name="arrow-left" catchtap="onClickBack" />
            <view>{{customTitle}}</view>
        </block>
    </view>
    <goods-cart class="shopCar" id="shopCar" />
</view>