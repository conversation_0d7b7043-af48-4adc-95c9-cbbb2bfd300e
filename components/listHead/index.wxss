.search-layout {
  background-color: #ffffff;
  padding: 0 0 12rpx 24rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  width: 100%;
}
.backArrow {
    padding: 3rpx 30rpx 3rpx 0
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}
.justify-content-between {
  justify-content: space-between;
}
.shopCar {
  margin-left: 0rpx;
}
.head-left {
    width: 55%;
}
.supplierType {
  line-height: 44rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  border-radius: 8rpx;
  margin-right: 10rpx;
}
.supplierType-1 {
  color: #faae16;
  border-color: #faae16;
  border: 1px solid #faae16;
}
.supplierType-2 {
  color: #52c718;
  border-color: #52c718;
  border: 1px solid #52c718;
}
.supplierName {
  width: 264rpx;
  position: relative;
}
.supplierName-text {
  width: 240rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 44rpx;
}
.supplierName::after {
  content: "";
  position: absolute;
  top: 18rpx;
  right: 0rpx;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 12rpx solid #8a8a8a;
  transform: rotate(180deg);
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: nowrap;
}
