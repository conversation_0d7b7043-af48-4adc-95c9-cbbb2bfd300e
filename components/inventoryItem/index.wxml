<!--components/inventoryItem/index.wxml-->
<view class="list-item">
    <view class="list-item-margin">
        <view class="list-item-left">
            <van-image width="160rpx" height="160rpx" fit="contain" src="{{item.itemUrl}}" />
        </view>
        <view class="list-item-right">
            <view class="title two-line-ellipsis">{{item.itemName || item.name}}</view>
            <view class="specs single-line-ellipsis">{{item.specs}}</view>
            <view class="item-warehouseName">{{item.warehouseName}}</view>
            <view class="num-btn-box {{item.warehouseName ? 'num-btn-box-warehouseName' : ''}}">
                <view class="item-num-box" wx:if="{{item.qtyAvi}}">
                    <view class="item-num">{{item.qtyAvi}}</view>
                    <view class="num-txt">可用库存</view>
                </view>
                <view class="item-num-box" wx:elif="{{item.totalQty}}">
                    <view class="item-num">{{item.totalQty}}</view>
                    <view class="num-txt">总库存</view>
                </view>
                <view class="item-num-box" wx:if="{{item.code}}">
                    <view class="num-txt">{{item.code}}</view>
                    <!-- <view class="num-txt">物料编码</view> -->
                </view>
                <view class="item-btn">
                    <van-button plain round type="info"  bindtap="itemBtnClick" custom-class="btn-class">{{(type == "invList" || type=='invCount') ? "盘点" : "加入调整台"}}</van-button>
                </view>
            </view>
        </view>
    </view>
    <view class="stepper-list-box">
        <view wx:for="{{item.warehouses}}" wx:key="index" wx:for-item="item1" class="stepper-box" data-value="{{index}}">
            <view class="stepper-item-left">
                <view>{{item1.warehouseName}}</view>
                <!-- <view>可用库存：{{item.qtyAvi}}</view> -->
            </view>
            <view class="stepper-item-right">
                <text class="item-right-txt">可用库存：</text>{{item1.qtyAvi}}
            </view>
        </view>
    </view>
</view>