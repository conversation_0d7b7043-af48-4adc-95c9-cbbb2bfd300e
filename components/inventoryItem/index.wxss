/* components/inventoryItem/index.wxss */
.list-item{
    width: 100%;
    overflow: hidden;
    background: #fff;
}
.list-item-margin{
    display: flex;
    margin: 24rpx 24rpx 0;
    /* height: 248rpx; */
}
.list-item-left{
    margin-top: 24rpx;
    margin-left: 24rpx;
}
.list-item-right{
    margin-left: 32rpx;
    flex: 1;
}
.title{
    font-size: 28rpx;
    line-height: 36rpx;
    max-height: 72rpx;
    margin-top: 40rpx;
    color: #242424;
}
.specs{
  font-size: 28rpx;
  line-height: 36rpx;
  max-height: 36rpx;
  margin-top: 12rpx;
  color: #8A8A8A;
  line-height: 36rpx;
  height: 36rpx;
  color: #8A8A8A;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.two-line-ellipsis {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* qutoprefixer: off */
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap:break-word;
    word-break: break-all;
    white-space: normal;
}
.item-warehouseName{
    margin-top: 12rpx;
    font-size: 24rpx;
    color: #707070;
}
.item-num-box{
    height: 64rpx;
    line-height: 64rpx;
}
.item-num{
    display: inline-block;
    font-size: 32rpx;
    color: #242424;
    font-weight: 500;
}
.num-txt{
    display: inline-block;
    margin-left: 8rpx;
    font-size: 24rpx;
    color: #707070;
}
.num-btn-box{
    margin-top: 48rpx;
    flex: 1;
    justify-content: space-between;
    display: flex;
}
.num-btn-box-warehouseName{
    margin-top: 24rpx;
}
.item-btn{
    margin-right: 24rpx;
}
.btn-class{
    padding:  0 32rpx !important;
    height: 64rpx !important;
    line-height: 64rpx !important;  
    color: #242424 !important;
    border: 2rpx solid #DBDBDB !important;
    border-radius: 8rpx !important;
}
.stepper-list-box{
    margin-top: 24rpx;
    background: #fff;
}
.stepper-box{
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 64rpx;
    line-height: 64rpx;
    background: #fff;
}
.stepper-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 32rpx;
    width: 100%;
    height: 100%;
    border-bottom: 2rpx solid #E6E6E6;
    box-sizing: border-box;
}
.stepper-item-left{
    margin-left: 48rpx;
    font-size: 28rpx;
    color: #242424;
}
.stepper-item-right{
    margin-right: 48rpx;
    font-size: 28rpx;
    color: #242424;
}
.item-right-txt{
    color: #707070;
}
