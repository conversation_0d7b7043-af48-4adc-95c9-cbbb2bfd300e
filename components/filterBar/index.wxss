/* components/filterBar/index.wxss */
.flex_vcenter {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
}
.flex_center {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
}
.flex_1 {
  flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
}
.flex_2 {
  flex: 2;
  -webkit-flex: 2;
  -ms-flex: 2;
}
.filter-item {
  height: 44rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  color: rgba(0,0,0,0.75);
  line-height: 44rpx;
}
.filter-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.popup-layout{
  width: 640rpx;
  /* height: 80vh; */
  background-color: #FFFFFF;
  padding: 0 32rpx;
  box-sizing: border-box;
  height: 100vh;
  overflow: auto;
}
.popup-layout .vendor-box-active{
  margin-top: 32rpx;
  margin-left: 32rpx;
  padding: 12rpx;
  background: var(--ThemeColor);
  border: 2rpx solid var(--ThemeColor);
  border-radius: 12rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #FFFFFF;
  text-align: left;
  line-height: 48rpx;
  font-weight: 400;
}
.popup-layout .vendor-box{
  margin-top: 32rpx;
  margin-left: 32rpx;
  padding: 12rpx;
  background: rgba(0,0,0,0.04);
  border: 2rpx solid rgba(0,0,0,0.08);
  border-radius: 12rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.45);
  text-align: left;
  line-height: 48rpx;
  font-weight: 400;
}
.popup-item-title{
  margin-bottom: 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 400;
}
.price-interval {
  line-height: 70rpx;
  margin-bottom: 32rpx;
}
.price-interval .price-input {
  vertical-align: top;
  text-align: center;
  display: inline-block;
  width: 160rpx;
  padding: 0 20rpx;
  margin: 0 20rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background-color: #f7f8fa;
}
.txt-back{
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: rgba(0,0,0,0.85);
  text-align: center;
  line-height: 48rpx;
  font-weight: 500;
  text-align: center;
  margin-left: 32rpx;
}
.img-back{
  width: 48rpx;
  height: 48rpx;
}

.btn-submit {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 18rpx 24rpx 18rpx;
  background-color: #fff;
  box-shadow: 0 -4rpx 12rpx 0 rgba(45, 45, 75, 0.06);
}

.btn-submit .btn-reset {
  font-family: PingFangSC-Regular;
  height: 78rpx;
  border: 1rpx solid rgba(0,0,0,0.15);
  border-radius: 12rpx;
  font-size: 32rpx;
  color: rgba(0,0,0,0.45);
  text-align: center;
  line-height: 80rpx;
}
.btn-submit .btn-confirm {
  font-family: PingFangSC-Regular;
  margin-left: 24rpx;
  height: 78rpx;
  border: 1rpx solid D0000D;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
  background: var(--ThemeColor);
}
.vendor {
  margin-bottom: 32rpx;
}
.vendor view,.popup-layout view text{
  font-size: 26rpx;
  color: rgba(0,0,0,0.45);
  background: rgba(0,0,0,0.04);
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.popup-layout .item-box text{
  min-width: 31.5%;
  display: inline-block;
  /* margin-right: 2.1%; */
  text-align: center;
  height: 60rpx;
  box-sizing: border-box;
  margin-right: 1.7%;
}
.popup-layout .selected{
  background-color: #fff !important;
  color: var(--ThemeColor) !important;
  border: 2rpx solid var(--ThemeColor) !important;
}
/* .popup-layout .item-box text:nth-child(3n){
  margin-right: 0;
  
} */
.item-box {
  margin-bottom: 32rpx;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: flex-start;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.van-dropdown-item__option--active {
  background: rgba(70,119,235,0.05);
}
.m-b-120 {
  margin-bottom: 120rpx;
}