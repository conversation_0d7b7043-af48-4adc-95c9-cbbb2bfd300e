// components/filterBar/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    salesFilters:{
      type:Array,
      value:[{ text: '默认销量', value: 0 },
      { text: '销量高~低', value: 1 },
      { text: '销量低~高', value: 2 }]
    },
    priceFilters:{
      type:Array,
      value:[{ text: '默认价格', value: 0 },
      { text: '价格高~低', value: 1 },
      { text: '价格低~高', value: 2 }]
    },
    tempSpecies:{
      type:Array,
      value:[]
    },
    clientRectBottom:{
      type:Number,
      value:66
    },
    clientRectTop:{
      type:Number,
      value:24
    },
    clientRectHeight:{
      type:Number,
      value:32
    },
    
  },

  /**
   * 组件的初始数据
   */
  data: {
    itemSpecie:[],
    orderBySales: 0,
    orderByPrice: 0,
    showPopup:false,
    showSubPopup:false,
    classFilters:[],
    vendorActiveIndex:-1,
    vendorDes:'全部',
    lowerPrice:'',
    maxPrice:'',
    titleName:'产品分类',
    priceStart: undefined,
    priceEnd: undefined,
    supStyleList:[],
    spuSceneList:[],
    supStyle:'',
    spuScene:'',
  },
  observers:{
    "tempSpecies": function (value) {
      this.setData({
        itemSpecie: value
      })
    }
  },
  async attached(){
    let supStyleList = wx.getStorageSync('dictMap').spuStyle || []
    supStyleList = supStyleList.filter(item => {
      return item.usable == 2
    })
    let spuSceneList = wx.getStorageSync('dictMap').spuScene || []
    spuSceneList = spuSceneList.filter(item => {
      return item.usable == 2
    })
    this.setData({
      supStyleList: supStyleList,
      spuSceneList: spuSceneList
    })
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //获取产品分类
    async getClassificationList() {
      const res = await App.getHttp()._post('/myx/ccs-mobile-web/brandClass/queryBrandClassList', {
      })
      
     return res
      
    },
    handleFilter(){
      this.setData({
        showPopup:!this.data.showPopup
      })
    },
    onClose(){
      this.setData({
        showPopup:false,
        showSubPopup:false
      })
    },
    onCloseSub(){
      this.setData({
        showSubPopup:false
      })
    },
    clickSubPopup(){
      this.setData({
        showSubPopup:!this.data.showSubPopup
      })
    },
    selSupScene(e){
      this.setData({
        spuScene:e.currentTarget.dataset.id
      })
    },
    startChange(event) {
      let value = event.detail.value || undefined
      this.setData({
        priceStart: value
      })
    },
    endChange(event) {
      let value = event.detail.value || undefined
      this.setData({
        priceEnd: value
      })
    },
    selSupStyle(e){
      this.setData({
        supStyle:e.currentTarget.dataset.id
      })
    },
    resetPop(){
      this.setData({
        spuScene:'',
        supStyle:''
      })
    },
    confirmPop(){
      this.doTriggerEvent()
      this.onClose()
    },
    salesChange(v){
      this.setData({
        orderBySales:v.detail
      })
      this.doTriggerEvent()
    },
    priceChange(v){
      this.setData({
        orderByPrice:v.detail
      })
      this.doTriggerEvent()
    },
    classClose(v){
      this.classChange(v)
      this.selectComponent('#class-dropdown').toggle(false);
    },
    classChange(v){
      if (this.data.itemSpecie.join('-')  != v.detail.value.join('-')){
        this.setData({
          itemSpecie:v.detail.value,
          titleName:v.detail.titleName,
        })
        this.doTriggerEvent()
      }
    },
    getTitleName(v){
      this.setData({
        titleName:v.detail.titleName
      })
    },
    onCloseClassSelect(v){
      this.selectComponent('#classSelect').onclose();
    },
   
    doTriggerEvent(){
      let event = {
        orderBySales:this.data.orderBySales || undefined,
        orderByPrice:this.data.orderByPrice || undefined,
        lowerPrice: this.data.priceEnd > this.data.priceStart ? this.data.priceStart : this.data.priceEnd,
        maxPrice: this.data.priceEnd > this.data.priceStart ? this.data.priceEnd : this.data.priceStart,
      }
      event.tempSpecies = this.data.itemSpecie
      // if(this.data.spuScene){ // 场景
      //   event.spuScene=this.data.spuScene
      // }
      // if(this.data.supStyle){ // 风格
      //   event.supStyle=this.data.supStyle
      // }
      this.triggerEvent('filterChange',event)
    }
  }
})
