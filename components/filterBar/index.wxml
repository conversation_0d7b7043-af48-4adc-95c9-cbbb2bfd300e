<!--components/filterBar/index.wxml-->
<view class="flex_center">
	<!-- 头部功能栏 -->
	<view class="flex_2">
		<!-- 品类 销量优先 -->
		<van-dropdown-menu active-color="#00b9c3">
			<van-dropdown-item title="{{ titleName }}" id="class-dropdown" bind:closed="onCloseClassSelect">
				<classSelect
				 bind:titleName="getTitleName"
				 bind:close="classClose"
				 bind:change="classChange"
				 id="classSelect"
				 classFilters="{{itemSpecie}}"
				/>

			</van-dropdown-item>
			<van-dropdown-item value="{{orderBySales}}" options="{{salesFilters}}" bind:change="salesChange" />
			<van-dropdown-item value="{{orderByPrice}}" options="{{priceFilters}}" bind:change="priceChange" />
		</van-dropdown-menu>
	</view>
	<!-- 筛选 -->
	<view class="flex_1 filter-item flex_center" bindtap="handleFilter">
		<view class="filter-info">筛选</view>
		<image class="filter-icon" src="/asset/svgs/filter.svg" />
	</view>
</view>
<!-- 第一层侧拉抽屉 -->
<van-popup
 show="{{showPopup}}"
 position="right"
 custom-style="height:100%"
 close-on-click-overlay="true"
 safe-area-inset-top="true"
 bind:close="onClose"
>
	<view class="popup-layout" style="padding-top:{{clientRectBottom}}px;">
		<view class="popup-item-title">价格区间（元）</view>
		<view class="price-interval">
			<input placeholder="最低价" type="number" bindchange="startChange" class="price-input price-start" value="{{priceStart}}" type="text"/> ~ <input placeholder="最高价" type="number" bindchange="endChange" value="{{priceEnd}}" class="price-input price-end" type="text"/>
		</view>
		<!-- <view class="popup-item-title">风格</view>
		<view class="item-box">
			<text wx:for="{{supStyleList}}" wx:key="itemValue" data-id="{{item.itemValue}}" bindtap = "selSupStyle" class="{{item.itemValue ==  supStyle ? 'selected' :''}}">{{item.itemName}}</text>
		</view>
		<view class="popup-item-title">适用场景</view>
		<view class="item-box m-b-120">
			<text wx:for="{{spuSceneList}}"  wx:key="itemValue" data-id="{{item.itemValue}}" bindtap = "selSupScene" class="{{item.itemValue ==  spuScene ? 'selected' :''}}">{{item.itemName}}</text>
		</view> -->
		
    <view class="btn-submit flex_center">
      <view class="btn-reset flex_1" catchtap="resetPop">重置</view>
      <view class="btn-confirm flex_1" catchtap="confirmPop">确定</view>
    </view>
	</view>
</van-popup>

