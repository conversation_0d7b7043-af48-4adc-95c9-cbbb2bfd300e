// components/search/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    disabled:{
      type:Boolean,
      value:true,
    },
    showSearchImg:{
      type:Boolean,
      value:true,
    },
    showScanImg:{
      type:Boolean,
      value:false,
    },
    showMsgImg:{
      type:Boolean,
      value:true
    },
    showSearchTxt:{
      type:Boolean,
      value:false
    },
    placeholder:{
      type:String,
      value:'请输入关键词'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    unReadFlag:false,
    searchValue: ''
  },
  pageLifetimes:{
    show:function(){
      this.getUnreadMsgCount()
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getUnreadMsgCount(){
      if (!this.data.showMsgImg) {
        return
      }
      const param = {
        custCode:wx.getStorageSync('mastCode'),
        readStatus:1
      }
      App.getHttp()._post('myx/ccs-mobile-web/msg/notice/getUnreadMessageNum', param).then(res => {
        this.setData({
          unReadFlag:res&&res.count>0
        })
      })
    },
    onConfirmListener(e){
      this.triggerEvent('onConfirm',String(e.detail.value))
    },
    onInput(e){
      this.setData({searchValue: e.detail.value})
    },
    onSearchTxtClick(e){
      this.triggerEvent('onSearchTxtClick',this.data.searchValue)
    },
    onOpenMsgListener(e) {
      this.triggerEvent('clickMsg')
    },
    onClickSearchListener(e) {
      this.triggerEvent('onClickSearch')
    },
    onClickScan(e){
      this.triggerEvent('onClickScan')
    },
    onCancel(e){
      this.setData({
        searchValue: ''
      })
    }
  }
})
