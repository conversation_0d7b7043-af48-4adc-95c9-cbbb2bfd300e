/* components/search/index.wxss */
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.search-layout{
  box-sizing: border-box;
  height: 104rpx;
  background: #fff;
}
.search-inner-layout{
  margin: 16rpx 24rpx 16rpx 32rpx;
  height: 72rpx;
  background: #F0F0F0;
  border-radius: 8rpx;
  border-radius: 8rpx;
  padding: 0 0;
}
.search-inner-scan{
  width: 56rpx;
  height: 56rpx;
  padding-left: 12rpx;
}
.search-inner-msg{
  width: 48rpx;
  height: 48rpx;
  position: relative;
}
.search-inner-img{
  margin-left: 24rpx;
  margin-right: 8rpx;
  width: 40rpx;
  height: 40rpx;
}
.search-error-img{
  width: 32rpx;
  height: 32rpx;
  margin: 0 24rpx;
}
.search-inner-dot{
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #00b9c3;
  position: absolute;
  right: 0;
  top: 0;
}
.search-inner-close{
  width: 40rpx;
  height: 40rpx;
  margin-left: 12rpx;
  margin-right: 24rpx;
}
.search-inner-input{
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 72rpx;
  margin-left: 8rpx;
}
.search-inner-input-holder{
  font-size: 32rpx;
  color: #8A8A8A;
}
.search-txt{
  margin-right: 26rpx;
  font-size: 32rpx;
  color: #00b9c3;
  line-height: 104rpx;
}