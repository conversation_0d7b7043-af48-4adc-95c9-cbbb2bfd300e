<!--components/search/index.wxml-->
<view class="search-layout  flex align-items-center">
  <view class="search-inner-layout flexbox flex align-items-center" bindtap="onClickSearchListener">
    <image class="search-inner-scan" src="/asset/imgs/scan.png" wx:if="{{showScanImg}}"  catchtap="onClickScan"/>
    <image class="search-inner-img" src="/asset/imgs/search.png" wx:if="{{showSearchImg}}"></image>
    <input  class="flexbox search-inner-input" placeholder-class="search-inner-input-holder" confirm-type='search' bindinput="onInput" value="{{searchValue}}"
      placeholder="{{placeholder}}" disabled="{{disabled}}" bindconfirm="onConfirmListener"></input>
    <image class="search-error-img" src="/asset/imgs/error.png" wx:if="{{showSearchImg && searchValue.length}}" bindtap="onCancel"></image>
  </view>
  <view  wx:if="{{showMsgImg}}" class="search-inner-msg">
    <image  class="search-inner-img" src="/asset/svgs/message-red.svg" bindtap="onOpenMsgListener"></image>
    <view hidden="{{!unReadFlag}}" class="search-inner-dot"></view>
  </view>
  <view wx:if="{{showSearchTxt}}" class="search-txt" bindtap="onSearchTxtClick">搜索</view>
</view>