var billStatusFormat = function(value) {
    var statusLabel = '待出库'
    switch(value) {
        case 1: statusLabel = '待出库' 
        break;
        case 2: statusLabel = '已出库' 
        break;
        default:
        break;
    }
    return statusLabel
}

var orderStatusFormat = function(value, stat) {
    var statusLabel = '待审核'
    if(stat && stat != '1') {
        switch(stat) {
            case '2': return '待审核'
            case '3': return '已审核'
            case '5': return '已关闭'
        }
    } else {
        if(value.isCompleted == 2) {
            statusLabel = '已关闭'
        } else if(value.state == 5 && value.isCompleted != 2) {
            statusLabel = '已审核'
        }
        return statusLabel
    }
}

var billSourceFormat = function(value, dict) {
    if(!value || dict.length == 0) {
        return ''
    }
    var sourceLabel = ''
    for(var i = 0; i< dict.length; i++) {
        if(dict[i].value == value) {
            sourceLabel = dict[i].name
        }
    }
    return sourceLabel
}
var auditQtyMin = function(datas) {
  if(!datas) {
      return 0
  }
  return datas.length
}
module.exports = {
    billStatusFormat: billStatusFormat,
    orderStatusFormat: orderStatusFormat,
    billSourceFormat: billSourceFormat,
    auditQtyMin: auditQtyMin
}