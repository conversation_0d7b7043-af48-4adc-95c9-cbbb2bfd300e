.item-content {
  background-color: #fff;
  margin: 24rpx;
  border-radius: 0 0 8rpx 8rpx;
}
.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
}
.item-bill {
  font-size: 28rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 36rpx;
}
.item-status {
  min-width: 120rpx;
  text-align: right;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 28rpx;
}
/* 待出库 */
.item-status-2, .item-confirm-1 { 
  color: #FAAE16;
}
.item-cust {
  color: #949596;
  margin-top: 16rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  line-height: 24rpx;
}
.source-sys {
  background: #FFFBE6;
  border-radius: 8rpx;
  border: 1px solid #FAAE16;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FAAE16;
  line-height: 24rpx;
  padding: 8rpx 12rpx;
  text-align: center;
}
.goodLayout {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
}
.good-img {
  width: 160rpx;
  height: 160rpx;
  margin-right: 32rpx;
  border-radius: 8rpx;
}
.good-info {
  flex: 1;
  height: 160rpx;
  display: flex;
  justify-content: space-between;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
}
.info-middle {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.info-name-box{
  flex: 1;
  font-size: 28rpx;
  line-height: 36rpx;
}
.info-name-box .name{
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 设置最大显示行数 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  color: #242424;
}
.info-name-box .specs {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 设置最大显示行数 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  color: #8A8A8A;
}
.info-count {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}
.info-price {
  color: #242424;
  font-size: 28rpx;
}
.info-outNum {
  color: #707070;
  font-size: 24rpx;
}
.total-block {
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 24rpx;
  font-size: 24rpx;
  text-align: right;
  padding: 24rpx;
}
.total-text {
  font-size: 32rpx;
}
.sale-audit-good {
  padding: 0;
}
.bar-code{
  word-break: break-all;
}
.m-b-12{
  margin-bottom: 12rpx;
}