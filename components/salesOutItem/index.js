// components/salesOutItem/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    itemObj: {
      type: Object,
      value: {},
      observer: function (newVal, oldVal) {
        if (newVal.id) {
          this.setData({
            confirmLineList: newVal.addLineList.map((item) => {
              return {
                id: item.id,
                confirmReqQty: item.billQty,
                price: item.pricecBillF,
              };
            }),
          });
        }
      },
    },
    orderLineList: {
      type: Array,
      value: [],
      observer: function (newVal, oldVal) {
        if (newVal.length > 0) {
          this.setData({
            confirmLineList: newVal.map((item) => {
              return {
                id: item.id,
                headId: item.headId,
                currAuditQty: item.toAuditQty,
                curAuditPrice: item.applyPrice,
                currCancelQty: 0,
                warehouseId: '',
                warehouseName: '',
                toAuditQty: item.toAuditQty
              };
            }),
          });
        }
      },
    },
    orderHead: {
      type: Object,
      value: {},
    },
    isAudit: {
      type: Boolean,
      value: false,
    },
    isFormOrderList: {
      type: Boolean,
      value: false
    },
    showHeadRight: {
      type: Boolean,
      value: true
    },
    showAmount: {
      type: Boolean,
      value: true
    },
    showCollapse: {
      type: Boolean,
      value: false
    },
    stat: {
      type: String,
      value: ''
    }
  },
  // 组件配置
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的初始数据
   */
  data: {
    confirmLineList: [],
    show: false,
    warehouseColumns: [],
    curId: '', // 当前操作块索id
    sourceDict: [], // 来源系统字典
    collapseNames: [],
  },
  attached() {
    // 拿取字典
    const sourceDict = wx.getStorageSync('dictMap').sourceSystem
    this.setData({
      sourceDict
    })
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onChangeStepper(e) {
      const id = e.currentTarget.dataset.id;
      let isInListIdx = this.data.confirmLineList.findIndex(
        (item) => item.id === id
      );
      let newArr = [...this.data.confirmLineList];
      if (isInListIdx != -1) {
        newArr[isInListIdx].confirmReqQty = e.detail;
      }
      this.setData({
        confirmLineList: newArr
      })
      this.triggerEvent("changeCount", newArr);
    },
    // 获取仓库列表
    checkkWarehouse(e) {
      const curId = e.currentTarget.dataset.id
      // 选择仓库
      App.getHttp()._post('/api/psi/baseWarehouse/page', {
        pageIndex: 1,
        pageSize: 200,
        param: {
          state: 2
        }
      }).then(res => {
        let warehouseColumns = []
        for (let r = 0; r < res.length; r++) {
          warehouseColumns.push({
            text: res[r].name,
            id: res[r].id
          })
        }
        this.setData({
          show: true,
          warehouseColumns: warehouseColumns,
          curId
        })
      })
    },
    // 确认选择仓库
    onConfirmWarehouse(event) {
      let detail = event.detail.value
      let curList = [...this.data.confirmLineList]
      let curObjIdx = curList.findIndex(item => item.id === this.data.curId)
      if (curObjIdx != -1) {
        curList[curObjIdx].warehouseName = detail.text
        curList[curObjIdx].warehouseId = detail.id
      }

      this.setData({
        show: false,
        confirmLineList: curList
      })
      this.triggerEvent('changeWarehouse', curList)
    },
    // 关闭选择仓库
    onCancelWarehouse() {
      this.setData({
        show: false
      })
    },
    // 修改销售订单的取消数量
    onChangeOrderCancel(e) {
      const id = e.currentTarget.dataset.id;
      let isInListIdx = this.data.confirmLineList.findIndex(
        (item) => item.id === id
      );
      let newArr = [...this.data.confirmLineList];
      if (isInListIdx != -1) {
        newArr[isInListIdx].currCancelQty = e.detail;
      }
      this.setData({
        confirmLineList: newArr
      })
      this.triggerEvent("changeCancel", newArr);
    },
    // 修改销售订单审批数量
    onChangeOrderAudit(e) {
      const id = e.currentTarget.dataset.id;
      let isInListIdx = this.data.confirmLineList.findIndex(
        (item) => item.id === id
      );
      let newArr = [...this.data.confirmLineList];
      if (isInListIdx != -1) {
        newArr[isInListIdx].currAuditQty = e.detail;
      }
      this.setData({
        confirmLineList: newArr
      })
      this.triggerEvent("changeAudit", newArr);
    },
    // 折叠监听
    onChangeCollape(event) {
      this.setData({
        collapseNames: event.detail,
      });
    },
  },
});
