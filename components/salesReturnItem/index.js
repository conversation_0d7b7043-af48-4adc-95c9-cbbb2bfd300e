// components/salesReturnItem/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    itemObj: {
      type: Object,
      value: {},
      observer: function (newVal, oldVal) {
        if (newVal.id) {
          this.setData({
            confirmLineList: newVal.addLineList.map((item) => {
              return {
                id: item.id,
                auditPrice: item.applyPrice,
                auditQtyBill: item.applyQty,
                auditAmount: ''
              };
            }),
          });
        }
      },
    },
    isAudit: {
      type: Boolean,
      value: false,
    },
    showHeadRight: {
      type: Boolean,
      value: true
    },
    showAmount: {
      type: Boolean,
      value: true
    }
  },
  // 组件配置
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的初始数据
   */
  data: {
    confirmLineList: [],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 修改销售订单审批数量
    onChangeOrderAudit(e) {
      const id = e.currentTarget.dataset.id;
      let isInListIdx = this.data.confirmLineList.findIndex(
        (item) => item.id == id
      );
      let newArr = [...this.data.confirmLineList];
      if (isInListIdx != -1) {
        newArr[isInListIdx].auditQtyBill = e.detail;
      }
      this.setData({
        confirmLineList: newArr
      })
      this.triggerEvent("changeAudit", newArr);
    }
  },
});
