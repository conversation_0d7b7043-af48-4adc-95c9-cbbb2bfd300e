<wxs src="./index.wxs" module="wxsUtil"></wxs>
<view class="item-content" wx:if="{{itemObj.billNo}}">
  <view class=" item-header">
    <view>
      <view class="item-bill">订单号: {{itemObj.billNo}}</view>
      <view class=" item-cust">{{itemObj.invoiceCustName}}</view>
    </view>
    <block wx:if="{{showHeadRight}}">
      <view class="source-sys" wx:if="{{isAudit}}">
        {{itemObj.sourceSystemName}}
      </view>
      <view wx:else>
        <view class="item-status item-confirm-{{itemObj.state}}">{{wxsUtil.orderStatusFormat(itemObj.state)}}</view>
      </view>
    </block>
  </view>
  <view class="item-goods " wx:for="{{itemObj.addLineList}}" wx:for-item="good" wx:for-index="goodIndex" wx:key="id">
    <view class="goodLayout">
      <image class="good-img" src="{{good.itemUrl}}"  mode="aspectFit"></image>
      <view class="good-info">
        <view class="info-name">{{good.itemName}}</view>
        <view class="info-count">
          <view class="info-price">
            ￥ {{good.applyPrice}}
          </view>
          <view wx:if="{{isAudit}}" class="info-outNum">
            <text class="">退货数量{{good.auditQtyBill || good.applyQty}}件</text>
          </view>
          <view wx:else class="info-outNum">
            <text class="">共{{good.applyQty || good.billQty}}件</text>
          </view>
        </view>
      </view>
    </view>
    <!-- <view wx:if="{{isAudit}}">
      <van-field label="退货数量" use-button-slot input-align="right">
        <van-stepper slot="button" value="{{ good.applyQty }}" min="{{0}}" max="{{good.applyQty}}" data-id="{{good.id}}" bind:change="onChangeOrderAudit" />
      </van-field>
    </view> -->
  </view>
  <!-- 合计 -->
  <view class="total-block" wx:if="{{showAmount}}">
    <text class="total-title">合计:</text>
    <text class="total-text">￥{{itemObj.totalAmount}}</text>
  </view>
  <!-- 预留插槽 -->
  <slot name="footer"></slot>
</view>