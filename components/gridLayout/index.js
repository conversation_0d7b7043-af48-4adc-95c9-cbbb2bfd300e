// components/gridLayout/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: ''
    },
    layoutList: {
      type: Array,
      value:[]
    },
    columnNum: {
      type: [String, Number],
      value: 4
    },
    iconSize: {
      type: [String, Number],
      value: '96rpx'
    },
    showBadge:{
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },
  attached(){
  },
  /**
   * 组件的方法列表
   */
  methods: {

  }
})
