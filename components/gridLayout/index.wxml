<!--components/gridLayout/index.wxml-->
<block wx:if="{{title}}">
  <view class="title">{{ title }}</view>
  <view class="line"></view>
</block>
<view class="content">
  <van-grid border="{{false}}" column-num="{{columnNum}}" icon-size="{{iconSize}}">
    <van-grid-item wx:for="{{layoutList}}" content-class="content-grid-item-class" wx:key="index" link-type="navigateTo" url="{{item.to}}" badge="{{showBadge?item.badge:''}}" use-slot>
      <image style="width: {{iconSize}}; height: {{iconSize}};" src="{{item.icon}}" mode="aspectFit" />
      <view class="van-item-txt">{{item.text}}</view>
    </van-grid-item>
  </van-grid>
</view>