// components/InventoryAnalysis/index.js
const App = getApp()
import * as echarts from '../../pages/ccs/echarts/ec-canvas/echarts.min'
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showTitle: {
      type: Boolean,
      value: true
    },
    marginStyle: {
      type: Boolean,
      value: false
    },
    hideChart: {
      type: Boolean,
      value: false
    }
  },
  pageLifetimes: {
    show() {
      this.getAnalysisData()
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    ec: {
      lazyLoad: true,
    },
    dataList: [],
    totalCount: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    initChart() {
      const that = this
      this.selectComponent('#inventory-chart').init((canvas, width, height, dpr) => {
        // 初始化图表
        let variable = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr, // 像素
        });
        variable.setOption(that.optionFunc());
        return variable//一定要return 否则展示会有问题
      });
    },
    optionFunc(chartData){
      const colors = ['#A0DD80', '#5D95F7', '#FAAE16']
      var option = {
        tooltip: {
          show: false
        },
        title: {
          text: `${this.data.totalCount}\n库存数量`,
          top: 'middle',
          left: 'center',
          textStyle: {
            fontSize: 12,
            fontFamily: 'PingFang SC-Regular, PingFang SC',
            fontweight: 400,
            color: '#242424'
          }
        },
        
        legend: {
         bottom: 0,
         itemWidth: 8,
         itemGap: 16,
         data: this.data.dataList.map((item, index) => {
          return {
            name: item.warehouseName,
            icon: 'circle'
          }
        })
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '60%'],
            avoidLabelOverlap: false,
            label: {
              formatter: '{c}',
            },
            itemStyle:{
              borderWidth: 2, //设置border的宽度有多大
              borderColor:'#fff',
            },
            data: this.data.dataList.map((item, index) => {
              return {
                value: item.qtyOnhand,
                name: item.warehouseName,
                itemStyle: { color: colors[index] }
              }
            })
          }
        ]
      };
      return option;
    },
    onClickToDetail() {
      wx.navigateTo({
        url: '/pages/ccs/analysis/inventoryAnalysis/index'
      })
    },
    getAnalysisData() {
      const url = '/api/psi/currentInvSearch/myx/pageSum'
      const supInfo = wx.getStorageSync('supInfo')
      const params = {
        param: {
          setsOfBooksId: supInfo.setsOfBooksId,
        }
      }
      App.getHttp()._post(url, params).then(res => {
        if(res && res.length > 0) {
          let totalCount = res.reduce((pre, cur) => {
            return pre + parseFloat(cur.qtyOnhand)
          }, 0)
          this.setData({
            dataList: res,
            totalCount
          })
          this.initChart()
        }
      })
    }
  }
})
