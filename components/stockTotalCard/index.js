// components/orderTotalCard/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    sourceData:{
      type:Object,
      value:{}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    itemList:[],
  },
  observers: {
    'sourceData': function(value) {
      if(value&&value.length>0){
        value.forEach(element => {
            this.data.itemList.push(element)
          })
        this.setData({
          itemList:this.data.itemList,
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})
