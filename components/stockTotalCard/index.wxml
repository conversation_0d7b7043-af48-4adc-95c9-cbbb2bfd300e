<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="card-layout">
  <view wx:for="{{itemList}}" wx:key="index" wx:for-item="item">
    <view class="good-layout flex">
      <image class="img" src="{{item.itemUrl}}"  mode="aspectFit"></image>
      <view class=" flexbox itemname two-line-ellipsis">{{item.itemName}}</view>
      <view class="count-box">
        <view class="itemprice">¥{{wxsUtil.moneyFormat(item.pricecBillF)}}</view>
        <view class="itemqty">发货数：{{item.billQty||0}}{{item.uomName||''}}</view>
      </view>
    </view>
    <view class="total-layout">
      <text class="txt">共{{item.billQty||0}}件商品</text>
      <text class="totaltxt">合计：</text>
      <text class="totalprice">¥{{wxsUtil.moneyFormat(item.amountBillF)}}</text>
    </view>
  </view>

</view>