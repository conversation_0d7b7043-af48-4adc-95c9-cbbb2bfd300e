.search-layout {
  background-color: #ffffff;
  padding: 0 0 12rpx 24rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  width: 100%;
}
.backArrow {
    padding: 3rpx 30rpx 3rpx 0
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}
.justify-content-between {
  justify-content: space-between;
}
.shopCar {
  margin-left: 10rpx;
}
.collectCar {
  margin-left: 30rpx;
}
.head-left {
    width: 45%;
}
.supplierType {
  line-height: 44rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  border-radius: 8rpx;
  margin-right: 10rpx;
}
.supplierType-1 {
  color: #faae16;
  border-color: #faae16;
  border: 1px solid #faae16;
}
.supplierType-2 {
  color: #52c718;
  border-color: #52c718;
  border: 1px solid #52c718;
}
.supplierName {
  /* width: 100%; */
  position: relative;
  display: inline-block;
  padding-right: 30rpx;
  padding-left: 20rpx;
}
.supplierName::after {
  content: "";
  position: absolute;
  top: 28rpx;
  right: 0rpx;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 12rpx solid #8a8a8a;
  transform: rotate(180deg);
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: nowrap;
}

.popup-dialog {
  z-index: 100000;
}
.popup-content-dialog {
  padding: 40rpx;
  width: 100%;
}
.popup-content-tag {
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}
.popup-content-tag-item {
  /* width: 100%; */
  margin: 20rpx 20rpx 0;
  text-align: center;
  padding: 10rpx 40rpx;
}

.icon-jump {
  position: absolute;
  right: 0;
}

