<!-- 头部导航 -->
<view class="search-layout flex-box align-center" style="padding-top: {{clientRectTop}}rpx;">
    <view class="head-left flex-box align-center" bindtap="onClickSelectSup">
        <block wx:if="{{showSupplier}}">
            <!-- <view class="supplierType supplierType-{{ custInfo.custType }}" wx:if="{{isShowType}}">
                {{custInfo.custType == 5 ? '厂商采购' : '经销采购'}}
            </view> -->
            <!-- <view class="supplierType" wx:if="{{isShowType}}">
                {{supplier.purchaseType == 'sup' ? '经销渠道 |' : ''}}
            </view> -->
            <!-- <van-icon  size="20" class="backArrow" name="arrow-left" catchtap="onClickBack" /> -->
            <view class="supplierName">
              {{supplier.saleOrgCodeName || '请选择销售组织'}}
            </view>
            
        </block>
        <!-- <block wx:elif="{{showSupplierText}}">
            <van-icon custom-style="back-arrow" size="20" class="backArrow" name="arrow-left" catchtap="onClickBack" />
            <view class="channelName-box single-line-ellipsis">
                {{supplier.channelName}}<text wx:if="{{supplier.channelName}}">|</text>{{supplier.bvendorName}}
            </view>
        </block> -->
        <!-- <block wx:else>
            <van-icon custom-style="back-arrow" size="20" class="backArrow" name="arrow-left" catchtap="onClickBack" />
            <view>{{customTitle}}</view>
        </block> -->
    </view>
    <van-popup show="{{ showPop }}" position="bottom" custom-style="height: 60%;" bind:click-overlay="questTypeCancel">
      <van-picker title="选择销售组织" show-toolbar columns="{{ saleOrgOptions }}" bind:confirm="questTypeConfirm" bind:cancel="questTypeCancel" />
    </van-popup>
    <van-icon class="collectCar" name="star-o" data-item="{{item}}" data-index="{{index}}" catchtap="onClickToCollect" size="25"></van-icon>
    <goods-cart class="shopCar" id="shopCar" />
</view>