// components/listHead/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isShowType: {
      type: Boolean,
      value: true
    },
    showSupplier: {
      type: Boolean,
      value: true
    },
    customTitle: {
      type: String,
      value: ''
    },
    showArrow:{
      type: Boolean,
      value: false
    },
    showSupplierText:{
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    saleOrgOptions: [], // 供应商数据源 
    supplier: {
      vendorCode: '',
      vendorId: '',
      vendorName: '',
      vendorSetsOfBooksId: '',
      invoiceCustCode: '',
      invoiceCustId: '',
      invoiceCustName: '',
      invoiceSetsOfBooksId: '',
      channelCode: '',
      channelId: '',
      channelName: '',
      saleOrgCode: '',
      saleOrgName: '',
      custGroupCode: '',
      companyCode: '',
      saleRegionCode: '',
      creditGrade: '',
      supId: '',
      priceListCode: '',
    }, // 当前供应商
    clientRectTop: 24,
    custInfo: {},
    showPop: false
  },
  lifetimes:{
    attached() {
      const rect = wx.getMenuButtonBoundingClientRect()
      this.setData({
        clientRectTop: (rect.top) * App.globalData.pxToRpxRatio,
      })
      this.getsupplierList() // 加载供应商
    },
  },
  // pageLifetimes: {
  //   show(){
  //     this.getsupplierList() // 加载供应商
  //   }
  // },
  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.setData({
        showPop: false
      })
    },
    onClickSelectSup() {
      if (this.data.saleOrgOptions <= 0) return
      this.setData({
        showPop: true
      })
    },
    // 获取销售组织
    async getsupplierList() {
      const res = await App.getHttp()._post(
        "/api/mmd/common/bsSaleOrgCode",
        {
          pageIndex: 1,
          pageSize: 5,
          param: {},
        }
      );
      this.setData({
        saleOrgOptions: res.map(item => {
          return {
            ...item,
            text: item.saleOrgCodeName
          }
        })
      })
      if (res.length > 1) return
      this.getCustData(res[0])
    },
    questTypeCancel(){
      this.setData({
        showPop: false
      })
    },
    questTypeConfirm(e){
      let item = e.detail.value;
      console.log(item, 'itemssss')
      this.getCustData(item)
      this.setData({
        showPop: false
      })
    },
    // 获取主客户
    async getCustData(salOrg) {
      const recordList = await App.getHttp()._post(
        "/api/mmd/common/bsSup/page",
        {
          pageIndex: 1,
          pageSize: 5,
          param: {saleOrgCode: salOrg.saleOrgCode,},
        }
      );
      let supplier = this.data.supplier
      if (recordList && recordList.length > 0) {
        // 默认当前客户, 优先取默认设置,没有再拿第一条
        const findDefaultIndex = recordList.findIndex((findSup) => !findSup.mainAccountId);
        const val = recordList[findDefaultIndex > -1 ? findDefaultIndex : 0];
        Object.keys(supplier).forEach((key) => {
          if (key === 'invoiceCustId') {
            this.data.supplier[key] = val.scustId;
          } else if (key === 'invoiceCustCode') {
            this.data.supplier[key] = val.scustCode;
          } else if (key === 'invoiceCustName') {
            this.data.supplier[key] = val.scustName;
          } else if (key === 'invoiceSetsOfBooksId') {
            this.data.supplier[key] = val.bsetsOfBooksId;
          } else if (key === 'vendorCode') {
            this.data.supplier[key] = val.bvendorCode;
          } else if (key === 'vendorId') {
            this.data.supplier[key] = val.bvendorId;
          } else if (key === 'vendorName') {
            this.data.supplier[key] = val.bvendorName;
          } else if (key === 'vendorSetsOfBooksId') {
            this.data.supplier[key] = val.ssetsOfBooksId;
          } else if (key === 'supId') {
            this.data.supplier[key] = val.id;
          } else if (key === 'channelId') {
            const channelIdArr = val.channelIds.split(',');
            this.data.supplier[key] = channelIdArr.length > 0 ? channelIdArr[channelIdArr.length - 1] : '';
          } else if (key === 'saleRegionCode') {
            this.data.supplier[key] = val.regionCode;
          } else {
            this.data.supplier[key] = val[key];
          }
        });
        // this.refreshQuery();
      }
      supplier.saleOrgCode = salOrg.saleOrgCode
      supplier.saleOrgCodeName = salOrg.saleOrgCodeName
      this.setData({
        supplier
      }, () => {
        this.triggerEvent('saleOrgChange', this.data.supplier)
      })
    },
    onClickBack() {
      wx.navigateBack({
        delta: 1
      })
    },
    onClickToCollect() {
      // 跳转到收藏
      // pages/ccs/focus/index
      wx.navigateTo({
        url: `/pages/ccs/focus/index?saleOrgCode=${this.data.supplier.saleOrgCode}&saleOrgName=${this.data.supplier.saleOrgCodeName}`,
      })
    },
    getCartsCount() {
      this.selectComponent('#shopCar').getCartsCount()
    },
  }
})
