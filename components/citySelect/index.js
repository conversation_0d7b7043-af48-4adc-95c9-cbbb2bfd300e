// components/citySelect/index.js
const App = getApp()
Component({

  /**
   * 页面的初始数据
   */
  data: {
    maxCount: 4,
    currentIndex: 0,
    currentSelect: [{ name: '请选择' }],
    regionList: []
  },
  created(){
    this.obtainRegionList()
  },
  methods: {
    cancel: function (item) {
      // 取消按钮，取消本次操作
      this.triggerEvent('closeCitySelect')
    },
    confirm() {
      // 确定按钮，确定选择的地区县
      const result = this.data.currentSelect.map((res) => {
        if (!res.code) {
          res.code = ''
          res.name = ''
          res.id = ''
        }
        return res
      })
      this.triggerEvent('addressChange', result)
    },

    onClickIndex: function (e) {
      // 从新选择地/区/县值
      let index = e.currentTarget.dataset.value // 获取传入的参数
      this.setData({
        currentIndex: index
      })
      let selectList = this.data.currentSelect
      selectList.splice(index, this.data.maxCount - index, {
        name: '请选择'
      })
      this.setData({
        currentSelect: selectList
      })
      this.obtainRegionList(
        index === 0 ? '' : this.data.currentSelect[index - 1].id
      )
    },
    obtainRegionList(parentId) {
      let param = !!parentId?{parentId: parentId}:{level: this.data.currentSelect.length}
      // 获取地区县的函数
      App.getHttp()._post('/api/mmd/region/getRegionInfo', param).then((res) => {
        if (res && res.length > 0) {
          this.setData({
            regionList: res
          })
        }
      })
    },
    onClickRegion: function (e) {
      // 选择地区县
      let region = e.currentTarget.dataset.value // 获取传入的参数
      // this.currentIndex = region.level
      this.setData({
        currentIndex: region.level
      })
      const howmany = this.data.maxCount - region.level
      let currentSelectList = this.data.currentSelect
      if (howmany === 0) {
        currentSelectList.splice(region.level - 1, 1, region)
      } else {
        currentSelectList.splice(region.level - 1, howmany, region)
      }
      if (this.data.currentSelect.length < this.data.maxCount) {
        currentSelectList.push({
          name: '请选择'
        })
        this.obtainRegionList(region.id)
      }
      this.setData({
        currentSelect: currentSelectList
      })
    },
  }
})