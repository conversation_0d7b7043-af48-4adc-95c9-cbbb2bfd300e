/* components/citySelect/index.wxss */

.area-layout {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow-y: hidden;
}

.area-layout .area-layout_top {
  font-size: 28rpx;
}
.area-layout .area-layout_top .area-layout_top_cancel {
  padding: 32rpx;
  color: #2d2d4b;
}

.area-layout .area-layout_top .area-layout_top_submit {
  padding: 32rpx;
  color: #2d2d4b;
}
.area-layout .area-layout_top .area-layout_top_active {
  color: #00b9c3;
}
.area-layout .area-layout_mid {
  padding: 20rpx 0;
  border-top: 1rpx solid rgba(0,0,0,0.3);
  border-bottom: 1rpx solid rgba(0,0,0,0.3);

}
.area-layout .area-layout_mid .area-layout_mid_txtbox {
  padding: 0 20rpx;
  font-size: 28rpx;
}

.area-layout .area-layout_mid .area-layout_mid__active {
  color:#00b9c3;
}
.area-layout .area-layout_bot {
  height: 400rpx;
  padding: 0 20rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;

}
.area-layout .area-layout_bot .area-layout_bot_listcell {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #6b6b6b;
}
.area-layout .flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.area-layout .justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}