<!--components/citySelect/index.wxml-->
<view class="area-layout">
  <view class="flex justify-content-between area-layout_top">
    <view class="area-layout_top_cancel" bindtap="cancel">
      取消
    </view>
    <view wx:if="{{currentSelect.length > 3}}"
      class="area-layout_top_submit {{currentSelect.length>3?'area-layout_top_active':''}}" bindtap="confirm">
      确定
    </view>
  </view>
  <view class="flex flex_wrap area-layout_mid">
    <view wx:for="{{currentSelect}}" wx:for-item="region" wx:for-index="index" wx:key="index"
      class="area-layout_mid_txtbox {{currentIndex == index?'area-layout_mid__active':''}}" bindtap="onClickIndex"
      data-value="{{index}}">
      {{ region.name }}
    </view>
  </view>
  <view class="area-layout_bot">
    <view wx:for="{{regionList}}" wx:for-item="region" wx:for-index="index" wx:key="index"
      class="area-layout_bot_listcell" bindtap="onClickRegion" data-value="{{region}}">
      {{ region.name }}
    </view>
  </view>
</view>