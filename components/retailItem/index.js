// components/retailItem/index.js
Component({
  // 组件配置
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    itemObj: {
      type: Object,
      value: {}
    },
    fromDetail: {
      type: Boolean,
      value: false
    },
    noMarginX: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})
