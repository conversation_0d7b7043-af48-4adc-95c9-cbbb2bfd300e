.item-content {
    background-color: #fff;
    margin: 24rpx;
    border-radius: 0 0 8rpx 8rpx;
  }
  .item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
  }
  .item-bill {
    font-size: 28rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 36rpx;
  }
  .item-status {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #707070;
    line-height: 28rpx;
  }
  /* 待出库 */
  .item-status-2, .item-confirm-1 { 
    color: #FAAE16;
  }
  .goodLayout {
    display: flex;
    align-items: center;
  }
  .good-img {
    width: 160rpx;
    height: 160rpx;
    margin-right: 32rpx;
    border-radius: 8rpx;
  }
  .good-info {
    flex: 1;
    height: 160rpx;
    display: flex;
    justify-content: space-between;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
  }
  .info-name {
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 设置最大显示行数 */
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    font-size: 28rpx;
    color: #242424;
    line-height: 36rpx;
  }
  .specs{
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* 设置最大显示行数 */
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    font-size: 26rpx;
    color: #8A8A8A;
    line-height: 36rpx;
  }
  .info-count {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
  }
  .info-price {
    color: #242424;
    font-size: 28rpx;
  }
  .info-outNum {
    color: #707070;
    font-size: 24rpx;
  }
  .total-block, .total-block-detail {
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #242424;
    line-height: 24rpx;
    font-size: 24rpx;
    text-align: right;
    padding: 24rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .total-block-detail {
    padding: 24rpx 0;
  }
  .total-text {
    font-size: 32rpx;
  }
  .total-qty {
    margin-right: 24rpx;
  }
  .info-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .add-to-return {
    border-radius: 8rpx;
    border: 1px solid #DBDBDB;
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #242424;
    line-height: 36rpx;
    padding: 14rpx 32rpx;
  }
  .item-goods {
    padding: 24rpx 32rpx;
  }
  .no-margin-x {
    margin: 24rpx 0;
  }
  .good-info-middle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    height: 160rpx;
  }
  