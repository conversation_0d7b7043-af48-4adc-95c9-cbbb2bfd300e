<view class="item-content {{noMarginX ? 'no-margin-x' : ''}}">
    <view class="item-header" wx:if="{{!fromDetail}}">
        <view class="item-bill">订单号: {{itemObj.billNo}}</view>
        <view class="item-status item-status-{{itemObj.state}}">
            {{itemObj.billType == '0205' ? '已完成' : '已退货'}}
        </view>
    </view>
    <view class="item-goods" wx:for="{{itemObj.addLineList}}" wx:for-item="good" wx:for-index="goodIndex" wx:key="id">
        <view class="goodLayout">
            <image class="good-img" src="{{good.itemUrl}}"  mode="aspectFit"></image>
            <view class="good-info">
                <view class="good-info-middle">
                    <view>
                      <view class="info-name">{{good.itemName}}</view>
                      <view class="specs single-line-ellipsis">{{good.specs||''}}</view>
                    </view>
                    <view class="info-price" wx:if="{{fromDetail}}">{{good.warehouseName}}</view>
                </view>
                <!-- <view class="info-name">{{good.itemName}}</view> -->
                <view class="info-count">
                    <view class="info-price">￥ {{good.pricecBillF}}</view>
                    <view class="info-outNum">
                        <text class="">共{{good.billQty}}件</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="total-block-detail" wx:if="{{fromDetail}}">
            <text class="total-label">合计:</text>
            <text class="total-text">￥{{good.amountBill}}</text>
        </view>
    </view>
    <!-- 合计 -->
    <view class="total-block" wx:if="{{!fromDetail}}">
        <view class="total-text total-qty" wx:if="{{itemObj.addLineList.length > 0}}">
            共{{itemObj.totalQty}}件
        </view>
        <view class="total-amount">
            <text class="total-title">合计:</text>
            <text class="total-text">￥{{itemObj.amountTotal}}</text>
        </view>
    </view>
    <!-- 预留插槽 -->
    <slot name="footer"></slot>
</view>