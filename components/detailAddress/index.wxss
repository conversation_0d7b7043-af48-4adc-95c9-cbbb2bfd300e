.address-layout {
  background: #ffffff;
  border-radius: 8rpx;
  padding: 32rpx;
  display: flex;
  align-items: flex-start;
  height: 150rpx;
}
.address-info {
  flex: 1;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 44rpx;
  font-size: 28rpx;
  margin-left: 18rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
.address-text {
  color: #242424;
  line-height: 44rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.contact {
  font-size: 28rpx;
  color: #707070;
}
