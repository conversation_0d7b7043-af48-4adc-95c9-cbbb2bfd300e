// components/retailAddCarts/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    itemInfo: {
      type: Object,
      value: {},
    },
    type: {
      type: Number,
      value: 1, // 1 加入结账台 2 立即开单
    },
  },
  attached() {
    this.getWarehouseList()
  },
  observers: {
    'show':function(value){
      console.log('==observers show=',value);
      if(value&&this.data.curwarehouseId){
        this.getOnhand()
      }
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    curwarehouseId: "",
    curwarehouseName: "",
    showPicker: false,
    warehouseOptions: [],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 确认提交
    onConfirmCreate() {
      if (this.data.type == 2 && !this.data.curwarehouseId) {
        wx.showToast({
          icon: "none",
          title: "请选择仓库",
        });
        return false;
      }
      if (this.data.itemInfo.qty == 0) {
        wx.showToast({
          icon: "none",
          title: "请填写数量",
        });
        return false;
      }
      this.triggerEvent("onClose", {...this.data.itemInfo, warehouseId: this.data.curwarehouseId, warehouseName: this.data.curwarehouseName});
      this.setData({
        curwarehouseId: "",
        curwarehouseName: "",
      })
    },
    // 修改数量
    onChangeStep(e) {
      this.data.itemInfo.qty = e.detail;
      this.setData({
        itemInfo: this.data.itemInfo
      })
    },
    // 获取对应仓库的库存
    getOnhand() {
      const url = "/api/psi/currentInv/selectItemStorage";
      const params = {
        itemId: this.data.itemInfo.itemId,
        warehouseId: this.data.curwarehouseId,
      };
      App.getHttp()
        ._post(url, params)
        .then((res) => {
          if (res && res.length > 0) {
            this.data.itemInfo.qtyAvi = res[0].qtyAvi||0;
            this.data.itemInfo.qty = 0;
            this.setData({
              itemInfo: this.data.itemInfo
            })
          }
        });
    },
    onClickTodo() {
      this.setData({
        showPicker: true
      })
    },
    getWarehouseList() {
      App.getHttp()._post('/api/psi/baseWarehouse/page', {
        pageIndex:1,
        pageSize: 200,
        param: {
          state: 2
        }
      }).then(res => {
        let warehouseColumns = []
        for (let r = 0; r < res.length; r++) {
          warehouseColumns.push({
            text: res[r].name,
            id: res[r].id
          })
          if(res[r].isDefault ===2){
            this.setData({
              curwarehouseId: res[r].id,
              curwarehouseName: res[r].name,
            })
          }
        }
        this.setData({
          warehouseOptions: warehouseColumns
        })
      })
    },
    onCancelWarehouse() {
      this.setData({
        showPicker: false
      })
    },
    onConfirmWarehouse(e) {
      const detail = e.detail.value
      this.setData({
        curwarehouseName: detail.text,
        curwarehouseId: detail.id,
        showPicker: false
      })
      this.getOnhand()
    },
    // 关闭弹框
    onCloseAddCarts() {
      // this.setData({
      //   curwarehouseId: "",
      //   curwarehouseName: "",
      // })
      this.triggerEvent('onClose')
    }
  },
});
