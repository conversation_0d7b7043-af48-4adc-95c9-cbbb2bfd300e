.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
/* 垂直 */
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}
.justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.content-layout {
  padding: 112rpx 24rpx 0 24rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}
.right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.content-layout .img {
  width: 160rpx;
  height: 160rpx;
  margin-right: 32rpx;
}
.itemName {
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  line-height: 40rpx;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
}
.specs{
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #8a8a8a;
  font-weight: 400;
}
.itemPrice {
  font-family: SanFranciscoText-Semibold;
  font-size: 36rpx;
  color: #242424;
  line-height: 42rpx;
  font-weight: 600;
}
