<view class="root-layout">
    <van-popup show="{{ show }}" position="bottom" custom-style="height: 55%;" bind:close="onCloseAddCarts" closeable close-icon-position="top-right" round>
        <view>
            <view class="content-layout flex">
                <image class="img" src="{{itemInfo.itemUrl}}" mode="aspectFit" lazy-load />
                <view class="right flexbox">
                    <view>
                      <view class="itemName  two-line-ellipsis">{{itemInfo.itemName}}</view>
                      <view class="specs  single-line-ellipsis">{{itemInfo.specs}}</view>
                    </view>
                    <view class="itemPrice">
                        <text class="uom" wx:if="{{itemInfo.price}}">¥{{itemInfo.price}}</text>
                        <text class="uom" wx:else>暂无报价</text>
                    </view>
                </view>
            </view>
            <block wx:if="{{type == 2}}">
                <van-field label="仓库" input-align="right" placeholder="请选择仓库" readonly value="{{curwarehouseName}}" is-link  bind:click-input="onClickTodo" />
                <van-field label="库存" input-align="right" placeholder="" readonly value="{{itemInfo.qtyAvi}}" />
                <van-field label="数量" use-button-slot input-align="right" wx:if="{{curwarehouseId}}">
                    <van-stepper slot="button" plus-class="plusclss" minus-class="plusclss" input-class="inputclss" value="{{itemInfo.qty}}" async-change integer step="1" min="0" max="{{itemInfo.qtyAvi}}" button-size="24" input-width="48" bind:change="onChangeStep" />
                </van-field>
            </block>
            <block wx:else>
                <van-field label="数量" use-button-slot input-align="right">
                    <van-stepper slot="button" plus-class="plusclss" minus-class="plusclss" input-class="inputclss" value="{{itemInfo.qty}}" async-change integer step="1" min="0" button-size="24" input-width="48" bind:change="onChangeStep" />
                </van-field>
            </block>
            <footer-button btnWord="确认" bind:mainClick="onConfirmCreate"></footer-button>
        </view>
    </van-popup>
    <van-popup show="{{ showPicker }}" round position="bottom" custom-style="height: 60%" bind:click-overlay="onCancelWarehouse">
        <van-picker columns="{{ warehouseOptions }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
    </van-popup>
</view>
  
