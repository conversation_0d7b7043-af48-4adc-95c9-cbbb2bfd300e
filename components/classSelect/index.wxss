/* components/classSelect/index.wxss */
.class-box {
  display: flex;
  background-color: #fff;
  font-size: 26rpx;
}
.class-box view {
  box-sizing: border-box;
}
.class-box view:nth-of-type(3) {
  box-sizing: border-box;
  border-left: 1rpx solid  rgba(0,0,0,0.04);
}
.class-box .level1 {
  width: 27.7%;
  background: rgba(0,0,0,0.04);
}
.class-box .level1 .van-sidebar-item--selected{
  background-color: #fff;
}
.class-box .level2 {
  width: 36%;
  background-color: #fff;
}
.class-box .level3 {
  width: 36%;
  background-color: #fff;
}
.class-box .level view {
  width: 100% !important;
  border: none;
}
.level1 .item {
  text-align: center;
  
}
.level1 .selItem {
  padding: 0 !important;
  background-color: #fff;
}
.level1 .selItem view view{
  margin: 20rpx 0;
  text-align: center;
  border-left: 12rpx solid #00b9c3;
  box-sizing: border-box;
}
.selItem{
  border: none !important;
  font-weight: 500;
  color: #00b9c3 !important;
}
.level2 .item,.level3 .item {
  background-color: #fff !important;
}
.class-box .item ,.class-box .selItem{
  height: 80rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding-top: 0;
  padding-bottom: 0;
}
/* .level1 .van-sidebar-item {
  background: transparent !important;
  padding: 0 !important;
  margin: 20rpx 0;
}
.van-sidebar-item--selected{
  border: none !important;
}
.van-sidebar-item--selected .van-sidebar-item__text{
  /* border: none !important; */
  /* display: block;
  width: 100%;
  text-align: center;
  color: #00b9c3;
  font-weight: 500;
  border-left: 6rpx solid #00b9c3;
}
.level1 .van-sidebar-item__text {
  text-align: center;
} */ 
/* .class-box view .item-detail {
  width: 100% !important;
  color: rgba(0,0,0,0.75);
}
.class-box view .item-detail view {
  width: 100% !important;
  display: inline-block;
  padding-left: 32rpx;
  margin: 24rpx 0;
}

.class-box .level1 {
  background: rgba(0,0,0,0.04);
  border: none;
}
.class-box .level1 .item-detail view{
  padding-left: 0;
  text-align: center;
}
.class-box .level1  view{
  color: rgba(0,0,0,0.45);
}
.class-box view .item-select {
  color: #00b9c3 !important;
  font-weight: 500;
  background-color: #fff;
}
.class-box view .item-select view{
  color: #00b9c3 !important;
 
}
.class-box .level1 .item-select view {
  border-left: 8rpx solid #00b9c3;
  color: #00b9c3 !important;
} */