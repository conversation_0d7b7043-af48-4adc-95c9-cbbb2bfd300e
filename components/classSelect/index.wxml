<!--components/classSelect/index.wxml-->
<view class="class-box">
	<view wx:for="{{classList}}" wx:key="index" wx:for-item="classItem" class="{{'level level'+ (index + 1)}}">
		<van-sidebar active-key="{{ activeKey[index] }}" data-list="{{classItem}}" data-level="{{index}}" bind:change="onChange">
			<van-sidebar-item custom-class="{{activeKey[index] == i ? 'selItem' : 'item'}}" wx:for="{{classItem}}" title="{{item.name}}" wx:for-index="i"  wx:key="i"/>

		</van-sidebar>
	</view>

	

</view>

