// components/classSelect/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    classFilters: {
      type: Array,
      value: []
    },

  },

  /**
   * 页面的初始数据
   */
  data: {
    activeKey: [0,0,0],
    titleName:[],
    value: [],
    oldValue: [],
    classList: [],
   
  },
  observers: {
    "classFilters": async function (value) {
      this.setData({
        value
      },()=>{
        if (value.length > 0 && this.data.classList.length == 0){
          this.getClassification()
        }
      })
      

    }
  },
  created() {
    this.getClassification()
  },
  methods: {
    getActiveKeyList(){

    },
    getActiveKey(array, id) {
      for (let index = 0; index < array.length; index++) {
        if (id == array[index].id) {
          return {
            index,
            titleName:array[index].name,
            array:array[index].childItemClassList || ''
          }
        }

      }
    },
    //获取产品分类
    async getClassification() {
      if (this.data.classList.length > 0){
        return
      }
      const res = await App.getHttp()._post('/api/mmd/itemClass/myx/getTree', {})
      // res.forEach(item => {
      //   item.brandClassId = item.brandId
      //   item.brandClassCode = item.brandCode
      //   item.brandClassName = item.brandName
      // });
      let classList = [res]
      let activeKey = []
      for (let index = 0; index < this.data.value.length; index++) {
        let obj = this.getActiveKey(classList[index], this.data.value[index])
        activeKey.push(obj.index)
        if (obj.array){
          classList.push(obj.array)
        } else {
           //最后一级
          this.data.titleName = obj.titleName
          this.triggerEvent('titleName',  {titleName:obj.titleName})
        }
      }
      this.setData({
        classList,
        activeKey
      })
    },
    onChange(e) {
      const index = e.detail //当前点击的index
      const level = +e.currentTarget.dataset.level //点击第几级
      const list= e.currentTarget.dataset.list
      this.data.value[level] = list[index].id
      this.data.titleName = list[index].name
      this.data.activeKey[level] = index
      if (level == 1) {
        this.setData({
          activeKey:this.data.activeKey
        })
        this.triggerEvent('close',  {value:this.data.value,titleName:this.data.titleName})
      } else {
        if (list[index].childItemClassList.length > 0) {
          this.data.classList[level+1] = list[index].childItemClassList
          this.data.activeKey[level+1] = 0
          this.data.titleName = this.data.classList[level+1][0].name
          this.data.value[level+1] = this.data.classList[level+1][0].id
        } else {
          this.data.classList[level+1] = []
          this.data.titleName = ''
          this.data.value[level+1] = undefined
        }
        this.setData({
          activeKey:this.data.activeKey,
          classList:this.data.classList
         })
      }
    },
    onclose() {

      this.triggerEvent('change', {value:this.data.value,titleName:this.data.titleName})

    }
  }
})