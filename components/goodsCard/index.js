// components/goodsCard/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showCart:{
      type:Boolean,
      value:true
    },
    item:{
      type:Object,
      value:{}
    },
    isHorizontal:{
      type:Boolean,
      value:false
    },
    showQtyOnhand:{
      type:Boolean,
      value:true
    },
    showRetailPrice:{
      type:Boolean,
      value:true
    },
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    addToCarts(){
      this.triggerEvent('addToCarts')
    },
    nowPurchase(){
      this.triggerEvent('nowPurchase')
    }
  }
})
