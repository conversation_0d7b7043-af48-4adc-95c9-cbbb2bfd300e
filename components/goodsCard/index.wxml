<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="card-layout-h" wx:if="{{isHorizontal}}"> 
  <view  class="flex_v_center">
    <image  class="img" src="{{item.itemUrl||item.picUrl}}" mode="aspectFit" lazy-load />
    <view class="left flexbox">
      <view  class="itemName itembox single-line-ellipsis">{{item.itemName}}</view>
      <view wx:if="{{item.vendorName}}" class="vendorName itembox single-line-ellipsis">供货方：{{item.vendorName}}</view>
      <view class="itemPrice">
        <text class="uom" wx:if="{{item.standardPrice || item.standardPrice === 0}}">¥</text>
        {{wxsUtil.moneyFormat(item.standardPrice)}} 
        <text class="itemQty" wx:if="{{showQtyOnhand}}">库存：{{item.qtyOnhand || '无货'}}</text>
      </view>
      <view class="vendor" wx:if="{{showRetailPrice}}">建议零售价：{{!item.retailPrice && item.retailPrice !== 0 ? '暂无报价': item.retailPrice }}</view>
      <image wx:if="{{showCart && item.isInPurItem === 2}}" class="cart" src="/asset/imgs/cart.png" mode="aspectFit" lazy-load catchtap="addToCarts" />
      <!-- <view class="now-purchase" catchtap="nowPurchase">立即购买</view> -->
    </view>
  </view>
  <view class="line"/>
</view>
<view wx:else class="card-layout-v">
  <image  class="img" src="{{item.itemUrl||item.picUrl}}" mode="aspectFit" lazy-load />
  <view class="itemName single-line-ellipsis">{{item.itemName}}</view>
  <view wx:if="{{item.vendorName}}" class="itemName itembox single-line-ellipsis">供货方:{{item.vendorName}}</view>
  <view class="itemPrice"><text class="uom">¥</text>{{wxsUtil.moneyFormat(item.standardPrice)}}</view>
</view>