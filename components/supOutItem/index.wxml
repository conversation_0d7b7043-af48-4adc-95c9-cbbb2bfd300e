<wxs src="./index.wxs" module="wxsUtil"></wxs>
<wxs src="/wxs/common.wxs" module="wxsUtilApp"></wxs>
<view class="item-content" wx:if="{{itemObj.billNo }}">
  <view class=" item-header">
    <view>
      <view class="item-bill">订单号: {{itemObj.billNo }}</view>
      <view class="item-cust">{{itemObj.custName }}</view>
      <view class="item-cust" wx:if="{{itemObj.remark}}">{{itemObj.remark}}</view>
    </view>
    <block wx:if="{{showHeadRight}}">
      <view wx:if="{{isAudit}}">
        <text class="source-sys" wx:if="{{itemObj.sourceSystem || orderHead.sourceSystem}}">{{itemObj.sourceSystemName || orderHead.sourceSystemName || wxsUtil.billSourceFormat((itemObj.sourceSystem || orderHead.sourceSystem), sourceDict)}}</text>
      </view>
      <view wx:else class="item-status">
        <view class="item-status-{{stat && stat != 1 ? stat : orderHead.state}}" wx:if="{{isFormOrderList}}">{{wxsUtil.orderStatusFormat(orderHead, stat)}}</view>
        <view class="item-confirm-{{itemObj.isConfirm}}" wx:else>{{wxsUtil.billStatusFormat(itemObj.isConfirm)}}</view>
      </view>
    </block>
  </view>
  <van-collapse value="{{ collapseNames }}" bind:change="onChangeCollape">
    <view class="item-goods " wx:for="{{itemObj.lines}}" wx:for-item="good" wx:for-index="goodIndex" wx:key="id">
      <view class="goodLayout">
        <image class="good-img"  mode="aspectFit" src="{{good.itemUrl}}"></image>
        <view class="good-info">
          <view class="info-middle">
            <view class="info-name-box">
              <view class="name">{{good.itemName}}</view>
              <view class="specs">{{good.specs}}</view>
            </view>
            <!-- 1满减 2满折 3一口价 4满赠 5众筹 -->
            <!-- 满减 满折 满赠才显示配比数 -->
            <view class="info-outNum" wx:if="{{isFormOrderList && isAudit && (orderHead.orderType === 1 || orderHead.orderType === 2 || orderHead.orderType === 4)}}">
              配比数 {{good.proportioningQty || ''}}
            </view>
          </view>
          <view class="info-count">
            <view class="info-price">
              ￥{{isFormOrderList ? wxsUtilApp.moneyFormatInt(good.applyPrice, 'int') + wxsUtilApp.moneyFormatInt(good.applyPrice) : wxsUtilApp.moneyFormatInt(good.pricecBillF, 'int') + wxsUtilApp.moneyFormatInt(good.pricecBillF)}}
            </view>
            <view class="info-outNum">
              <text>共{{good.applyQty || good.billQty}}件</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 销售出库模块 -->
      <block wx:if="{{!isFormOrderList}}">
        <view class="sale-audit-good" wx:if="{{isAudit}}">
          <van-field value="{{ good.warehouseName }}" readonly label="出库仓库" input-align="right" />
          <van-field use-button-slot label="审核数量" wx:if="{{itemObj.isConfirm === 1}}">
            <van-stepper slot="button" value="{{ good.billQty }}" max="{{good.billQty}}" data-id="{{good.id}}" min="{{wxsUtil.auditQtyMin(good.barCodeFlowResponseDTOList)}}" bind:change="onChangeStepper" disabled />
          </van-field>
          <van-field value="{{ good.confirmQty }}" readonly label="确认数量" input-align="right" wx:else />
        </view>
      </block>
      <block wx:else>
        <view wx:if="{{isAudit}}">
          <van-field readonly input-align="right" right-icon="arrow" placeholder="请选择仓库" label="出库仓库" value="{{confirmLineList[goodIndex].warehouseName}}" data-id="{{good.id}}" border="{{false}}" bind:click-icon="checkkWarehouse" bind:click-input="checkkWarehouse" />
          <van-field readonly input-align="right" label="开单数量" value="{{good.applyQty}}"></van-field>
          <van-field label="取消数量" use-button-slot input-align="right">
            <van-stepper slot="button" value="{{0}}" min="{{0}}" max="{{good.toAuditQty}}" data-id="{{good.id}}" bind:change="onChangeOrderCancel" />
          </van-field>
          <van-field label="审核数量" use-button-slot input-align="right">
            <van-stepper slot="button" value="{{ good.toAuditQty }}" min="{{0}}" max="{{good.toAuditQty}}" data-id="{{good.id}}" bind:change="onChangeOrderAudit" />
          </van-field>
        </view>
      </block>
      <van-collapse-item name="{{'collapse'+index}}" wx:if="{{good.barCodeFlowResponseDTOList&&showCollapse}}">
        <view slot="title">已扫码信息({{good.barCodeFlowResponseDTOList.length}})</view>
        <view slot="value">时间</view>
        <view class="flex font-sub-hint m-b-12" wx:for="{{good.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex">
          <view class="bar-code">条码：{{barCodeItem.barCode}} 扫码时间：{{barCodeItem.createTime}}</view>
        </view>
      </van-collapse-item>
    </view>
  </van-collapse>
  <!-- 合计 -->
  <view class="total-block" wx:if="{{showAmount}}">
    <text class="total-title">合计:</text>
    <text class="total-text">￥{{itemObj.amountTotal || orderHead.totalAmount}}</text>
  </view>
  <!-- 预留插槽 -->
  <slot name="footer"></slot>
  <van-popup show="{{ show }}" round position="bottom" custom-style="height: 60%" bind:close="onClose">
    <van-picker columns="{{ warehouseColumns }}" show-toolbar title="选择仓库" bind:cancel="onCancelWarehouse" bind:confirm="onConfirmWarehouse" />
  </van-popup>
</view>