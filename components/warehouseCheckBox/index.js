// components/warehouseCheckBox/index.js
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        stockList:{
            type: Array,
            value: []
        },
        showOpenSet:{
            type: Boolean,
            value: false
        }
    },
    observers: {
        'showOpenSet': function(showOpenSet) {
            // 清空全部已选
            let isChecked = false;
            let stockList = this.data.stockList
            this.setData({acitveIndex: isChecked ? firstIndex : -1})
            console.log(this)
            stockList = stockList.map(item=>{
                item.isChecked = isChecked;
                item.result = []
                return item;
            })
            this.triggerEvent('listChange',stockList)
        }
      },

    /**
     * 组件的初始数据
     */
    data: {
        acitveIndex: -1
    },

    /**
     * 组件的方法列表
     */
    methods: {
        onItemCheck(e) {
            console.log('onItemCheck', e.detail)
            let isChecked = e.detail;
            let firstIndex = e.target.dataset.value;
            let stockList = this.data.stockList
            this.setData({acitveIndex: isChecked ? firstIndex : -1})
            console.log(this)
            stockList[firstIndex].isChecked = isChecked
            stockList[firstIndex].result = isChecked ? stockList[firstIndex].items.map(item => {
                return item.itemId
            }) : []
            console.log(stockList[firstIndex].result)
            this.triggerEvent('listChange',stockList)
            // this.setData({
            //     stockList: stockList
            // })
        },
        onSecondItemCheck(e) {
            console.log('onSecondItemCheck', e.detail)
            let result = e.detail;
            let firstIndex = e.target.dataset.value;
            let stockList = this.data.stockList
            this.setData({acitveIndex: e.detail.length > 0 ? firstIndex : -1 })
            stockList[firstIndex].result = result
            if (result.length !== stockList[firstIndex].items.length) {
                stockList[firstIndex].isChecked = false
            } else if (result.length === stockList[firstIndex].items.length) {
                stockList[firstIndex].isChecked = true
            }
            console.log('stockList', stockList)
            this.triggerEvent('listChange',stockList)
            // this.setData({
            //     stockList: stockList
            // })
        },
        onStepperChange(e) {
            console.log(e);
            let stockList = this.data.stockList
            let index1 = e.currentTarget.dataset['index1'];
            let index2 = e.currentTarget.dataset['index2'];
            stockList[index1].items[index2].billQty = e.detail
            console.log('stockList', stockList)
            this.triggerEvent('listChange',stockList)
            // this.setData({ stockList })
        },
        onDeleteClick(e) {
            let index1 = e.target.dataset.value1;
            let index2 = e.target.dataset.value2;
            // let stockList = this.data.stockList
            // stockList[index1].items.splice(index2, 1)
            // if (!stockList[index1].items.length) {
            //     stockList.splice(index1, 1)
            // }
            // this.setData({
            //     stockList: stockList
            // })
            this.triggerEvent('onDeleteClick',{index1, index2})
        },
    },
})
