<!--components/warehouseCheckBox/index.wxml-->
<view wx:for="{{stockList}}" wx:key="index1" wx:for-item="item1" wx:for-index="index1" class="stock-list-item" data-value="{{index1}}">
    <van-checkbox value="{{ item1.isChecked }}" checked-color="#00b9c3" data-value="{{index1}}" bind:change="onItemCheck" icon-size="36rpx" disabled="{{ !showOpenSet && acitveIndex !== -1 && acitveIndex != index1}}">
        <view class="warehouseName">{{item1.warehouseName}}({{item1.items.length}})</view>
    </van-checkbox>
    <van-checkbox-group value="{{ item1.result }}" data-value="{{index1}}" bind:change="onSecondItemCheck" disabled="{{  !showOpenSet && acitveIndex !== -1 && acitveIndex != index1}}">
        <view wx:for="{{item1.items}}" wx:key="index2" wx:for-index="index2" wx:for-item="item2" data-value1="{{index1}}" data-value2="{{index2}}">
            <vew class="swip-cell-box">
                <van-checkbox name="{{item2.itemId}}" icon-size="36rpx" custom-class="van-check-good">
                </van-checkbox>
                <view style="flex:1">
                    <van-swipe-cell right-width="{{ 65 }}" left-width="{{ 0 }}">
                    <view class="list-item">
                        <view class="list-item-left">
                            <van-image width="160rpx" fit="contain" height="160rpx" src="{{item2.itemUrl}}" />
                        </view>
                        <view class="list-item-right">
                            <view class="title two-line-ellipsis">{{item2.itemName}}</view>
                            <view class="specs single-line-ellipsis">{{item2.specs}}</view>
                            <view class="item-num">商品编码：{{item2.itemCode}}</view>
                            <view class="stepper-box">
                                <view class="num-box">
                                    <view class="num">{{item2.qtyAvi}}</view><view class="num-txt">库存</view>
                                </view>
                                <van-stepper slot="right-icon" min="0" value="{{ item2.billQty }}" bind:change="onStepperChange" data-index1="{{index1}}" data-index2="{{index2}}" max="{{item2.qtyAvi}}"  button-size="56rpx"/>
                            </view>
                        </view>
                    </view>
                    <view slot="right" class="item-delete" bindtap="onDeleteClick" data-value1="{{index1}}" data-value2="{{index2}}">删除</view>
                </van-swipe-cell>
                </view>
            </vew>
        </view>
    </van-checkbox-group>
</view>