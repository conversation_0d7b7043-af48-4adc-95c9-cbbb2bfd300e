/* components/warehouseCheckBox/index.wxss */
.stock-list-item{
    margin-top: 24rpx;
    padding: 32rpx 0 32rpx 24rpx;
    background: #fff;
}
.warehouseName{
    line-height: 36rpx; 
    color: #3D3D3D;
    font-size: 28rpx;
    font-weight: 500;
}
.swip-cell-box{
    height: 240rpx;
    display: flex;
}
.list-item{
    display: flex;
    margin-left: 10rpx;
}
.van-check-good{
    margin-top: 88rpx;
    width: 68rpx;
}
.list-item-left{
    margin-top: 24rpx;
}
.list-item-right{
    margin-left: 32rpx;
    margin-right: 24rpx;
    flex: 1;
}
.title{
    color: #242424; 
    max-height: 72rpx;
    line-height: 36rpx;
    font-size: 28rpx;
    margin-top: 4rpx;
}
.specs{
  color: #8a8a8a; 
  height: 36rpx;
  line-height: 36rpx;
  font-size: 22rpx;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.two-line-ellipsis {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* qutoprefixer: off */
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap:break-word;
    word-break: break-all;
    white-space: normal;
  }
.stepper-box{
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
}
.num-box{
    height: 56rpx;
    line-height: 56rpx;
}
.num{
    display: inline-block;
    color: #242424;
    font-size: 32rpx;
    font-weight: 500;
}
.num-txt{
    display: inline-block;
    color: #707070;
    font-size: 24rpx;
    font-weight: 400;
    margin-left: 8rpx;
}
.item-delete{
    margin-top: 24rpx;
    text-align: center;
    height: 192rpx !important;
    line-height: 192rpx;
    width: 160rpx;
    color: #fff;
    font-size: 28rpx !important;
    background: #FF4A4D;
}
.item-num{
    color: #707070;
    font-size: 24rpx;
}