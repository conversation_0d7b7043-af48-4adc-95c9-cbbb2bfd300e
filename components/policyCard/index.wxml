<!--components/policyCard/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="root-layout">
  <view class="top-layout flex_v_center">
    <image class="icon" src="/asset/svgs/free.svg"></image>
    <view class="flexbox policy single-line-ellipsis">{{supObj.policyTitle}}</view>
    <timer-count timelong="{{wxsUtil.dataDiff(supObj.systemTime,supObj.policyEndTime)}}" />
  </view>
  <view class="middle-layout flex">
    <view class="flexbox">
      <image wx:for="{{wxsUtil.limitArrLength(supObj.itemUrlList,3)}}" wx:key="index" wx:for-item="itemUrl" class="img"
        src="{{itemUrl}}" mode="aspectFit"></image>
    </view>
    <view class="rightbox" bindtap="onCliclCheck">
      <view class="rightbox-inner">
        <view class="total">共{{supObj.nonGiftItemQty}}件商品</view>
        <view class="check flex_v_center">查看详情<image class="arrow" src="/asset/svgs/left.svg"></image>
        </view>
      </view>
    </view>
  </view>
  <view class="line" />
  <view class="bottom-layout flex">
    <view class="type">赠</view>
    <view class="flexbox">
      <view class="flex" wx:for="{{wxsUtil.limitArrLength(supObj.giftList,3)}}" wx:key="index" wx:for-item="giftObj">
        <view class="itemname">{{wxsUtil.formatItemCode(giftObj.itemCode)}}</view>
        <view class="itemname flexbox single-line-ellipsis">{{giftObj.itemName}}</view>
        <view class="count">x {{giftObj.purchaseQty}}</view>
      </view>
    </view>
  </view>
  <view class="vendor-layout txt ">{{supObj.vendorName}}</view>
</view>