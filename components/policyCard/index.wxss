/* components/policyCard/index.wxss */
.root-layout{
  background-color: #FFFFFF;
}
.top-layout{
  height: 88rpx;
  padding: 0 24rpx;
}
.top-layout .icon{
  width: 32rpx;
  height: 32rpx;
}
.top-layout .policy{
  margin-left: 16rpx;
  font-family: PingFangSC-Medium;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 500;
}
.middle-layout{
  padding:  24rpx;
}
.middle-layout .img{
  width: 170rpx;
  height: 170rpx;
  margin-right: 16rpx;
}
.middle-layout .total{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.middle-layout .rightbox{
  width: 144rpx;
  height: 170rpx;
  position: relative;
}
.middle-layout .rightbox-inner{
  position: absolute;
  right: 0;
  bottom: 0;
}
.middle-layout .check{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.75);
  line-height: 40rpx;
  font-weight: 400;
}
.middle-layout .arrow{
  width: 48rpx;
  height: 48rpx;
}
.bottom-layout{
  padding:  24rpx;
}
.vendor-layout{
  padding: 0 24rpx 24rpx 24rpx;
}
.bottom-layout .type{
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #00b9c3;
  border-radius: 4rpx;
  text-align: center;
  line-height: 36rpx;
  color: #00b9c3;
  font-family: PingFangSC-Regular;
  font-size: 22rpx;
  font-weight: 400;
  margin-right: 16rpx;
}
.bottom-layout .itemname{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.85);
  line-height: 40rpx;
  font-weight: 400;
  margin-right: 16rpx;
}
.bottom-layout .count{
  font-family: SanFranciscoText-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.45);
  line-height: 40rpx;
  font-weight: 400;
}
.line{
  width: 100%;
  height: 1px;
  transform: scaleY(0.5) ;
  background-color: rgba(0,0,0,0.1);
}
.txt {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.flex{
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.flex_v_center {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}