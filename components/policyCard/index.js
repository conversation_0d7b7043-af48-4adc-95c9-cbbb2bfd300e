Component({
  /**
   * 组件的属性列表
   */
  properties: {
    supObj:{
      type:Object,
      value:{}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  observers: {
    'supObj': function(supObj) {
      // 在 numberA 或者 numberB 被设置时，执行这个函数
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onCliclCheck(){
      const supObj=this.data.supObj
      this.triggerEvent('onCheck',{channelCode:supObj.channelCode,policyId:supObj.policyId,productCode:supObj.productCode,vendorCode:supObj.vendorCode})
    },
    dataDiff(){
    }    
  }
})
