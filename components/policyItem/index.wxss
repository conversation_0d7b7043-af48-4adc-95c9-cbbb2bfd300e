.item-box{
  margin:24rpx 24rpx 0;
  background: #fff;
  border-radius: 8rpx;
}
.item-img{
  margin-top: 24rpx;
  margin-left: 24rpx;
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
}
.item-content{
  display: flex;
}
.item-content.content-padding{
  padding-bottom: 24rpx;
}
.item-name{
  max-height: 72rpx;
  margin-top: 24rpx;
  margin-right: 24rpx;
  font-size: 28rpx;
  line-height: 36rpx; 
  color: #242424;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.item-code{
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #707070;
}
.item-specs{
  margin-top: 8rpx;
  font-size: 24rpx;
  line-height: 40rpx; 
  color: #9E9E9E;
}
.item-price-cart-box{
  display: flex;
}
.item-price-box{
  flex: 1;
}
.item-promotionPrice-box{
  display: flex;
  margin-top: 18rpx;
}
.item-promotionPrice{
  display: flex;
  font-size: 32rpx;
  height: 38rpx;
  line-height: 40rpx; 
  color: #FF4A4D;
}
.item-promotionPrice-symbol{
  margin-top: 10rpx;
  font-size: 20rpx;
  line-height: 20rpx;
}
.item-promotionPrice-integer{
  font-size: 32rpx;
  font-weight: 500;
}
.item-promotionPrice-decimal{
  margin-top: 16rpx;
  font-size: 20rpx;
  line-height: 20rpx;
}
.item-right{
  margin-left: 24rpx;
  flex: 1;
}
.item-itemPrice{
  flex: 1;
  margin-top: 8rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #9E9E9E;
  text-decoration: line-through;
}
.item-proportioningQty{
  line-height: 32rpx;
  font-size: 24rpx;
  color: #707070;
  margin-right: 24rpx;
}
.item-cart-box{
  margin-top: 40rpx;
  margin-right: 24rpx;
  width: 80rpx;
  height: 64rpx;
  background: #FE8700;
  border-radius: 8rpx;
}
.item-cart-img{
  margin-top: 12rpx;
  margin-left: 20rpx;
  width: 40rpx;
  height: 40rpx;
}

.item-bottom{
  display: flex;
  justify-content: space-between;
  margin-top: 18rpx;
  padding: 0 24rpx;
  font-size: 24rpx;
  line-height: 70rpx; 
  color: #707070;
  border-top: 2rpx solid #E6E6E6;
}
.item-list .item-content{
  margin-bottom: 8rpx;
}
.groupAmount-box{
  display: flex;
  padding-bottom: 24rpx;
}
.groupAmount-txt{
  margin-top: 24rpx;
  margin-left: 24rpx;
  margin-right: 8rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  color: #707070;
}
.groupAmount-box .item-cart-box{
  margin: 0 24rpx 0 0;
}
.groupAmount-box .item-promotionPrice{
  line-height: 48rpx;
  margin-top: 16rpx;
  flex: 1;
}
.groupAmount-box .item-promotionPrice-symbol{
  margin-top: 20rpx;
}
.groupAmount-box .item-promotionPrice-integer{
  font-size: 32rpx;
  font-weight: 500;
}
.groupAmount-box .item-promotionPrice-decimal{
  margin-top: 20rpx;
}