<!--components/policyItem/index.wxml-->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="item-box">
  <!-- 组合 -->
  <view wx:if="{{itemInfo.policyItemGroupLineResponseDTOList}}">
    <view wx:for="{{itemInfo.policyItemGroupLineResponseDTOList}}" wx:for-item="item" wx:key="index" class="item-list" catchtap="onItemClick" data-value="{{item.itemId}}">
      <view class="item-content">
        <image class="item-img" src="{{item.itemUrl}}" data-value="{{itemInfo.itemId}}"  mode="aspectFit"/>
        <view class="item-right">
          <view class="item-name">商品名称:{{item.itemName}}</view>
          <view class="item-price-cart-box">
            <view class="item-price-box">
              <view class="item-code">商品编码:{{item.itemCode}}</view>
              <!-- <view class="item-specs">商品型号:{{item.specs}}</view> -->
              <view class="item-promotionPrice-box">
                <view class="item-promotionPrice">
                  <view class="item-promotionPrice-symbol">￥</view>
                  <view class="item-promotionPrice-integer">{{wxsUtil.moneyFormatInt(item.promotionPrice, 'int')}}</view>
                  <view class="item-promotionPrice-decimal">{{wxsUtil.moneyFormatInt(item.promotionPrice, 'decimal')}}</view>
                </view>
                <view class="item-itemPrice">
                  <text wx:if="{{promotionType == 3 && item.itemPrice}}">{{item.itemPrice}}</text>
                </view>
                <view class="item-proportioningQty">配比 {{item.proportioningQty}}</view>
              </view>
            </view>
            <!-- <view catchtap="addToCarts" class="item-cart-box">
              <view>配比 2</view>
              <image class="item-cart-img" src="/asset/imgs/carts-white.png" />
            </view> -->
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 单品 -->
  <view wx:else class="item-content {{promotionType == 3 ? '' : 'content-padding'}}" catchtap="onItemClick" data-value="{{itemInfo.itemId}}">
    <image class="item-img" src="{{itemInfo.itemUrl}}" data-value="{{itemInfo.itemId}}"  mode="aspectFit"/>
    <view class="item-right">
      <view class="item-name">商品名称:{{itemInfo.itemName}}</view>
      <view class="item-price-cart-box">
        <view class="item-price-box">
          <view class="item-code">商品编码:{{itemInfo.itemCode}}</view>
          <view class="item-promotionPrice-box">
            <view class="item-promotionPrice">
              <view class="item-promotionPrice-symbol">￥</view>
              <view class="item-promotionPrice-integer">{{wxsUtil.moneyFormatInt(itemInfo.promotionPrice, 'int')}}</view>
              <view class="item-promotionPrice-decimal">{{wxsUtil.moneyFormatInt(itemInfo.promotionPrice, 'decimal')}}</view>
            </view>
            <view class="item-itemPrice"><text wx:if="{{promotionType == 3 && itemInfo.itemPrice}}">{{itemInfo.itemPrice}}</text></view>
          </view>
        </view>
        <view catchtap="addToCarts" class="item-cart-box">
          <image class="item-cart-img" src="/asset/imgs/carts-white.png" />
        </view>
      </view>
    </view>
  </view>
  <view class="item-bottom" wx:if="{{promotionType == 3}}">
    <view>起订量 {{itemInfo.leastQty || 0}}</view>
    <view>单客户限购量 {{itemInfo.singleCustLimit || 0}}</view>
    <view>已购数量 {{itemInfo.totalPurchaseQty || 0}}</view>
  </view>
  <view wx:if="{{itemInfo.policyItemGroupLineResponseDTOList}}">
      <view class="groupAmount-box">
        <view class="groupAmount-txt">组合价</view>
        <view class="item-promotionPrice">
          <view class="item-promotionPrice-symbol">￥</view>
          <view class="item-promotionPrice-integer">{{wxsUtil.moneyFormatInt(itemInfo.groupAmount, 'int')}}</view>
          <view class="item-promotionPrice-decimal">{{wxsUtil.moneyFormatInt(itemInfo.groupAmount, 'decimal')}}</view>
        </view>
        <view catchtap="addToCarts" class="item-cart-box">
          <image class="item-cart-img" src="/asset/imgs/carts-white.png" />
        </view>
      </view>
    </view>
</view>