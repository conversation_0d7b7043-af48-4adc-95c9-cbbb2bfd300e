// components/policyItem/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    itemInfo:{
      type: Object,
      value: {}
    },
    isSup:{
      type: Boolean,
      value: false
    },
    promotionType:{
      type: Number|String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    addToCarts(){
      this.triggerEvent('addToCarts')
    },
    onItemClick(e){
      let itemId = e.currentTarget.dataset.value // 获取传入的参数
      console.log('itemId', itemId, e)
      // 跳转到商品详情页面
      if(this.data.isSup){
        wx.navigateTo({
          url: `/pages_sub/pages/ccs/sup/skuDetail/index?itemId=${itemId}`,
        })
        return
      }
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/down/skuDetail/index?itemId=${itemId}`,
      })
    },
  }
})
