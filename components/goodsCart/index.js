// components/goodsCart/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isSup: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    cartCount: '',
    purchaseType: ''
  },
  pageLifetimes: {
    show() {
      const purchaseType = wx.getStorageSync('vendorInfo').purchaseType
      this.setData({
        purchaseType
      })
      this.getCartsCount()
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onClickToShop() {
      wx.navigateTo({
        url: '/pages_sub/pages/ccs/down/shop/index',
      })
    },
    getCartsCount() {
      const url = '/api/vcs/mobile-web/myx/supOrder/countCartNum'
      const supInfo = wx.getStorageSync('supInfo')
        App.getHttp()
      ._post(url, {
        invoiceSetsOfBooksId: supInfo.setsOfBooksId
      }).then(res => {
        this.setData({
          cartCount: res&&res==0?'':res
        })
      })
    },
  }
})
