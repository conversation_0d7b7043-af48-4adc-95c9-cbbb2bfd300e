// components/footerButton/index.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    btnWord:{
      type:String,
      value:'确认'
    },
    placeholder:{
      type:Boolean,
      value:true
    }
  },
  /**
   * 页面的初始数据
   */
  data: {
    isClick: true,
    loading:false
  },
  methods: {
    handleMainClick: function () {
      if (this.data.isClick) {
        this.setData({
          isClick:false,
          loading:true
        })
        this.triggerEvent('mainClick')
      }
      const tiemr = setTimeout(() => {
        this.setData({
          isClick:true,
          loading:false
        })
        clearTimeout(tiemr)
      }, 800)
    }
  }

  
})