// components/policyItem/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    itemInfo:{
      type: Object,
      value: {}
    },
    promotionType: {
      type: Number,
      value: 0
    },
    isSup:{
      type: Boolean,
      value: false
    }
  },
  lifetimes:{
    ready(){
      this.handleSections()
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentAmount: 0,
    currentPrice: 0,
    sections: [],
    currentIndex: 0,
    singlePercent: 0,
    showDialog: false
  },
  observers: {
    'itemInfo.sections': function(sections) {
      console.log('sections', sections)
      if(!sections){
        return;
      }
      this.setData({
        sections: sections,
        currentPrice: this.data.itemInfo.actualCrowdPrice,
        currentAmount: this.data.itemInfo.actualCrowdQuantity,
      })
      this.handleSections()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleSections(){
      // 计算总段数
      let sections = this.data.sections
      let currentPrice = this.data.currentPrice
      // 一个圆点占据两段
      let segmentsNum = sections.length * 2
      // 计算到达第几段中间位置
      let index = sections.findIndex((item,index)=>{
        return item.beginSection <= this.data.currentAmount && item.endSection >= this.data.currentAmount || (index == sections.length - 1) && item.endSection < this.data.currentAmount
      })
      console.log('index',index, this.data.currentAmount)
      // 计算百分比
      let currSegmsNum = index * 2 + 1
      let percent = (currSegmsNum / segmentsNum).toFixed(4) *100
      let singlePercent = (1 / segmentsNum).toFixed(4) *100
      this.setData({percent,currentIndex: index, singlePercent})
    },
    onItemClick(e){
      let itemId = e.currentTarget.dataset.value // 获取传入的参数
      // 跳转到商品详情页面
      if(this.data.isSup){
        wx.navigateTo({
          url: `/pages_sub/pages/ccs/sup/skuDetail/index?itemId=${itemId}`,
        })
        return
      }
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/down/skuDetail/index?itemId=${itemId}`,
      })
    },
    onInfoClick(e){
      let index = e.currentTarget.dataset.value // 获取传入的参数
      // this.setData({showDialog: true})
      this.triggerEvent('showDialog', this.data.itemInfo)
    },
    addToCarts(){
      this.triggerEvent('addToCarts')
    }
  }
})
