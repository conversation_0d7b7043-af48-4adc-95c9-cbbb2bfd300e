<!--components/policyItem/index.wxml-->
<!-- 众筹样式 -->
<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view wx:if="{{promotionType == 5}}" class="item-zc-box">
  <policy-title dataInfo="{{itemInfo}}" promotionType="{{promotionType}}" />
  <view class="item-content" catchtap="onItemClick" data-value="{{itemInfo.itemId}}">
    <image class="item-img" src="{{itemInfo.itemUrl}}" data-value="{{itemInfo.itemId}}"  mode="aspectFit"/>
    <view class="item-right">
      <view class="item-name">商品名称:{{itemInfo.itemName}}</view>
      <view class="item-price-cart-box">
        <view class="item-price-box">
          <view class="item-code">商品编码:{{itemInfo.itemCode}}</view>
          <view class="item-promotionPrice-box">
            <view class="item-promotionPrice">
              <view class="item-promotionPrice-symbol">￥</view>
              <view class="item-promotionPrice-integer">{{wxsUtil.moneyFormatInt(itemInfo.maxPrice, 'int')}}</view>
              <view class="item-promotionPrice-decimal">{{wxsUtil.moneyFormatInt(itemInfo.maxPrice, 'decimal')}}</view>
            </view>
            <image class="item-img-info" src="/asset/imgs/policy-info.png"  data-value="{{index}}"  catchtap="onInfoClick"/>
            <view class="item-itemPrice">{{itemInfo.normalPrice}}</view>
          </view>
        </view>
        <view catchtap="addToCarts" class="item-cart-box" catchtap="addToCarts">
          <image class="item-cart-img" src="/asset/imgs/carts-white.png" />
        </view>
      </view>
    </view>
  </view>
  <view class="progress-box">
    <view class="progress-th">
      <view class="progress-th-amont">众筹量</view>
      <view class="progress-th-price">价格</view>
    </view>
    <view class="progress-circle-box">
      <view wx:for="{{sections}}" wx:for-item="item" wx:for-key="key" wx:key="index" data-value="{{index}}" class="progress-circle {{currentIndex >= index ? 'progress-circle-active' : ''}}" style="left: {{(index * 2 + 1)*singlePercent + '%'}}">
        <view class="progress-item-amount {{currentIndex >= index ? 'progress-item-amount-active' : ''}}">{{item.beginSection}}</view>
        <view class="item-promotionPrice-box">
          <view class="item-promotionPrice {{currentIndex >= index ? '' : 'progress-item-amount-inactive'}}">
            <view class="item-promotionPrice-integer">{{wxsUtil.moneyFormatInt(item.taxPrice, 'int')}}</view>
            <view class="item-promotionPrice-decimal">{{wxsUtil.moneyFormatInt(item.taxPrice, 'decimal')}}</view>
          </view>
        </view>
      </view>
      <progress percent="{{percent}}" class="progress" stroke-width="2rpx" color="#00b9c3" backgroundColor="#BDBDBD" />
    </view>
  </view>
  <!-- <van-dialog
  wx:if="{{showDialog}}"
  use-slot
  use-title-slot
  show="{{ showDialog }}"
  show-cancel-button="{{false}}"
  confirmButtonText="知道了"
  custom-class="dialog-class"
  confirm-button-class="dialog-confirm"
  >
    <view name="title" class="dialog-title">众筹说明</view>
    <view class="dialog-table">
      <view class="dialog-th">
        <view class="dialog-th-item  first">序号</view>
        <view class="dialog-th-item">起始量</view>
        <view class="dialog-th-item">终止量</view>
        <view class="dialog-th-item">价格(元)</view>
      </view>
      <view class="dialog-tr" wx:for="{{sections}}" wx:for-item="item" wx:key="index">
        <view class="dialog-tr-item first">{{index + 1}}</view>
        <view class="dialog-tr-item">{{item.beginSection}}</view>
        <view class="dialog-tr-item">{{item.endSection}}</view>
        <view class="dialog-tr-item">{{item.taxPrice}}</view>
      </view>
    </view>
    <view class="dialog-num">
      <view>最小众筹数量 {{itemInfo.minCrowdQuantity}}</view>
      <view>最大封顶数量 {{itemInfo.maxCrowdQuantity}}</view>
    </view>
  </van-dialog> -->
</view>
<view wx:else class="item-box">
  <view class="item-title-box two-line-ellipsis">
    <view class="policy-tag {{itemInfo.itemCombType == 1 ? 'tag-single': 'tag-group'}}">{{itemInfo.itemCombTypeName}}</view>{{itemInfo.policyName || itemInfo.name }}
  </view>
  <view class="item-code"><text class="item-code-txt">政策单号：</text>{{itemInfo.policyCode || itemInfo.code }}</view>
  <view class="countDown-box">
    <countDown endTime="{{itemInfo.endDate || itemInfo.endTime}}" startTime="{{itemInfo.startDate || itemInfo.startTime}}"/>
  </view>
  <view class="item-note" wx:if="{{itemInfo.promotionType != 3}}"><text class="item-code-txt">政策描述：</text>{{itemInfo.note}}</view>
  <view class="item-img-box">
    <view class="item-img">
      <image wx:for="{{itemInfo.itemUrlList}}" wx:for-item="item" wx:key="index" data-value="{{index}}" src="{{item}}"  mode="aspectFit"/>
    </view>
    <view class="item-img-txt">共{{itemInfo.totalItemCount}}款商品
      <van-icon name="/asset/imgs/arrow-left.png" custom-style="height: 32rpx; width: 32rpx;transform: rotate(-180deg) !important;" custom-class="item-img-icon" />
    </view>
  </view>

</view>