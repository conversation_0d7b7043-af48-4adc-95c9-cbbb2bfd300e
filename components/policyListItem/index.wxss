/* components/policyItem/index.wxss */
.policy-tag{
  display: inline-block;
  padding: 0 8rpx;
  height: 40rpx;
  font-size: 24rpx;
  text-align: center;
  line-height: 40rpx;
  margin-right: 8rpx;
  border-radius: 8rpx;
}
.tag-single{
  color: #fff;
  background: #FAAE16;
}
.tag-group{
  color: #fff;
  background: #FF4A4D;
}
.item-title-box{
  max-height: 96rpx;
  font-size: 32rpx;
  line-height: 48rpx;
  font-weight: 500;
  color: #242424;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.item-code{
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #242424;
}
.item-code-txt{
  color: #707070;
}
.item-note{
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #FF4A4D;
}
.item-img-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}
.item-box .item-img{
  flex: 1;
}
.item-img image{
  margin-right: 16rpx;
  width: 144rpx;
  height: 144rpx;
  border-radius: 8rpx;
}
.item-img-txt{
  font-size: 24rpx;
  line-height: 40rpx;
  color: #8A8A8A;
}
.item-box{
  background: #fff;
  margin: 24rpx 24rpx 0;
  padding: 24rpx;
}
.countDown-box{
  margin-top: 16rpx;
}
.item-img-icon image{
  font-size: 0 !important;
  line-height: 0 !important;
  margin-top: -4rpx;
}
/* 众筹样式 */
.item-zc-box{
  margin: 24rpx 24rpx 0;
  background: #fff;
  border-radius: 8rpx;
}
.item-zc-box .item-img{
  margin-top: 24rpx;
  margin-left: 24rpx;
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
}
.item-zc-box .item-content{
  display: flex;
}
.item-zc-box .item-name{
  max-height: 72rpx;
  margin-top: 24rpx;
  margin-right: 24rpx;
  font-size: 28rpx;
  line-height: 36rpx; 
  color: #242424;
}
.item-zc-box .two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.item-zc-box .item-code{
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #707070;
}
.item-zc-box .item-specs{
  margin-top: 8rpx;
  font-size: 24rpx;
  line-height: 40rpx; 
  color: #9E9E9E;
}
.item-zc-box .item-price-cart-box{
  display: flex;
}
.item-zc-box .item-price-box{
  flex: 1;
}
.item-zc-box .item-promotionPrice-box{
  display: flex;
  margin-top: 18rpx;
}
.item-zc-box .item-promotionPrice{
  display: flex;
  font-size: 32rpx;
  height: 38rpx;
  line-height: 40rpx; 
  color: #FF4A4D;
}
.item-zc-box .item-promotionPrice-symbol{
  margin-top: 10rpx;
  font-size: 20rpx;
  line-height: 20rpx;
}
.item-zc-box .item-promotionPrice-integer{
  font-size: 32rpx;
  font-weight: 500;
}
.item-zc-box .item-promotionPrice-decimal{
  margin-top: 16rpx;
  font-size: 20rpx;
  line-height: 20rpx;
}
.item-zc-box .item-right{
  margin-left: 24rpx;
  flex: 1;
}
.item-zc-box .item-itemPrice{
  flex: 1;
  margin-top: 8rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
  line-height: 32rpx; 
  color: #9E9E9E;
  text-decoration: line-through;
}
.item-zc-box .item-proportioningQty{
  line-height: 32rpx;
  font-size: 24rpx;
  color: #707070;
  margin-right: 24rpx;
}
.item-zc-box .item-cart-box{
  margin-top: 40rpx;
  margin-right: 24rpx;
  width: 80rpx;
  height: 64rpx;
  background: #FE8700;
  border-radius: 8rpx;
}
.item-zc-box .item-cart-img{
  margin-top: 12rpx;
  margin-left: 20rpx;
  width: 40rpx;
  height: 40rpx;
}
.item-img-info{
  width: 32rpx;
  height: 32rpx;
  margin-top: 4rpx;
  margin-left: 16rpx;
}
/* 进度条样式 */
.progress-box{
  height: 132rpx;
  display: flex;
}
.progress-th{
  width: 88rpx;
  margin-left: 24rpx;
  font-size: 24rpx;
  color: #707070;
}
.progress-th-amont{
  margin-top: 14rpx;
}
.progress-th-price{
  margin-top: 24rpx;
}
.progress-circle-box{
  position: relative;
  margin: 0 32rpx 0 16rpx;
  flex: 1;
}
.progress-circle{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 22rpx;
  height: 22rpx;
  border-radius: 22rpx;
  background: #BDBDBD;
}
.progress-circle-active{
  background: #00b9c3;
}
.progress{
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.progress-item-amount{
  position: absolute;
  top: -48rpx;
  line-height: 40rpx;
  font-size: 24rpx;
  color: #BDBDBD;
}
.progress-item-amount-active{
  color: #242424;
}

.progress-box .item-promotionPrice-box{
  position: absolute;
  bottom: -40rpx;
  display: flex;
}
.progress-box .item-promotionPrice{
  display: flex;
  font-size: 20rpx;
  height: 38rpx;
  line-height: 40rpx; 
  color: #FF4A4D;
}
.progress-box .progress-item-amount-inactive{
  color: #242424;
}
.progress-box .item-promotionPrice-symbol{
  margin-top: 10rpx;
  font-size: 24rpx;
  line-height: 20rpx;
}
.progress-box .item-promotionPrice-integer{
  font-size: 20rpx;
  font-weight: 500;
}
.progress-box .item-promotionPrice-decimal{
  margin-top: 12rpx;
  font-size: 18rpx;
  line-height: 20rpx;
}
