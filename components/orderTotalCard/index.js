// components/orderTotalCard/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    sourceData:{
      type:Object,
      value:{}
    },
    isSaleOrder: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    giftTotal:0,
    giftList:[],
    itemList:[],
    totalAmont:0,
    totalQty:0,
  },
  observers: {
    'sourceData': function(value) {
      if(value.orderLines){
        value.orderLines.forEach(element => {
          // element.applyAmount||element.pric 这俩个为0可能要去掉 TODO
            this.data.itemList.push(element)
          // if(element.isPresent==2||element.applyAmount||element.price){
          //   this.data.itemList.push(element)
          // }else{
          //   this.data.giftTotal+=(element.applyQty||element.qty||0)
          //   this.data.giftList.push(element)
          // }
        });
        this.setData({
          itemList:this.data.itemList,
          giftList:this.data.giftList,
          totalAmont:value.totalAmount,
          totalQty:value.totalQty,
          // giftTotal:this.data.giftTotal
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})
