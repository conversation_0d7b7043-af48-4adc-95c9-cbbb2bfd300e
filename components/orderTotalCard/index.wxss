/* components/orderTotalCard/index.wxss */
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.line{
  width: 100%;
  height: 1rpx;
  background-color: rgba(0,0,0,0.1);
  -webkit-transform-origin: 0 0; 
  transform-origin: 0 0; 
  -webkit-transform: scaleY(0.5); 
  transform: scaleY(0.5)
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.m-t-16{
  margin-top: 16rpx;
}
.m-l-24{
  margin-left: 24rpx;
}
.card-layout{
  background-color: #FFFFFF;
}
.card-layout .good-layout {
  padding: 24rpx;
}
.card-layout .good-layout .itemname {
  margin: 0 16rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}

.card-layout .good-layout .img {
  width: 160rpx;
  height: 160rpx;
}
.good-layout .cart-title {
  height: 80rpx;
  margin-bottom: 10rpx;
}
.card-layout .good-layout .count-box {
  width: 100%;
}

.justify-content-between {
  justify-content: space-between;
}
.card-layout .good-layout .count-box .itemprice {
  display: inline-block;
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: #ee0a24;
  /* text-align: right; */
  line-height: 40rpx;
  font-weight: 500;
}

.card-layout .good-layout .count-box .itemqty {
  display: inline-block;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.75);
  text-align: right;
  line-height: 40rpx;
  font-weight: 400;
}
.card-layout .good-layout .wait-audit {
  text-align: right;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.75);
  text-align: right;
  line-height: 30rpx;
  font-weight: 400;
}
.card-layout .gift-layout{
  background-color: #FFFFFF;
}
.card-layout .gift-layout .bottom-layout{
  padding:  24rpx 24rpx  20rpx 60rpx;
}
.card-layout .gift-layout .bottom-layout .type{
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #00b9c3;
  border-radius: 4rpx;
  text-align: center;
  line-height: 36rpx;
  color: #00b9c3;
  font-family: PingFangSC-Regular;
  font-size: 22rpx;
  font-weight: 400;
  margin-right: 16rpx;
}
.card-layout .gift-layout .bottom-layout .itemname{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 400;
  margin-right: 16rpx;
}
.card-layout .gift-layout .bottom-layout .count{
  font-family: SanFranciscoText-Regular;
  font-size: 28rpx;
  color: rgba(0,0,0,0.45);
  line-height: 44rpx;
  font-weight: 400;
}
.card-layout .gift-layout .up-layout{
  height: 80rpx;
  padding: 0 24rpx;
}
.card-layout .gift-layout .up-layout .icon{
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.card-layout .total-layout {
  padding: 26rpx 24rpx;
  background-color: #FFFFFF;
  text-align: right;
}
.card-layout .total-layout .totaltxt {
  margin-left: 32rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  font-weight: 400;
}
.card-layout .total-layout .totalprice {
  font-family: SanFranciscoText-Medium;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: right;
  line-height: 48rpx;
  font-weight: 500;
}
.txt {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
  font-weight: 400;
}
.font-s28-lh44{
  font-family: PingFangSC-Medium;
  font-size: 28rpx;
  color: rgba(0,0,0,0.85);
  line-height: 44rpx;
  font-weight: 500;
}
.font-s24-lh44{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.45);
  line-height: 44rpx;
  font-weight: 400;
}