<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="card-layout">
    <view class="good-layout" wx:for="{{itemList}}" wx:key="index" wx:for-item="item">
      <view class="flex">
        <image class="img" mode="aspectFit" src="{{item.itemUrl}}"></image>
        <view class=" flexbox itemname two-line-ellipsis">
          <view class="cart-title">{{item.itemName}}</view>
          <view class="flex count-box justify-content-between">
            <view class="itemprice">¥{{item.applyPrice||item.price}}</view>
            <view class="itemqty">×{{item.applyQty||item.qty}}</view>
          </view>
          <view wx:if="{{item.auditQty > 0 && !isSaleOrder}}" class="wait-audit">待审核： {{item.auditQty}}</view>
        </view>
      </view>
      <!-- <view class=" flexbox itemname two-line-ellipsis">{{item.itemName}}</view>
      <view class="count-box">
        <view class="itemprice">¥{{wxsUtil.moneyFormat(item.applyPrice||item.price)}}</view>
        <view class="itemqty">订购数：{{item.applyQty||item.qty}}{{item.uomName||''}}</view>
      </view> -->
      <!-- 销售订单显示数量 -->
      <van-cell-group wx:if="{{isSaleOrder}}">
        <van-cell title="取消数量" value="{{item.cancelQty}}"></van-cell>
        <van-cell title="审核数量" value="{{item.auditQty}}"></van-cell>
        <van-cell title="待审核数量" value="{{item.toAuditQty}}"></van-cell>
      </van-cell-group>
    </view>
    <!-- 赠品 -->
    <view class="gift-layout" wx:if="{{giftTotal>0}}">
      <view class="up-layout flex align-items-center">
        <image class="icon" src="/asset/svgs/free.svg"></image>
        <view class="font-s28-lh44 flexbox ">赠品</view>
        <view class="font-s24-lh44">共{{giftTotal}}件赠品</view>
      </view>
      <view class="line"/>
      <view class="bottom-layout flex">
        <view class="type">赠</view>
        <view class="flexbox">
          <view class="flex" wx:for="{{giftList}}" wx:for-item="giftItem" wx:key="index">
            <view class="itemname">{{wxsUtil.formatItemCode(giftItem.itemCode)}}</view>
            <view class="itemname flexbox single-line-ellipsis">{{giftItem.itemName}}</view>
            <view class="count">x {{giftItem.applyQty||giftItem.qty}}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- <view class="total-layout">
      <text class="txt">共{{totalQty}}件商品</text>
      <text class="totaltxt">合计：</text>
      <text class="totalprice">¥{{wxsUtil.moneyFormat(totalAmont)}}</text>
    </view> -->
  </view>
