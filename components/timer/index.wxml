<!--components/timer/index.wxml-->
<van-count-down use-slot time="{{ timelong }}" bind:change="onChange">
  <text class="txt">距结束</text>
  <text class="item">{{ timeData.days }} </text>
  <text class="txt">天</text>
  <text class="item">{{ timeData.hours }}</text>
  <text class="txt-red">:</text>
  <text class="item">{{timeData.minutes }}</text>
  <text class="txt-red">:</text>
  <text class="item">{{ timeData.seconds }}</text>
</van-count-down>