// components/scanBtns/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    reduceTxt: {
      type: String,
      value: '扫码扣减商品'
    },
    addTxt: {
      type: String,
      value: '扫码添加商品'
    },
    routeName: {
      type: String,
      value: ''
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    openContinueChecked: false
  },
  lifetimes: {
    attached() {
      // 在组件实例进入页面节点树时执行
      const continueScanConfig = wx.getStorageSync('continueScanConfig')||{}
      this.setData({
        openContinueChecked: continueScanConfig[this.data.routeName] || false
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onCheckChange(e) {
      this.setData({
        openContinueChecked: e.detail
      })

      let continueScanConfig = wx.getStorageSync('continueScanConfig')||{}
      continueScanConfig[this.data.routeName] =  e.detail
      wx.setStorageSync('continueScanConfig', continueScanConfig)
    },
    onClickReduceScan() {
      this.triggerEvent('clickReduceScan', { openPDA: this.data.openContinueChecked })
    },
    onClickAddScan() {
      this.triggerEvent('clickAddScan', { openPDA: this.data.openContinueChecked })
    }
  }
})
