<!--components/scanBtns/index.wxml-->
<view class="flex scan-btn-layout align-items-center">
        <view class="flexbox scan-btn" catchtap="onClickReduceScan">{{reduceTxt}}</view>
        <view class="scan-holder"></view>
        <view class="flexbox scan-btn" catchtap="onClickAddScan">{{addTxt}}</view>
        <view class="scan-holder"></view>
        <van-checkbox value="{{ openContinueChecked }}" bind:change="onCheckChange">扫码枪</van-checkbox>
      </view>
