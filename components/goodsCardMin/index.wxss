/* components/goodsCardMin/index.wxss */
.card-layout-v{
  width: 340rpx;
  padding: 16rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}
.card-layout-v .img{
  width: 308rpx;
  height: 308rpx;
}
.good-img-layout {
  position: relative;
}
.rcmd-type {
  position: absolute;
  top: 0;
  left: 0;
  padding: 2px 3px;
  font-size: 13px;
  color: white;
}
.card-layout-h{
  padding: 24rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;
  height: 224rpx;
  border-radius: 8rpx;
}
.card-layout-h .left{
  position: relative;
  height: 176rpx;
}
.card-layout-h .price-layout{
  position: absolute;
  color: #F97D4E;
  bottom: 0;
  right: 0;
  left: 0;
}

.card-layout-h .img{
  width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
}
.card-layout-h .itembox{
  height: 56rpx;
}
.card-layout-h .cart{
  width: 40rpx;
  height: 40rpx;
}
.itemName{
  margin-top: 2rpx;
  font-family:PingFang SC-粗体, PingFangSC-Regular;
  font-size: 28rpx;
  line-height: 28rpx;
  color: #242424;
  font-weight: 400;
  max-height: 56rpx;
}

.good-specs {
  font-size: 24rpx;
  font-weight: 100;
  color: #7f7f7f;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-qty {
  font-size: 28rpx;
  font-weight: 100;
  color: #404040;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-qty-stock {
  margin-right: 20rpx;
}
.itemPrice{
  font-family: SanFranciscoText-Semibold;
  font-size: 36rpx;
  color: #F97D4E;
  line-height: 52rpx;
  font-weight: 600;
}

.price-symbol, .price-rem {
  font-size: 24rpx;
}
.price-text {
  font-size: 32rpx;
}
.itemPrice .itemQty{
  margin-top: 16rpx;
  margin-left: 24rpx;
  font-family: SanFranciscoText-Semibold;
  font-size: 24rpx;
  color: #707070;
  line-height: 42rpx;
  font-weight: 400;
}
.now-purchase {
  width: 120rpx;
  height: 35rpx;
  position: absolute;
  right: 0;
  bottom: 30rpx;
  font-size: 24rpx;
  border: 1px solid #0379ff;
  border-radius: 25rpx;
  text-align: center;
  line-height: 35rpx;
  color: #0379ff;
}
.uom{
  margin-right: 4rpx;
  font-family: SanFranciscoText-Medium;
  font-size: 28rpx;
  color: #F97D4E;
  line-height: 44rpx;
  font-weight: 500;
}
.vendor{
  margin-top: 8rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: rgba(0,0,0,0.45);
  line-height: 20px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.flex_v_center {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
}
.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
/* 水平两边对齐 */
.justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}

