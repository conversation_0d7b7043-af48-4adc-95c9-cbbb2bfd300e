<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="card-layout-h" wx:if="{{isHorizontal}}">
  <view class="flex_v_center">
    <!-- <image class="img" src="{{item.itemUrl||item.picUrl}}" mode="aspectFit" lazy-load /> -->
    <view class="good-img-layout">
      <image class="img" wx:if="{{item.itemUrl}}" src="{{item.itemUrl}}" mode="aspectFit" lazy-load></image>
      <image class="img" wx:else src="/asset/imgs/bg_shop.png" mode="aspectFit" lazy-load></image>
      <view wx:if="{{item.rcmdType === 1 || item.rcmdType === 2}}" class="rcmd-type" style="background:{{item.rcmdType === 1 ? '#FAAE16' : '#FF4A4D'}}">{{ item.rcmdType === 1 ? '新品' : '热卖' }}</view>
    </view>
    <!-- <image class="img" wx:if="{{item.itemUrl}}" src="{{item.itemUrl}}" mode="aspectFit" lazy-load></image>
    <image class="img" wx:else src="/asset/imgs/bg_shop.png" mode="aspectFit" lazy-load></image> -->
    <view class="left flexbox ">
      <view class="itemName  single-line-ellipsis">{{item.itemName}}</view>
      <view class="good-specs">{{item.specs}}</view>
      <view class="good-qty">
        <text class="good-qty-stock">库存：{{item.stockQty}} </text>
        <text wx:if="{{item.isBulkOrder === 2}}">整托数：{{item.itemNumber}}</text>
        <text wx:else> </text>
      </view>
      <view class="price-layout flex_v_center justify-content-between">
        <view class="itemPrice">
          <text wx:if="{{item.beSell == 2}}" class="price-symbol">促销价 </text><text class="price-symbol" space="false" wx:if="{{item.applyPrice}}">¥</text>
          <text class="price-text" space="false">{{item.applyPrice > 0 ? wxsUtil.moneyFormatInt(item.applyPrice, 'int') : '--'}}</text>
          <text class="price-rem" space="false" wx:if="{{item.applyPrice > 0}}">{{wxsUtil.moneyFormatInt(item.applyPrice)}} </text>
        </view>
        <view>
          <van-icon name="star" catchtap="onClickCollect" size="20"></van-icon>
          <!-- <van-icon wx:else name="star-o" catchtap="onClickCollect" size="20"></van-icon> -->
          <image style="vertical-align: top;margin-left: 20rpx;" wx:if="{{showCart}}" class="cart" src="/asset/imgs/cart.png" mode="aspectFit" lazy-load catchtap="addToCarts" />
        </view>
      </view>
    </view>
  </view>
  <view class="line" />
</view>
<view wx:else class="card-layout-v">
  <image class="img" src="{{item.itemUrl||item.picUrl}}" mode="aspectFit" lazy-load />
  <view class="itemName single-line-ellipsis">{{item.itemName}}</view>
  <view class="itemPrice m-t-16"><text class="uom">¥</text>{{wxsUtil.moneyFormat(item.standardPrice)}}</view>
</view>