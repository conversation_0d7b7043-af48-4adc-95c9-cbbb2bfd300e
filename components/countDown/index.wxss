/* components/countDown/index.wxss */
.item-date-box{
  position: relative;
  display: flex;
  font-size: 24rpx;
  line-height: 32rpx;
  color: #242424;
  z-index: 1;
}
.wrap{
  flex-direction: column;
}
.text-right{
  margin-top: 32rpx;
  line-height: 32rpx;
  text-align: right;
}
.time-box{
  display: flex;
}
.item-date-box .time{
  padding: 0 6rpx;
  background: #393E45;
  color: #fff;
  border-radius: 8rpx;
  margin: 0 4rpx;
}
.end-txt{
  margin-right: 4rpx;
  color: #707070;
}
.time-box-margin{
  margin-top: 16rpx;
  margin-left: 48rpx;
}
.flex-right{
  justify-content: flex-end;
}