// components/countDown/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    startTime: {
      type: String,
      value: ''
    },
    endTime:{
      type: String,
      value: ''
    },
    isWrap:{
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // startTime: Date.now() + 1000 * 60 * 60 * 24 * 3,
    // endTime: Date.now() + 1000 * 60 * 60 * 24 * 30,
    countDownDay: '00',
    countDownHour: '00',
    countDownMinute: '00',
    countDownSecond: '00',
    timer: '',
  },
  lifetimes: {
    created: function() {
      // 在组件实例刚刚被创建时执行
      this.setData({
        timer: setInterval(() => {
          this.handleCountDown()
        }, 1000)
      })
    },
    detached: function(){
      clearInterval(this.data.timer)
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleCountDown: function () {
      if(!this.data.endTime){
        return;
      }
      let nowTime = Date.now()
      let startFlag = new Date(this.data.startTime).getTime() - nowTime > 0
      let endTime =  startFlag ? this.data.startTime : this.data.endTime
      let countDownTime = new Date(endTime).getTime() - nowTime >= 0 ? new Date(endTime).getTime() - nowTime : 0
      // console.log('countDownTime', countDownTime)
      let countDownDay = Math.floor(countDownTime / 1000 / 60 / 60 / 24)
      let countDownHour = Math.floor(countDownTime / 1000 / 60 / 60) % 24
      let countDownMinute = Math.floor(countDownTime / 1000 / 60) % 60
      let countDownSecond = Math.floor(countDownTime / 1000) % 60
      countDownDay = this.handleNumFormmat(countDownDay)
      countDownHour = this.handleNumFormmat(countDownHour)
      countDownMinute = this.handleNumFormmat(countDownMinute)
      countDownSecond = this.handleNumFormmat(countDownSecond)
      this.setData({
        countDownDay,
        countDownHour,
        countDownMinute,
        countDownSecond,
        startFlag
      })
    },
    handleNumFormmat(num){
      let newNum = String(num)
      if(newNum.length == 1){
        newNum = '0' + newNum
      }
      return newNum
    }
  }
})
