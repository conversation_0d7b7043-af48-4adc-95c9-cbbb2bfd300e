<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<scroll-view scroll-y lower-threshold="{{50}}" style="height: {{contentViewHeight}}rpx; position: relative;" refresher-default-style="white" refresher-triggered="{{triggered}}" scroll-top='{{topNum}}' scroll-with-animation="{{true}}" bindscrolltolower="onTolower" bindscroll="onPageScroll">
  <view class="root-layout"  id="page-layout">
    <view class="{{showToTop?'filter-layout-search-top':'filter-layout-search'}}" style="background: #ffffff;border-bottom:{{showToTop?'1rpx solid #F0F0F0':'0'}}">
      <!-- <view class="box-layout  flex-box align-center" style="background: #f0f0f0" >
        <van-icon name="/asset/imgs/purchase/search.png" size="20" />
        <view class="search-text">搜索</view>
      </view> -->
      
      <van-search value="{{ keyword }}" placeholder="关键字可模糊搜索(商品名称、编码、型号)" use-action-slot bind:change="onChangeKeyword" bind:search="onConfirm">
        <view slot="action" class="search-right" bind:tap="onConfirm">搜索</view>
      </van-search>
    </view>
    <!-- 顶部区域 -->
    <view class="top-layout" id="top-layout">
      <view wx:if="{{haveAdData}}" class="filter-layout">
        <adSwiper wx:if="{{showAd}}" radiusRpx="{{0}}" bind:onClickAd="onClickAdListener" heightRpx="{{windowWidthRpx}}" bind:noteAd="noteAd"></adSwiper>
      </view>
      <view class="class-layout">
        <van-tag wx:for="{{itemSeries}}" wx:key="index" color="{{item.value !== itemSerie.value ? '#f0f0f0' : '#e6f2fb'}}" text-color="{{item.value !== itemSerie.value ? '#808080' : '#2a72ca'}}" round size="large" custom-class="popup-content-tag-item" type="primary" data-item="{{item}}" bind:tap="clickTag">{{item.name}}</van-tag>
        <!-- <swiper indicator-dots="{{itemSeries.length>1}}" indicator-color="rgba(0,0,0,0.1)" style="height: 350rpx;" indicator-active-color="#00b9c3" autoplay="{{false}}" circular>
          <swiper-item wx:for="{{itemSeries}}" wx:key="index">
            <gridLayout wx:key="index" columnNum="4" iconSize="48px" layoutList="{{item.list}}" />
          </swiper-item>
        </swiper> -->
      </view>
    </view>
    <!-- 热卖-新品 -->
    <van-tabs custom-class="custom-self-class" active="{{ rcmdType }}" nav-class="navStyle" bind:click="onChangeTab">
      <van-tab title="成品" name="1"></van-tab>
      <van-tab title="配件" name="2"></van-tab>
      <van-tab title="促销" name="3"></van-tab>
    </van-tabs>
    <block wx:if="{{recommendList.length > 0}}">
      <view class="goodsList flex-box align-center" wx:for="{{recommendList}}" data-item="{{item}}" bindtap="recommendDetail" wx:key="index">
        <view class="good-img-layout">
          <image class="good-img" wx:if="{{item.itemUrl}}" src="{{item.itemUrl}}" mode="aspectFit"></image>
          <image class="good-img" wx:else src="/asset/imgs/bg_shop.png" mode="aspectFit"></image>
          <view wx:if="{{rcmdType === '1' && (item.rcmdType === 1 || item.rcmdType === 2)}}" class="rcmd-type" style="background:{{item.rcmdType === 1 ? '#FAAE16' : '#FF4A4D'}}">{{ item.rcmdType === 1 ? '新品' : '热卖' }}</view>
          <!-- <image class="good-noQty" wx:if="{{!item.qtyOnhand == '无货'}}" src="/asset/imgs/purchase/stockout.png" mode="aspectFit"></image> -->
        </view>
        <view class="good-info">
          <view class="good-name">{{item.itemName}}</view>
          <!-- <view class="good-specs">规格: {{item.specs}}</view> -->
          <view class="good-specs">规格：{{item.specs}}</view>
          <view class="good-qty">
            <text class="good-qty-stock" wx:if="{{rcmdType !== '2'}}">库存：{{item.stockQty}} </text>
            <text wx:if="{{rcmdType !== '2' && item.isBulkOrder === 2}}">整托数：{{item.itemNumber}}</text>
          </view>
          <view class="">
            <view class="good-price">
              <view wx:if="{{rcmdType !== '2'}}" class="price-info">
                <text wx:if="{{item.beSell == 2}}" class="price-symbol">促销价 </text><text class="price-symbol" space="false" wx:if="{{item.applyPrice}}">¥</text>
                <text class="price-text" space="false">{{item.applyPrice > 0 ? wxsUtil.moneyFormatInt(item.applyPrice, 'int') : '--'}}</text>
                <text class="price-rem" space="false" wx:if="{{item.applyPrice > 0}}">{{wxsUtil.moneyFormatInt(item.applyPrice)}} </text>
              </view>
              <view wx:else class="price-info">
                <text class="price-symbol" space="false"></text>
              </view>
              <!-- <text class="onhand-type type-{{item.qtyOnhand == '无货' ? 1 : 2}}">
                {{item.qtyOnhand}}
              </text> -->
              <view>
                <van-icon wx:if="{{item.isFav === 2 && rcmdType === '1'}}" name="star" data-item="{{item}}" data-index="{{index}}" catchtap="onClickCollect" size="20"></van-icon>
                <van-icon wx:elif="{{rcmdType === '1'}}" name="star-o" data-item="{{item}}" data-index="{{index}}" catchtap="onClickCollect" size="20"></van-icon>
                <van-icon wx:if="{{rcmdType !== '2' && item.applyPrice > 0 && !(rcmdType === '3' && item.stockQty <= 0)}}" style="vertical-align: top;margin-left: 20rpx;" name="/asset/imgs/purchase/good-shopCar.png" data-item="{{item}}" catchtap="onClickToShopCar" size="20"></van-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="none-product" wx:else>
      <no-product noneTxt="暂无商品" />
    </view>
    <add-carts show="{{showAddCartsPop}}"  bind:onchange="onchange" bind:onClose="onCloseAddCarts" item="{{addItemInfo}}" isSup="{{purchaseType == 'sup'}}"></add-carts>
    <view class="scorll-top" style="bottom: {{offsetBottom}}rpx" wx:if="{{showToTop}}" bindtap="toTop">
      <van-icon size="16" color="#00b9c3" name="arrow-up" />
    </view>
  </view>
</scroll-view>