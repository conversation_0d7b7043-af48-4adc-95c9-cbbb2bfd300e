.root-layout,
.grid-content {
  background: rgba(0, 0, 0, 0.04);
}

.top-layout {
  margin-bottom: 4rpx;
}
.flex-box {
  display: flex;
}
.align-center {
  align-items: center;
}
.justify-content-between {
  justify-content: space-between;
}
/* .filter-layout {
  position: relative;
} */
.filter-layout-search-top {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 99;
  padding: 24rpx 32rpx;
  transition: background 0.3s;
}
.filter-layout-search {
  left: 0;
  right: 0;
  z-index: 99;
  padding-right: 32rpx;
  transition: background 0.3s;
}
.box-layout{
  justify-content: center;
  height: 72rpx;
  line-height: 72rpx;
  border-radius: 8rpx;
  transition: background 0.3s;
}
.search-text {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #8a8a8a;
}
.class-layout{
  background-color: #ffffff;
  padding: 20rpx;
}
.middle-layout {
  background-color: #fff;
}
.middle-title {
  padding: 24rpx 24rpx 0 24rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}
.navStyle {
  background-color: #fff;
}

.goodsList {
  font-family: PingFangSC-Regular, PingFang SC;
  margin: 24rpx;
  background: #fff;
  border-radius: 5px;
  overflow: hidden;
}
.good-img-layout {
  position: relative;
}
.good-img {
  width: 224rpx;
  height: 224rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  overflow: hidden;
}
.rcmd-type {
  position: absolute;
  top: 0;
  left: 0;
  padding: 2px 3px;
  font-size: 13px;
  color: white;
}
.good-noQty {
  width: 128rpx;
  height: 128rpx;
  position: absolute;
  left: 44rpx;
  top: 44rpx;
}
.good-info {
  height: 224rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  padding: 10rpx;
}
.good-name {
  font-size: 28rpx;
  font-weight: 400;
  color: #242424;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-specs {
  font-size: 28rpx;
  font-weight: 400;
  color: #7f7f7f;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-qty {
  font-size: 28rpx;
  font-weight: 100;
  color: #404040;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.good-qty-stock {
  margin-right: 20rpx;
}
.good-adjust {
  font-weight: 400;
  color: #9e9e9e;
  font-size: 24rpx;
  align-items: flex-end;
  justify-content: space-between;
}
.good-price {
  color: #F97D4E;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.price-symbol, .price-rem {
  font-size: 24rpx;
}
.price-text {
  font-size: 32rpx;
}
.scorll-top {
  position: fixed;
  right: 64rpx;
  background: #ffffff;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 5px -4px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: center;
}
/* 重写vant样式 */
.van-cell {
  padding: 10rpx 20rpx 10rpx 0 !important;
}
.none-product{
  padding: 164rpx 0;
}
/**重新定义样式background: rgba(0,0,0,0.04);*/
.custom-self-class{
  border-bottom: 1rpx solid transparent !important;
  background-color: #fff;
}

.popup-content-tag-item {
  margin: 0 10rpx 10rpx 0;
}
