// components/downPurchase/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
  },

  /**
   * 组件的初始数据
   */
  data: {
    windowWidthRpx: "375", // 轮播广告高度
    showAd: true, // 控制轮播广告是否展示
    haveAdData: true,
    keyword: '',
    pageIndex: 1,
    pageSize: 10,
    pages: 1,
    offsetBottom: 160, // 回顶按钮下边距
    itemSeries: [],
    itemSerie: {},
    rcmdType: "1",
    recommendList: [],
    contentViewHeight: "1440", // 滑动高度
    addItemInfo: {}, // 添加到购物车的信息
    showAddCartsPop: false,
    showToTop: false,
    topNum: 0,
    totalRcmd: 0,
    supplier: {},
    purchaseType: '' // 从缓存中拿取供应商的采购类型 向上/向下
  },
  pageLifetimes: {
    async show() {
      // await this.init()
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 页面滚动时
    onPageScroll(e) {
      const windowHeight = App.globalData.windowWidth;
      // 滑动超过屏幕的四分之一就显示回顶按钮
      if (e.detail.scrollTop > windowHeight / 4) {
        this.setData({
          showToTop: true,
        });
      } else {
        this.setData({
          showToTop: false,
        });
      }
    },
    // 初始化数据
    async init(event) {
      // 获取供应商类型，选择供应商时写入的字段
      const purchaseType = 'sup'
      let itemSeries = wx.getStorageSync('dictMap').itemSeries
      this.setData({
        purchaseType,
        supplier: event,
        itemSeries,
        rcmdType: "1",
        pageIndex: 1,
        totalRcmd: 0,
      });
      this.data.recommendList = [] //切换时，清空推荐列表数据, 但不立马刷新列表, 立马刷新放到http请求之后
      this.initPage(); // 重绘页面结构布局
      await this.getRecommendList(); // 加载热卖数据
    },
    // 推荐触底事件
    onTolower() {
      this.setData({
        pageIndex: this.data.pageIndex + 1,
      });
      this.getRecommendList();
    },
    noteAd() {
      this.setData({
        haveAdData: false
      })
    },
    // 重绘页面结构
    initPage() {
      const query = wx.createSelectorQuery();
      query.select("#listHead").boundingClientRect();
      let windowHeight = App.globalData.screenHeight;
      let windowWidth = App.globalData.windowWidth;
      query.exec((res) => {
        this.setData({
          windowWidthRpx: windowWidth, // L轮播图高度为屏幕宽度的一半
          contentViewHeight:
            (windowHeight -
              res[0].height -
              12 -
              App.globalData.deviceBottomOccPx - App.globalData.statusBarHeight) *
            App.globalData.pxToRpxRatio, // 计算scroll高度
          offsetBottom:
            (80 + App.globalData.deviceBottomOccPx) *
            App.globalData.pxToRpxRatio,
        });
      });
    },
    // 输入框内容改变
    onChangeKeyword(e) {
      this.setData({
        keyword: e.detail
      })
    },
    onConfirm(e) {
      this.setData({
        pageIndex: 1,
        totalRcmd: 0,
        recommendList: []
      })
      this.getRecommendList();
    },
    // 切换热卖新品导航
    onChangeTab(e) {
      this.setData({
        pageIndex: 1,
        recommendList: [],
        totalRcmd: 0,
        rcmdType: e.detail.name,
      });
      this.getRecommendList();
    },
    // 获取商品列表
    async getRecommendList() {
      if (
        this.data.pageIndex != 1 &&
        this.data.totalRcmd == this.data.recommendList.length
      ) {
        return;
      }
      const sortLista = [
        { fieldName: 'rcmdType', sortOrder: 'ASC' },
        { fieldName: 'orderItemList.summaryApplyQty', sortOrder: 'DESC' },
      ];
      const params = {
        pageIndex: this.data.pageIndex,
        pageSize: this.data.pageSize,
        param: {
          itemSeries: this.data.itemSerie.value || undefined, // 产品细列条件入参
          beSell: this.data.rcmdType === '3' ? 2 : 1, // 是否促销
          beAccessory: this.data.rcmdType === '2' ? 2 : undefined, // 是否配件
          saleOrgCode: this.data.supplier.saleOrgCode,
          channelCode: this.data.supplier.channelCode,
          channelId: this.data.supplier.channelId,
          invoiceCustCode: this.data.supplier.invoiceCustCode,
          invoiceCustId: this.data.supplier.invoiceCustId,
          invoiceSetsOfBooksId: this.data.supplier.invoiceSetsOfBooksId,
          vendorCode: this.data.supplier.vendorCode,
          vendorId: this.data.supplier.vendorId,
          vendorSetsOfBooksId: this.data.supplier.vendorSetsOfBooksId,
          sortList: this.data.rcmdType === '1' ? sortLista : undefined,
          keyword: this.data.keyword,
          supId: this.data.supplier.supId,
          priceListCode: this.data.supplier.priceListCode,
          isFilterPrice: this.data.rcmdType === '2' ? undefined : 2,
        },
      };
      const url = '/api/vcs/order/shopping/page'
      const { recordList: res, totalRecord } = await App.getHttp()._post(
        url,
        params,
        true
      );
      let recommendList = [...this.data.recommendList];
      
      if (res && res.length > 0) {
        this.setData({
          recommendList:
            this.data.pageIndex == 1
              ? [].concat(res)
              : recommendList.concat(res),
          totalRcmd: totalRecord,
        }, () => {
          this.getStock()
        });
      } else if (this.data.pageIndex == 1 && totalRecord == 0) {
        this.setData({
          recommendList: [],
          totalRcmd: totalRecord,
        });
      }
    },
    // 获取库存数据
    async getStock() {
      if (this.data.rcmdType !== '2') {
        const queryParam = [];
        let goodsList = this.data.recommendList
        goodsList.forEach(item => {
          queryParam.push({
            itemCode: item.itemCode,
            orgCode: this.data.supplier.saleOrgCode,
            beSell: item.beSell,
            itemType: this.data.rcmdType === '3' ? '促销' : item.itemTypeName,
          });
        })
        const content = await App.getHttp()._post(
          '/api/interface/erp/stock/query',
          {query:queryParam}
        );
        for (let f = 0; f < goodsList.length; f++) {
          const fItem = goodsList[f];
          const findTrue = content.find((item) => item.itemCode === fItem.itemCode);
          if (findTrue) {
            goodsList[f].stockQty = findTrue.stockQty || 0;
          } else {
            goodsList[f].stockQty = 0;
          }
        }
        this.setData({
          recommendList: goodsList
        })
      }
    },
    // 产品系列切换
    clickTag(event) {
      let item = event.currentTarget.dataset.item
      this.setData({
        itemSerie: item
      }, () => {
        this.getRecommendList()
      })
    },
    // 跳转到推荐商品详情
    recommendDetail(e) {
      const item = e.currentTarget.dataset.item
      this.jumpDetail(item)
      // const supplier = this.data.supplier;
      // let paramString = ''
      // if (this.data.rcmdType === '2') {
      //   paramString = '&beAccessory=2'
      // }
      // Object.keys(supplier).forEach((key) => {
      //   paramString = `${paramString}&${key}=${supplier[key]}`
      // })
      // wx.navigateTo({
      //   url: `/pages_sub/pages/ccs/sup/skuDetail/index?itemId=${item.itemId}${paramString}`,
      // })
    },
    onClickAdListener(event) {
      if (!event.detail.itemId) {
        wx.showToast({
          title: '无商品ID！！',
          icon: 'error'
        })
        return
      }
      this.jumpDetail(event.detail, 'adJump')
    },
    // 跳转商品详情页
    jumpDetail(item, type) {
      const supplier = this.data.supplier;
      let paramString = ''
      if ((this.data.rcmdType === '2') && (type !== 'adJump')) {
        paramString = '&beAccessory=2'
      }
      if ((this.data.rcmdType === '3') && (type !== 'adJump')) {
        paramString = '&rcmdType=3'
      }
      if (!supplier.saleOrgCode) {
        wx.showToast({
          title: '请选择销售组织',
          icon: 'error'
        })
        return
      }
      Object.keys(supplier).forEach((key) => {
        paramString = `${paramString}&${key}=${supplier[key]}`
      })
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/sup/skuDetail/index?itemId=${item.itemId}${paramString}`,
      })
    },
    // 加入购物车
    onClickToShopCar(e) {
      if (this.data.rcmdType === '2') {
        // 跳转配件下单
        return
      }
      let addItemInfo = e.currentTarget.dataset.item;
      addItemInfo.qty = addItemInfo.boxMatchNumber || 1;
      if (this.getTabBar()) {
        this.getTabBar().setData({
          isShow: false,
        });
      }
      this.setData({
        addItemInfo: addItemInfo,
        showAddCartsPop: true,
      });
    },
    // 购物车卡片数据变化
    onchange(v) {
      if (!v.detail) return;
      const reault = v.detail;
      this.setData({
        addItemInfo: reault,
      });
    },
    onCloseAddCarts(e) {
      if (e.detail) {
        this.addShops(e.detail);
      } else {
        this.setData({
          showAddCartsPop: false,
        });
        if (this.getTabBar()) {
          this.getTabBar().setData({
            isShow: true,
          });
        }
      }
    },
    // 添加购物车
    addShops(item) {
      if (item.qty == 0) {
        wx.showToast({
          title: "购买数量不能为0",
          icon: "error",
        });
        return;
      }
      if (item.isBulkOrder === 2 && !item.itemNumber) {
        wx.showToast({
          title: "缺少商品整托数量，请联系管理员!",
          icon: "none",
        });
        return;
      }
      if (item.isBulkOrder === 2 && item.qty % item.itemNumber > 0) {
        wx.showToast({
          title: "整托商品下单数量必须是整托数的整数倍!",
          icon: "none",
        });
        return;
      }
      if (item.beSell === 2 && item.qty > item.stockQty) {
        wx.showToast({
          title: "促销商品下单数量超出了库存数量!",
          icon: "none",
        });
        return;
      }
      const reault = item;
      const supplier = this.data.supplier;
      const params = {
        channelCode: supplier.channelCode, //渠道
        channelId: supplier.channelId, //渠道
        supId: supplier.supId, //协同关系Id
        productCode: reault.productCode || '', //产品组
        saleOrgCode: supplier.saleOrgCode, // 销售组织
        orderType: 0, // 订单类型
        invoiceCustId: supplier.invoiceCustId, // 客户ID
        invoiceCustCode: supplier.invoiceCustCode, // 客户ID
        invoiceSetsOfBooksId: supplier.invoiceSetsOfBooksId, // 客户ID
        vendorSetsOfBooksId: supplier.vendorSetsOfBooksId, // 供应方账套ID
        vendorCode: supplier.vendorCode, // 供应商ID
        vendorId: supplier.vendorId, // 供应商ID
        sourceSystem: 2,
      };
      params.items = [
        {
          itemId: reault.itemId, // 商品ID
          itemName: reault.itemName, // 商品名称
          itemCode: reault.itemCode, // 商品编码
          itemCount: reault.qty, // 购买数量
          orderType: 0,
        }
      ]
      const url = '/api/vcs/mobile-web/myx/supOrder/addToCart'
      App.getHttp()
        ._post(url, params)
        .then((res) => {
          // 触发父组件更新购物车数量
          this.triggerEvent('getCartsCount')
          this.setData({
            showAddCartsPop: false,
          });
          wx.showToast({
            title: "添加成功",
            icon: "success",
            image: "/asset/imgs/purchase/add-success.png",
          });
          if (this.getTabBar()) {
            this.getTabBar().setData({
              isShow: true,
            });
          }
        });
    },
    // 收藏
    async onClickCollect(e) {
      let item = e.currentTarget.dataset.item;
      let index = e.currentTarget.dataset.index;
      let goodsList = this.data.recommendList
      let url = item.isFav === 2 ? '/api/vcs/mobile-web/myx/supOrder/deleteFavoriteItemBySaleOrgCode' : '/api/vcs/mobile-web/myx/supOrder/createFavoriteItem'
      await App.getHttp()._post(
        url,
        {
          ...this.data.supplier,
          itemId: item.itemId,
          itemName: item.itemName,
          itemCode: item.itemCode,
          itemUrl: item.itemUrl,
          specs: item.specs,
          type: item.type,
          brandId: item.brandId,
        }
      );
      goodsList[index].isFav = item.isFav === 2 ? 1 : 2
      this.setData({
        recommendList: goodsList
      })
    },
    toTop() {
      this.setData({
        topNum: 0,
      });
    },
    // 搜索跳转
    onClickSearchListener(e) {
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/${this.data.purchaseType}/searchHis/index`,
      });
    },
  },
});
