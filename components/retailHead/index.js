// components/retailHead/index.js
const App = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showReturn: {
      type: Boolean,
      value: true,
    },
    customTitle: {
      type: String,
      value: '零售'
    }
  },
  pageLifetimes: {
    show() {
      const rect = wx.getMenuButtonBoundingClientRect();
      this.setData({
        windowHeadHeight: rect.height * App.globalData.pxToRpxRatio,
        windowHeadPaddingTop: rect.top * App.globalData.pxToRpxRatio,
        windowHeadPaddingRight: (rect.width + 23) * App.globalData.pxToRpxRatio,
      });
      this.getCheckoutPlatformCount();
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    cartCount: '',
    windowHeadHeight: 64,
    windowHeadPaddingTop: 48,
    windowHeadPaddingRight: 218,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 获取结账台数量
    getCheckoutPlatformCount() {
      const url = "/api/psi/invCheckstand/getRtList";
      const supInfo = wx.getStorageSync("supInfo");
      const curCust = wx.getStorageSync("curCust");
      const params = {
        invoiceSetsOfBooksId: supInfo.setsOfBooksId,
        invoiceCustId: curCust.custId,
      };
      App.getHttp()
        ._post(url, params)
        .then((res) => {
          if (res && res.length > 0) {
            this.setData({
              cartCount: res.length,
            });
          } else {
            this.setData({
              cartCount: '',
            });
          }
        });
    },
    onClickBack() {
      wx.navigateBack({
        delta: 1,
      });
    },
    onClickToShop() {
      wx.navigateTo({
        url: "/pages/ccs/retail/checkoutPlatform/index",
      });
    },
  },
});
