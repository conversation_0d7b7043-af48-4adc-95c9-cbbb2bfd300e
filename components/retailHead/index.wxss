/* components/retailHead/index.wxss */
.head-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 24rpx;
  padding-bottom: 12rpx;
  background-color: #fff;
  font-size: 36rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 44rpx;
}
.head-right {
  display: flex;
  align-items: center;
  color: #242424;
}
.head-left {
  display: flex;
  align-items: center;
}
.head-title {
  margin-left: 18rpx;
}
.head-info {
  right: -116rpx !important;
}
