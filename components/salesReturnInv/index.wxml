<wxs src="./index.wxs" module="wxsUtil"></wxs>
<view class="item-content" wx:if="{{itemObj.billNo}}">
  <view class=" item-header">
    <view>
      <view class="item-bill">订单号: {{itemObj.billNo}}</view>
      <view class=" item-cust">{{itemObj.invoiceCustName}}</view>
    </view>
    <block wx:if="{{showHeadRight}}">
      <view class="source-sys" wx:if="{{isAudit}}">
        {{itemObj.sourceSystemName}}
      </view>
      <view wx:else>
        <view class="item-status item-confirm-{{itemObj.isConfirm}}">{{wxsUtil.orderStatusFormat(itemObj.isConfirm)}}</view>
      </view>
    </block>
  </view>
  <van-collapse value="{{ collapseNames }}" bind:change="onChangeCollape">
    <view class="item-goods " wx:for="{{itemObj.addLineList}}" wx:for-item="good" wx:for-index="goodIndex" wx:key="id">
      <view class="goodLayout">
        <image class="good-img" src="{{good.itemUrl}}" mode="aspectFit"></image>
        <view class="good-info">
          <view class="info-name-box">
            <view class="name">{{good.itemName}}</view>
            <view class="specs">{{good.specs}}</view>
          </view>
          <view class="info-count">
            <view class="info-price">
              ￥ {{good.pricecBillF}}
            </view>
            <view wx:if="{{isAudit}}" class="info-outNum">
              <text class="">退货数量{{good.billQty}}件</text>
            </view>
            <view wx:else class="info-outNum">
              <text class="">共{{good.applyQty || good.billQty}}件</text>
            </view>
          </view>
        </view>
      </view>
      <view wx:if="{{isAudit}}">
        <van-field readonly input-align="right" right-icon="arrow" placeholder="请选择仓库" label="出库仓库" value="{{good.warehouseName}}" data-id="{{good.id}}" border="{{false}}" bind:click-icon="checkkWarehouse" bind:click-input="checkkWarehouse" />
        <van-field label="确认数量" use-button-slot input-align="right">
          <van-stepper slot="button" value="{{ good.billQty }}" min="{{0}}" max="{{good.billQty}}" data-id="{{good.id}}" bind:change="onChangeOrderAudit" disabled/>
        </van-field>
        <van-collapse-item name="{{'collapse'+index}}" wx:if="{{good.barCodeFlowResponseDTOList&&showCollapse}}">
          <view slot="title">已扫码信息({{good.barCodeFlowResponseDTOList.length}})</view>
          <view slot="value">时间</view>
          <view class="flex font-sub-hint m-b-12" wx:for="{{good.barCodeFlowResponseDTOList}}" wx:for-item="barCodeItem" wx:for-index="barCodeIndex" wx:key="barCodeIndex">
            <view class="bar-code">条码：{{barCodeItem.barCode}} 扫码时间：{{barCodeItem.createTime}}</view>
          </view>
        </van-collapse-item>
      </view>
    </view>
  </van-collapse>
  <!-- 合计 -->
  <view class="total-block" wx:if="{{showAmount}}">
    <text class="total-title">合计:</text>
    <text class="total-text">￥{{itemObj.totalAmount}}</text>
  </view>
  <!-- 预留插槽 -->
  <slot name="footer"></slot>
</view>