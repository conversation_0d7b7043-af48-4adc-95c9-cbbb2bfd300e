// components/salesReturnInv/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    itemObj: {
      type: Object,
      value: {},
      observer: function (newVal, oldVal) {
        if (newVal.id) {
          this.setData({
            confirmLineList: newVal.addLineList.map((item) => {
              return {
                id: item.id,
                confirmReqQty: item.billQty,
                warehouseId:item.warehouseId,
                warehouseName:item.warehouseName,
                price:item.pricecBillF
              };
            }),
          });
        }
      },
    },
    isAudit: {
      type: Boolean,
      value: false,
    },
    showHeadRight: {
      type: Boolean,
      value: true
    },
    showAmount: {
      type: Boolean,
      value: true
    },
    showCollapse: {
      type: Boolean,
      value: false
    },
  },
  // 组件配置
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的初始数据
   */
  data: {
    confirmLineList: [],
    collapseNames: [],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 修改确认数量
    onChangeOrderAudit(e) {
      const id = e.currentTarget.dataset.id;
      let isInListIdx = this.data.confirmLineList.findIndex(
        (item) => item.id == id
      );
      let newArr = [...this.data.confirmLineList];
      if (isInListIdx != -1) {
        newArr[isInListIdx].confirmReqQty = e.detail;
      }
      this.setData({
        confirmLineList: newArr
      })
      this.triggerEvent("changeCount", newArr);
    },
     // 获取仓库列表
     getWarehouseColumns(){
    
      // 选择仓库
      App.getHttp()._post('/api/psi/baseWarehouse/page', {
        pageIndex: 1,
        pageSize: 200,
        param: {
          state: 2
        }
      }).then(res => {
        let warehouseColumns = []
        for (let r = 0; r < res.length; r++) {
          warehouseColumns.push({
            text: res[r].name,
            id: res[r].id
          })
        }
        this.setData({
          warehouseColumns: warehouseColumns,
        })
      })
     },
     checkkWarehouse(e) {
      this.setData({
        show: true
      })
    },
  },
});
