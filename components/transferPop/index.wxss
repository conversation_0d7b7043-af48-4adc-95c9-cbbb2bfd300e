/* components/transferPop/index.wxss */
.pop-title{
    margin-top: 24rpx;
    text-align: center;
    font-size: 32rpx;
    color:#242424;
    line-height: 48rpx;
}
.list-item{
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
}
.image-custom-class{
    margin-left: 48rpx;
}
.list-item-right{
    margin-left: 32rpx;
    flex: 1;
}
.title{
    color: #242424;
    line-height: 36rpx;
    height: 72rpx;
    font-size: 28rpx;
}
.specs{
  margin-top: 12rpx;
  font-size: 24rpx;
  line-height: 36rpx;
  height: 36rpx;
  color: #8A8A8A;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.two-line-ellipsis {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* qutoprefixer: off */
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap:break-word;
    word-break: break-all;
    white-space: normal;
}
.item-num{
    height: 64rpx;
    line-height: 64rpx;
    margin-top: 16rpx;
    color: #242424;
    font-size: 32rpx;
}
.num-box{
    margin-top: 36rpx;
    display: flex;
    justify-content: space-between;
}
.num-txt{
    margin-left: 56rpx;
    font-size: 32rpx;
    color: #3D3D3D
}
.btn-box{
    margin-top: 124rpx;
    height: 96rpx;
}
.btn-class{
    display: block !important;
    margin: 8rpx 24rpx 0 24rpx!important;
    height: 80rpx!important;
    line-height: 80rpx!important;
    border-radius: 8rpx!important;
    font-size: 32rpx!important;
    padding: 0 !important;
}
.stepper-class{
    margin-right: 48rpx;
}