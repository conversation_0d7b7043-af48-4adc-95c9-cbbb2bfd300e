<!--components/transferPop/index.wxml-->
<van-popup show="{{ showPop }}" bind:close="onClose" closeable position="bottom" round custom-class="pop-class">
    <view class="pop-title">加入调整台</view>
    <view class="list-item">
        <view class="list-item-left">
            <van-image width="160rpx" height="160rpx" fit="contain" src="{{itemInfo.itemUrl}}" custom-class="image-custom-class"/>
        </view>
        <view class="list-item-right">
            <view class="title two-line-ellipsis">{{itemInfo.itemName || itemInfo.name}}</view>
            <view class="specs single-line-ellipsis">{{itemInfo.specs}}</view>
            <view class="item-num">商品编码：{{itemInfo.itemCode || itemInfo.code}}</view>
            <!-- <view class="item-btn">
                <van-button plain round type="info" size="mini">加入调整台</van-button>
            </view> -->
        </view>
    </view>
    <!-- <van-cell title="入库单价" value="{{'￥999'}}" /> -->
    <view class="num-box">
        <view class="num-txt">数量</view>
        <van-stepper slot="right-icon" value="{{ itemInfo.qty }}" min="0" bind:change="onStepperChange" custom-class="stepper-class" button-size="56rpx"/>
    </view>
    <!-- <van-field label="备注" value="{{ itemInfo.note }}" placeholder="请输入" border="{{ false }}" bind:change="onNoteChange" /> -->
    <view class="btn-box">
        <van-button type="info" round bindtap="submitBtnClick" custom-class="btn-class">提交</van-button>
    </view>
</van-popup>