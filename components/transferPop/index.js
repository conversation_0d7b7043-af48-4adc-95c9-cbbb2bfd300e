// components/transferPop/index.js
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        showPop:{
            type: Boolean,
            value: false,
        },
        itemInfo:{
            type: Object,
            value: {}
        }
    },

    /**
     * 组件的初始数据
     */
    data: {

    },

    /**
     * 组件的方法列表
     */
    methods: {
        onStepperChange(e){
            let itemInfo = this.data.itemInfo
            itemInfo.qty = e.detail
            this.setData({
                itemInfo: itemInfo, 
            })
        },
        onNoteChange(e){

        },
        submitBtnClick(){
            this.triggerEvent('submitBtnClick', this.data.itemInfo)
        },
        onClose(){
            this.triggerEvent('onPopClose')
        }
    }
})
