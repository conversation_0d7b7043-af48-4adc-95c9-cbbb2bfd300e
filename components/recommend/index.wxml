<!--components/recommend/index.wxml-->
<view>
  <van-sticky offset-top="0">
    <van-tabs ellipsis="{{false}}" bindchange="chooseBrand">
      <van-tab wx:for="{{brandList}}" wx:key="index" title="{{item.itemName}}" name="{{item.itemValue}}" />
    </van-tabs>
  </van-sticky>
  <!-- 产品推荐 -->
  <view class="new-product">
    <view class="title flex_center ">
      <view class="inner">
        <image src="/asset/svgs/home-title-bg.svg" class="img"/>
        <text class="txt">新品推荐</text>
      </view>
    </view>
    <view wx:if="{{newProductList.length > 0}}" class="content">
      <goods-card wx:for="{{newProductList}}" wx:key="index" vertical show-cart="false" item="{{item}}"
        bindtap="pushDetail" data-item="{{item}}" data-type="2" />
    </view>
    <!-- 缺省 -->
    <view class="none" wx:else>
      <no-product noneTxt="暂无新品推荐" sizeRpx="170" />
    </view>
  </view>
  <view class="new-product">
    <view class="title flex_center ">
      <view class="inner">
        <image src="/asset/svgs/home-title-bg.svg" class="img"/>
        <text class="txt">热卖推荐</text>
      </view>
    </view>
    <view wx:if="{{hotProductList.length > 0}}" class="content">
      <goods-card wx:for="{{hotProductList}}" wx:key="index" vertical :show-cart="false" item="{{item}}"
        bindtap="pushDetail" data-item="{{item}}" data-type="2" />
    </view>
    <!-- 缺省 -->
    <view class="none" wx:else>
      <no-product noneTxt="暂无热卖推荐" sizeRpx="170" />
    </view>
  </view>
</view>