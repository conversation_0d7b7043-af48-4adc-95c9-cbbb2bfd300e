// components/recommend/index.js
const App = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    newProductList: {
      type: Array,
      value: []
    },
    hotProductList: {
      type: Array,
      value: []
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    brandList:[]
  },

  created() {
    this.getProductList(1)
    this.setData({
      brandList:App.getDict('itemSpecies',[]).filter(res=>![2,3,4,6].includes(res.itemValue))
    })
  },

  /**
   * 组件的方法列表
   */
  methods: {
    pushDetail(e) {
      const item = e.currentTarget.dataset.item
      wx.navigateTo({
        url: `/pages_sub/pages/ccs/down/skuDetail/index?itemId=${item.itemId}&vendorCode=${item.vendorCode}&custCode=${item.custCode}&channelCode=${item.channelCode}&productCode=${item.productCode}`,
      })
    },
    chooseBrand(e) {
      this.getProductList(e.detail.name)
    },
    getProductList(itemSpecie) {
      //1-新品推荐，2-爆款热卖
      let newParam = {
        pageIndex: 1,
        pageSize: 999,
        param: {
          species: itemSpecie,
          custCode:wx.getStorageSync('mastCode'),
          rcmdType: 1
        }
      }
      App.getHttp()._post('myx/ccs-mobile-web/homepage/rcmdSetting/getRecommendProductInfo', newParam).then(res => {
        this.setData({
          newProductList: res
        })
      })
      let hotParam = {
        pageIndex: 1,
        pageSize: 999,
        param: {
          species: itemSpecie,
          custCode:wx.getStorageSync('mastCode'),
          rcmdType: 2
        }
      }
      App.getHttp()._post('myx/ccs-mobile-web/homepage/rcmdSetting/getRecommendProductInfo', hotParam).then(res => {
        this.setData({
          hotProductList: res
        })
      })
    }
  }
})