/* components/recommend/index.wxss */
.new-product {
  margin: 32rpx 24rpx;
}

.new-product .title {
  height: 48rpx;
  margin-bottom: 24rpx;
}
.new-product .title .inner{
  position: relative;
  width: 232rpx;
  height: 48rpx;
}
.new-product .title .inner .img{
  width: 232rpx;
  height: 48rpx;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.new-product .title .inner .txt{
  line-height: 48rpx;
  font-size: 32rpx;
  color: rgba(0,0,0,0.85);
  width: 232rpx;
  height: 48rpx;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  text-align: center;
}

.new-product .content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.new-product .none{
  padding: 60rpx 0;
  margin-top: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
}
.flex_center {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
}