/* components/goodsCardNew/index.wxss */
.good-card {
  background-color: #fff;
  margin: 24rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  font-family: PingFangSC-Medium, PingFang SC;
}
.title {
  font-weight: 500;
  color: #242424;
  font-size: 32rpx;
  line-height: 48rpx;
}
.custom-title {
  margin-right: 10rpx;
}
.good-content {
  margin-top: 32rpx;
}
.good-status {
  font-size: 24rpx;
  font-weight: 400;
  width: 88rpx;
  line-height: 40rpx;
  border-radius: 8rpx;
  padding: 0 8rpx;
  text-align: center;
  height: 40rpx;
  color: #52c718;
  background-color: #f5ffeb;
}
.good-status-2,
.good-status-3{
  color: #00b9c3;
  background-color: #e7f2ff;
}
.good-status-5{
  color: #faae16;
  background-color: #fffbe6;
}

.good-status-99 {
  color: #242424;
  background-color: #f0f0f0;
}
.toDeliver  {
  color: #00b9c3;
  background-color: #e7f2ff;
}
.toSign {
  color: #faae16;
  background-color: #fffbe6;
}
.good-img {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}
.content-right {
  flex-direction: column;
}
.supplier {
  color: #707070;
  line-height: 36rpx;
  font-size: 28rpx;
  font-weight: 400;
  margin-bottom: 32rpx;
}
.good-info {
  flex: 1;
  justify-content: space-between;
  align-items: flex-end;
}
.good-name-box{
  flex:1;
  font-weight: 400;
}
.good-name {
  color: #242424;
  line-height: 36rpx;
  font-size: 28rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-specs {
  color: #808080;
  line-height: 36rpx;
  font-size: 24rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  white-space: normal;
}
.good-price {
  width: 126rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: #242424;
  font-weight: 500;
}
.price-symbol,
.price-rem {
  font-size: 20rpx;
}
.price-text {
  font-size: 32rpx;
}
.good-qty {
  font-size: 28rpx;
}
.expand-text {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 40rpx;
}
.action-block {
  justify-content: flex-end;
}
.m-t-16{
  margin-top: 16rpx;
}
.action-btn {
  padding: 10rpx 24rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 44rpx;
  background-color: #fff;
  color: #242424;
  border-radius: 8rpx;
  border: 1px solid #DBDBDB;
  margin-left: 16rpx;
}
.scan-rate{
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00b9c3;
}
.confirm {
  border: none;
  background-color: #00b9c3;
  color: #fff;
}
.qty-block {
  background: #F9F9F9;
  border-radius: 8rpx;
  width: 100%;
  justify-content: space-around;
  padding: 10rpx 0;
  margin-top: 24rpx;
}
.detail-state-item {
  font-size: 28rpx;
  line-height: 44rpx;
  display: flex;
  align-items: center;
}
.detail-state-qty {
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  font-size: 28rpx;
  margin-left: 8rpx;
}
.state-text {
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #9E9E9E;
}
.activeState {
  color: #00b9c3;
}
.popup-head {
  position: relative;
  padding: 24rpx 0;
  justify-content: center;
}
.closeIcon {
  position: absolute;
  right: 32rpx;
}
.popup-content {
  padding: 0 24rpx;
}
.popup-supplier {
  display: flex;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #707070;
  line-height: 36rpx;
  margin-bottom: 8rpx;
}
.left-label {
  margin-left: 10rpx;
}
.left-label::before {
  content: "*";
  color: red;
  position:absolute;
  font-size: 28rpx;
  left: -8rpx;
  
}
.popup-footer {
  justify-content: space-around;
  position: fixed;
  bottom: 40rpx;
  width: 100%;
}
.footer-btn {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242424;
  line-height: 48rpx;
  border-radius: 8rpx;
  padding: 16rpx 130rpx;
  border: 1px solid #DBDBDB;
}
.btn-confirm {
  background: #00b9c3;
  color: #fff;
  border: none;
}
.supInfo{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expand-block {
  padding-top: 24rpx;
}
.van-field__label::before {
  content: "*"!important;
  color: red!important;
  position:absolute!important;
  font-size: 14px!important;
}
