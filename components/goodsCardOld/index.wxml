<wxs src="/wxs/common.wxs" module="wxsUtil"></wxs>
<view class="good-card" wx:if="{{goodData.id || orderHead.id}}" bindtap="onClickGood">
    <view class="good-head flex align-items-center justify-content-between">
        <view class="title">
            <text class="custom-title">{{headTitle}}</text>
            <text>{{dataType == 1 || dataType == 3 ? orderHead.poNo : (goodData.billNo || goodData.orderNo)}}</text>
        </view>
        <view wx:if="{{showState}}" class="good-status good-status-{{dataType == 1 ? orderHead.state : goodData.state}} {{orderHead.queryStatName == '待签收' ? 'toSign' : ''}} {{orderHead.queryStatName == '待发货' ? 'toDeliver' : ''}}">
            {{dataType == 1 || dataType == 3 ? orderHead.queryStatName : (goodData.stateName || wxsUtil.formatState(goodData.state, stateDict))}}
        </view>
    </view>
    <view class="good-content"  wx:for="{{dataType == 1 || dataType == 3 ? (showExpandBlock ? (wxsUtil.formatGoodList(showExpand,orderLines, 3)) : orderLines) : (showExpandBlock ? wxsUtil.formatGoodList(showExpand,goodData.addLineList, 3) : goodData.addLineList)}}" wx:key="index">
        <block wx:if="{{dataType == 3}}">
            <view class="flex" wx:for="{{item.outLines}}" wx:key="index" wx:for-item="outItem">
                <image class="good-img" mode="aspectFit" src="{{item.itemUrl}}"></image>
                <view class="content-right flexbox flex">
                    <view class="supplier">{{orderHead.vendorName}}</view>
                    <view class="good-info flex" wx:if="{{showAllInfo}}">
                        <view class="good-name-box">
                          <view class="good-name">{{outItem.itemName}}</view>
                          <view class="good-specs">{{outItem.specs}}</view>
                        </view>
                        <view class="good-price ">
                            <view>
                                <text class="price-symbol" space="false">¥</text>
                                <text class="price-text" space="false">{{wxsUtil.moneyFormatInt(outItem.pricecBillF, 'int')}}</text>
                                <text class="price-rem" space="false">{{wxsUtil.moneyFormatInt(outItem.pricecBillF)}}</text>
                            </view>
                            <view class="flex align-items-center">
                                <van-icon size="12" color="#9E9E9E" name="cross" />
                                <view class="good-qty">{{outItem.billQty}}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="flex align-items-center justify-content-between m-t-16">
              <view class="scan-rate">
                <text  wx:if="{{item.outHead.sunQty}}" >扫码进度：{{wxsUtil.formatPercent(item.outHead.scanRate,'%')}} ({{item.outHead.sunScanQty}}/{{item.outHead.sunQty}})</text>
              </view>
              <view class="flexbox flex  action-block">
                  <!-- <view class="action-btn" catchtap="onClickToSign" data-out-lines="{{item.outLines}}" data-type="{{1}}" data-id="{{orderHead.id}}" data-out-head="{{item.outHead}}">
                      差异签收
                  </view> -->
                  <view class="action-btn confirm" catchtap="onClickToSign" data-out-lines="{{item.outLines}}" data-type="{{2}}" data-id="{{orderHead.id}}" data-out-head="{{item.outHead}}">
                      签收
                  </view>
                </view>
            </view>
        </block>
        <block wx:else>
            <view class="supInfo" wx:if="{{isSup}}">
                <view class="supplier">{{goodData.channelName}}</view>
                <view class="supplier">{{goodData.saleOrgName}}</view>
            </view>
            <view class="flex">
                <image class="good-img" mode="aspectFit" src="{{item.itemUrl}}"></image>
                <view class="content-right flexbox flex">
                    <view class="supplier">
                        {{dataType == 1 ? orderHead.vendorName : goodData.vendorName}}
                    </view>
                    <view class="good-info flex" wx:if="{{showAllInfo}}">
                      <view class="good-name-box">
                          <view class="good-name">{{item.itemName}}</view>
                          <view class="good-specs">{{item.specs}}</view>
                        </view>
                        <view class="good-price ">
                            <view>
                                <text class="price-symbol" space="false">¥</text>
                                <text class="price-text" space="false">{{wxsUtil.moneyFormatInt(item.applyPrice, 'int')}}</text>
                                <text class="price-rem" space="false">{{wxsUtil.moneyFormatInt(item.applyPrice)}}</text>
                            </view>
                            <view class="flex align-items-center">
                                <van-icon size="12" color="#9E9E9E" name="cross" />
                                <view class="good-qty">{{item.applyQty}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="good-name" wx:else>{{item.itemName}}</view>
                </view>
            </view>
            <view class="flex align-items-center qty-block" wx:if="{{showQty}}">
                <view class="detail-state-item qty-item">
                    <view class="state-text {{orderHead.state == 2 && orderHead.queryStatName == '待审'  ? 'activeState' : ''}}">
                        待审核
                    </view>
                    <view class="detail-state-qty {{orderHead.state == 2 && orderHead.queryStatName == '待审' ? 'activeState' : ''}}">
                        {{item.toAuditQty}}
                    </view>
                </view>
                <view class="detail-state-item qty-item ">
                    <view class="state-text {{orderHead.isUndelivered == 2 ? 'activeState' : ''}}">
                        待发货
                    </view>
                    <view class="detail-state-qty {{orderHead.isUndelivered == 2 ? 'activeState' : ''}}">
                        {{item.toDeliveryQty}}
                    </view>
                </view>
                <view class="detail-state-item qty-item">
                    <view class="state-text {{orderHead.isUnsigned == 2 ? 'activeState' : ''}}">
                        待签收
                    </view>
                    <view class="detail-state-qty {{orderHead.isUnsigned == 2 ? 'activeState' : ''}}">
                        {{item.toSignedQty}}
                    </view>
                </view>
                <view class="detail-state-item qty-item">
                    <view class="state-text {{orderHead.isCompleted == 2 ? 'activeState' : ''}}">
                        已完成
                    </view>
                    <view class="detail-state-qty {{orderHead.isCompleted == 2 ? 'activeState' : ''}}">
                        {{item.signedQty}}
                    </view>
                </view>
            </view>
            <view class="flex align-items-center qty-block" wx:if="{{showReturnQty}}">
                <view class="detail-state-item qty-item">
                    <view class="state-text">
                        退货数量
                    </view>
                    <view class="detail-state-qty">
                        {{item.applyQty}}
                    </view>
                </view>
                <view class="detail-state-item qty-item ">
                    <view class="state-text">
                        申请退货单价
                    </view>
                    <view class="detail-state-qty">
                        {{item.applyPrice}}
                    </view>
                </view>
            </view>
        </block>
    </view>
    <slot name="amount"></slot>
    <slot name="footer-btn"></slot>
    <view class="expand-block" wx:if="{{dataType != 3 && showExpandBlock && (orderLines.length > 3 || goodData.addLineList.length > 3)}}">
        <view wx:if="{{showExpand}}" class="flex align-items-center justify-content-center" catchtap="onClickExpand">
            <view class="expand-text">查看全部{{orderLines.length || goodData.addLineList.length}}类商品</view>
            <van-icon size="12" color="#707070" name="arrow-down" />
        </view>
        <view wx:else class="flex align-items-center justify-content-center" catchtap="onClickExpand">
            <view class="expand-text">收起</view>
            <van-icon size="12" color="#707070" name="arrow-up" />
        </view>
    </view>
</view>