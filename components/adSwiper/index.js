// components/adSwipe/index.js
const App = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    autoplay: {
      type: Boolean,
      value: true,
    },
    indicatorDots: {
      type: Boolean,
      value: true,
    },
    indicatorCount: {
      type: Boolean,
      value: false,
    },
    heightRpx: {
      type: Number,
      value: 272,
    },
    radiusRpx: {
      type: Number,
      value: 16,
    },
    adType: {
      type: Number,
      value: 1,
    },
    indicatorActive: {
      type: String,
      value: "#FFFFFF",
    },
    adList: {
      type: Array,
      value: [],
    },
    autoHttp: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    current: 0,
  },
  attached() {
    this.getSwiperData()
  },
  pageLifetimes: {
    show() {
      if (this.data.autoHttp) {
        this.getSwiperData()
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    getSwiperData() {
      App.getHttp()
          ._post(
            "/api/mms/adSetting/myx/page",
            // { custCode:wx.getStorageSync('mastCode')}
            {
              pageIndex: 1,
              pageSize: 5,
              param: {
                // sortName: "",
                // sortType: "",
                // id: 0,
                // isPush: 0,
                // adName: "",
                adType: this.data.adType,
                // isDel: 0
              },
            }
          )
          .then((res) => {
            if (res && res.length > 0) {
              this.setData({
                adList: res,
              });
            } else {
              this.triggerEvent('noteAd')
            }
          });
    },
    onClickAd(even) {
      // const vendorInfo = wx.getStorageSync("vendorInfo");
      const item = this.data.adList[even.currentTarget.dataset.index];
      this.triggerEvent('onClickAd', item)
      // if (item.policyId && item.policyType == 10) {
      //   const queryParam = {
      //     channelCode: item.channelCode,
      //     policyCType: item.policyCType,
      //     policyId: item.policyId,
      //     productCode: item.productCode,
      //     vendorCode: item.vendorCode,
      //   };
      //   const url = vendorInfo.purchaseType == 'sup' ? '/pages_sub/pages/ccs/sup/policy/single/index' : '/pages_sub/pages/ccs/down/policy/single/index'
      //   wx.navigateTo({
      //     url:
      //       `${url}?query=` +
      //       JSON.stringify(queryParam),
      //   });
      // } else if (item.adType === 2) {
      //   if (item.outsideUrl) {
      //     const url = JSON.stringify(item.outsideUrl);
      //     wx.navigateTo({
      //       url: `/pages/webview/index?url=${url}`,
      //     });
      //   }
      // } else if (item.adType === 1) {
      //   const url = vendorInfo.purchaseType == 'sup' ? '/pages_sub/pages/ccs/sup/skuDetail/index' : '/pages_sub/pages/ccs/down/skuDetail/index'
      //   wx.navigateTo({
      //     url: `${url}?itemId=${item.itemId}&vendorCode=${
      //       item.vendorCode ? item.vendorCode : vendorInfo.bvendorCode
      //     }&custCode=${item.custCode}&channelCode=${
      //       item.channelCode
      //     }&productCode=${item.productCode}`,
      //   });
      // } else {
      //   this.triggerEvent("clickSwiperItem", {
      //     index: even.currentTarget.dataset.index,
      //   });
      // }
    },
    onCurrentChange(e) {
      this.data.current = e.detail.current;
      this.setData({
        current: e.detail.current,
      });
    },
  },
});
