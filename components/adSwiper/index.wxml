<swiper indicator-dots indicator-color="#A9A9A9" indicator-active-color="{{indicatorActive}}" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" circular interval="3600" duration="1200" style="height:{{heightRpx}}rpx;position:relative" 	bindchange="onCurrentChange">
  <swiper-item wx:for="{{adList}}" wx:key="adId">
    <image catchtap="onClickAd" data-index="{{index}}" class="ad-image" src="{{item.picUrl}}" mode="aspectFit" lazy-load style="height:{{heightRpx}}rpx;border-radius: {{radiusRpx}}rpx;" />
  </swiper-item>
</swiper>
  <view wx:if="{{indicatorCount}}" class="swipter-count">{{current+1+'/'+adList.length}}</view>