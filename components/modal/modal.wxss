/* components/modal/modal.wxss */
/*遮罩层*/
.modal-mask{
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 999;
}
/*遮罩内容*/
.modal-content{
  display: flex;
  flex-direction: column;
  width: 72%;
  background-color: #fff;
  border-radius: 32rpx;
  text-align: center;
}
/*中间内容*/
.main-content{
  flex: 1;
  height: 100%;
  overflow-y: hidden; 
  max-height: 80vh; /* 内容高度最高80vh 以免内容太多溢出*/
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}
/*底部按钮*/
.modal-footer{
  display: flex;
  flex-direction: row;
  height: 90rpx;
  line-height: 90rpx;
  box-sizing: border-box;
  border-top: 1rpx solid rgba(0,0,0,0.10);
}
.cancel-btn, .confirm-btn{
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  font-family: PingFangSC-Regular;
}
.cancel-btn{
  color: rgba(0,0,0,0.45);
  border-right: 1rpx solid rgba(0,0,0,0.10);
}
.confirm-btn {
  color: #00b9c3;
}