const EVN = wx.getAccountInfoSync().miniProgram.envVersion
let domain = ''
let domainXJ = ''
let EVNval = ''
switch (EVN) {
  case 'develop': // 本地调试变量
    domainXJ = 'http://***********:9291' // UAT
    domain = 'https://youyou.shimge.com:50443' // UAT
    // domain = 'https://youyou.shimge.com:8443' // 正式服
    EVNval = '开发'
    // domain = 'https://ccs-sit-stable-pub.meicloud.com' // 开发
    break;
  case 'trial': // UAT 环境变量
    EVNval = 'UAT'
    domainXJ = 'http://*************' // UAT
    domain = 'https://youyou.shimge.com:50443' // UAT
    break;
  default:
    EVNval = '正式服'
    domainXJ = 'http://*************' // 正式
    domain = 'https://youyou.shimge.com:8443' // 正式服
    break;
}

module.exports = {
  DOMAIN: domain,
  DOMAINXJ: domainXJ,
  E<PERSON>val
}
// export const DOMAIN = domain