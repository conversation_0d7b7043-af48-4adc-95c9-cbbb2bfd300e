const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

const formatDateTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}
const moneyFormat = function (value, decimalL = 2) {
  if (!value || value == 0) return '0.00'
  if (typeof value != 'number') {
    value = parseFloat(value)
  }
  value = value.toFixed(decimalL)
  var arr = value.split('.')
  var leftVal = arr[0]
  leftVal = leftVal.replace(/\B(?=(\d{3})+$)/g, ",");
  return leftVal + '.' + arr[1]
}
const encodePassword = (password, randomLength = 20) => {
  const randomWordStr = randomWord(false, randomLength);
  let str = '';
  String(password).split('').forEach((item, i) => {
    str += item + randomWordStr[i];
  });

  function encode(str) {
    // encodeURI系统自带, toBase64自定义写的
    const encode = encodeURI(str);
    const base64 = toBase64(encode);
    return base64;
  }
  return encode(str);
}

const randomWord = (randomFlag, min, max) => {
  let str = '';
  let range = min;
  const arr = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ];

  // 随机产生
  if (randomFlag) {
    range = Math.round(Math.random() * (max - min)) + min;
  }
  for (let i = 0; i < range; i++) {
    const pos = Math.round(Math.random() * (arr.length - 1));
    str += arr[pos];
  }
  return str;
}

const toBase64 = (str) => {
  str = String(str)
  // Base64字符集
  const base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
  // 编码函数
  let result = '';
  for (let i = 0; i < str.length; i += 3) {
    let a = str.charCodeAt(i);
    let b = i + 1 < str.length ? str.charCodeAt(i + 1) : 0;
    let c = i + 2 < str.length ? str.charCodeAt(i + 2) : 0;

    let a1 = a >> 2,
      a2 = ((a & 3) << 4) | (b >> 4),
      a3 = ((b & 15) << 2) | (c >> 6),
      a4 = c & 63;
    result += base64Chars[a1] + base64Chars[a2] + (i + 1 < str.length ? base64Chars[a3] : '=') + (i + 2 < str.length ? base64Chars[a4] : '=');
  }
  return result;
}

module.exports = {
  formatTime: formatTime,
  formatDateTime: formatDateTime,
  moneyFormat,
  encodePassword
}