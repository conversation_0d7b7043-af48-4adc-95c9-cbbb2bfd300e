
import { <PERSON><PERSON>A<PERSON>, DOMAINXJ, E<PERSON><PERSON> } from './server'
const baseUrl = DOMAIN
const baseUrlXJ = DOMAINXJ
const http = ({ url = '', param = {}, method = 'GET', isReturnAll = false, needAuth = true, isScanPDA = false, isXJ = false } = {}) => {
  let selfHeader = {
    'content-type': 'application/json',
    'Accept-Language': 'zh-CN,zh;q=0.9',//后端搞了国际化,这个以后也是个问题前端怎么动态
  }
  if (wx.getStorageSync('checkSetsOfBooksId')) {
    selfHeader.Sets_of_books_id = wx.getStorageSync('checkSetsOfBooksId')
  }
  if (needAuth && wx.getStorageSync('authorization')) {
    selfHeader.Authorization = wx.getStorageSync('authorization')
  }
  const App = getApp()
  App.globalData.loadingCount++
  wx.showLoading({
    title: '工作中...',
  })
  return new Promise((resolve, reject) => {
    const requestTask = wx.request({
      url: getUrl(url),
      data: param,
      method: method,
      header: selfHeader,
      complete: (res) => {
        App.globalData.loadingCount--;
        if (App.globalData.loadingCount === 0) {
          wx.hideLoading();
        }
        if (isReturnAll) {
          // 62262新界扫码特殊处理
          if (res.data.code == 200 || res.data.code == 62262 || res.data.code == 62263 || res.data.code == 62256) {
            resolve(res.data)
          } else {
            const errMsg = res.data.chnDesc || '请求异常,稍后再尝试!'
            if (errMsg.length > 14) {
              wx.showModal({
                title: '异常提示',
                content: errMsg,
                showCancel: false
              })
            } else {
              wx.showToast({
                title: res.data.chnDesc || '请求异常,稍后再尝试!',
                icon: 'none',
                duration: 3000
              })
            }
            reject(res.data)
          }
        } else if (isScanPDA) {
          // 连续扫码PDA, 因为涉及界面焦点问题, 不适合拦截弹窗
          if (res.data.code == 200) {
            resolve(res.data.content || res.data.recordList)
          }else if( res.data.code == 62262 || res.data.code == 62263){
            resolve(res.data)
          } else {
            reject(res.data)
          }
        } else if (isXJ){
          //新界内部接口服务
          if(res.statusCode == 200)
            resolve(res.data)
          else
            reject(res.data)
        } else if (res.data.code == 200) {
          resolve(res.data.content || res.data.recordList)
        } else if (res.data.code == 401) {
          wx.showToast({
            title: res.data.detail || '请求异常,稍后再尝试!',
            icon: 'none',
            duration: 3000,
            complete: () => {
              reject('请重新登录.')
              setTimeout(() => {
                wx.reLaunch({ url: '/pages/login/index' })
              }, 3000);
            }
          })
        } else {
          const errMsg = res.data.chnDesc || '请求异常,稍后再尝试!'
          if (errMsg.length > 14) {
            wx.showModal({
              title: '异常提示',
              content: errMsg,
              showCancel: false
            })
          } else {
            wx.showToast({
              title: errMsg,
              icon: 'none',
              duration: 3000
            })
          }
          reject(res.data)
        }
      }
    })
    requestTask.onHeadersReceived((res) => {
      //判断是否有用户Token，如果有放在缓存中
      if (res && res.header && res.header.Authorization) {
        wx.setStorageSync('authorization', res.header.Authorization)
      }
    })
  })
}

const getUrl = (url) => {
  if (url.indexOf('://') == -1) {
    // 临时加个, 设置环境有限使用人工设置环境
    const tempBaseUrl = wx.getStorageSync('host') || baseUrl
    url = tempBaseUrl + url;
  }
  return url
}

// get方法
const _get = (url, param = {}) => {
  return http({
    url,
    param
  })
}

const _post = (url, param = {}, isReturnAll, needAuth) => {
  return http({
    url,
    param,
    method: 'POST',
    isReturnAll,
    needAuth
  })
}
const _requestXJ = (url = '', param = {}, method = 'GET') => {
  return http({
    url,
    param,
    method,
    isReturnAll: false,
    needAuth: false,
    isScanPDA: false,
    isXJ: true,
  })
}
const _postScanPDA = (url, param = {}) => {
  return http({
    url,
    param,
    method: 'POST',
    isReturnAll: false,
    needAuth: true,
    isScanPDA: true
  })
}

const _postWithCust = (url, requestParam = {}, isPage = false) => {
  const custInfo = wx.getStorageSync('custInfo')
  if (isPage) {
    requestParam.param = { ...requestParam.param, custCode: custInfo.custCode, custId: custInfo.custId, custType: custInfo.custType }
  } else {
    requestParam = { ...requestParam, custCode: custInfo.custCode, custId: custInfo.custId, custType: custInfo.custType }
  }
  return http({
    url,
    param: requestParam,
    method: 'POST',
  })
}
const _put = (url, param = {}) => {
  return http({
    url,
    param,
    method: 'PUT'
  })
}

const _delete = (url, param = {}) => {
  return http({
    url,
    param,
    method: 'PUT'
  })
}
module.exports = {
  _get,
  _post,
  _postWithCust,
  _postScanPDA,
  _requestXJ,
  _put,
  _delete,
  baseUrl,
  baseUrlXJ,
  EVNval
}