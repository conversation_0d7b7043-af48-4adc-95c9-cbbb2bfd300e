{"name": "sacon", "version": "1.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@vant/weapp": {"version": "1.10.23", "resolved": "https://repo.meicloud.com/repository/npm-group/@vant/weapp/-/weapp-1.10.23.tgz", "integrity": "sha512-Lqlc0oT89AxR8e4uLmP59xkI1KkWSTWKWV9kXM17CJz55eL0fPMo5jd0d132KpcfRmiwIOVr7TwK8u8v6XIhlg=="}, "dayjs": {"version": "1.11.9", "resolved": "https://repo.meicloud.com/repository/npm-group/dayjs/-/dayjs-1.11.9.tgz", "integrity": "sha512-QvzAURSbQ0pKdIye2txOzNaHmxtUBXerpY0FJsFXUMKbIZeFm5ht1LS/jFsrncjnmtv8HsG0W2g6c0zUjZWmpA=="}, "fast-deep-equal": {"version": "2.0.1", "resolved": "https://repo.meicloud.com/repository/npm-group/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w=="}, "miniprogram-computed": {"version": "4.4.0", "resolved": "https://repo.meicloud.com/repository/npm-group/miniprogram-computed/-/miniprogram-computed-4.4.0.tgz", "integrity": "sha512-th9y1Ua7H6rC47Vs20/QFC6zlq/A/92zbKrCxkJNlXf7xwNdg86BRRFvccR8yIwVXRXjMYUrcsVDq6bwzbE0Cg==", "requires": {"fast-deep-equal": "^2.0.1", "rfdc": "^1.1.4"}}, "rfdc": {"version": "1.3.0", "resolved": "https://repo.meicloud.com/repository/npm-group/rfdc/-/rfdc-1.3.0.tgz", "integrity": "sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA=="}}}