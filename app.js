//app.js
var HttpApi = require('./utils/api')
var SYS_USER_BENCH= [
  '/pages_sub/pages/ccs/sup/order/receipt/index',
  '/pages_sub/pages/ccs/down/order/list/index?appQueryStat=1',
  '/pages_sub/pages/ccs/down/salesOut/index',
  '/pages/ccs/retail/order/index',
  '/pages/ccs/inventory/list/list',
  '/pages/ccs/inventory/count/count',
  '/pages/ccs/inventory/otherInBill/index',
  '/pages_sub/pages/ccs/down/shop/index'
]
App({
  onLaunch: function (options) {
    // 获取菜单按钮（右上角胶囊按钮）的布局位置信息。
    const rect = wx.getMenuButtonBoundingClientRect()
    // 获取系统一些界面参数
    wx.getSystemInfo({
      success: (result) => {
        this.globalData.menuButtonClientReact = rect
        //iphone6 750rpx=375px, 微信是固定750rpx宽
        this.globalData.rpxToPxRatio = Number((result.windowWidth/750).toFixed(2))
        this.globalData.pxToRpxRatio = Number((750/result.windowWidth).toFixed(2))
        this.globalData.statusBarHeight = result.statusBarHeight
        this.globalData.windowWidth = result.windowWidth
        this.globalData.windowHeight = result.windowHeight
        this.globalData.screenHeight = result.screenHeight
        try {
          this.globalData.deviceBottomOccPx =  Number(result.screenHeight-result.safeArea.bottom)
        } catch (error) {
          this.globalData.deviceBottomOccPx = 0
        }
      },
    })
  },
  getHttp() {
    return HttpApi
  },
  getDict(dictCode, defaultValue) {
    const dict = wx.getStorageSync('dictMap')[dictCode]
    return dict ? dict : defaultValue
  },
  getCartsCount() {
    return new Promise((resolve, reject) => {
      const supInfo = wx.getStorageSync('supInfo')
      const custInfo =  wx.getStorageSync('custInfo')
      this.getHttp()._post('/api/vcs/mobile-web/myx/supOrder/countCartNum', {
        invoiceSetsOfBooksId: supInfo.setsOfBooksId
      }, true).then(res => {
        resolve(res.content)
      })
    })
  },
  checkUserBench(){
    const userSelfMenus = wx.getStorageSync('userBench')
    if(userSelfMenus&&userSelfMenus.length>0){
      const custMenus = wx.getStorageSync('custMenus')
      const authUserSelfMenu= userSelfMenus.filter(element => {
        return custMenus.findIndex(findMenu=>findMenu.perms&&findMenu.perms==element.to)>-1
      })
      if(authUserSelfMenu.length>0){
        wx.setStorageSync('userBench', authUserSelfMenu)
      }else{
        wx.removeStorageSync('userBench')
      }
    }
  },
  setDefaultUserBench(){
    const custMenus = wx.getStorageSync('custMenus')
    this.globalData.defaultUserBench = custMenus.filter(res=>res.perms&&SYS_USER_BENCH.includes(res.perms)).map(menu=>{
      return {
        icon: menu.icon,
        text: menu.name,
        to: menu.perms
      }  
    })
    this.checkUserBench()
  },
  globalData: {
    loadingCount:0,//http请求 loading的控制
    dicts: ['itemSpecies', 'usable', 'stat', 'sourceSystem','shipMode','warehouseType', 'newsTopCategory', 'problemType','NoticeMessageType', 'orderPayType', 'itemLineType', 'billType', 'itemSeries', 'saleOrgCode'],
    defaultUserBench:[],
    menuButtonClientReact:{},
    windowWidth:375,//默认iphone6, 建议开发机型和设计图机型
    windowHeight:667,//默认iphone6, 建议开发机型和设计图机型
    screenHeight:667,//默认iphone6, 建议开发机型和设计图机型
    rpxToPxRatio:0.5,//默认iphone6, 建议开发机型和设计图机型
    pxToRpxRatio:2, //默认iphone6, 建议开发机型和设计图机型
    deviceBottomOccPx:0, //默认iphone6, 建议开发机型和设计图机型
    statusBarHeight:20, //默认iphone6, 建议开发机型和设计图机型
    EVNval:HttpApi.EVNval, // 环境标识变量
  }
})