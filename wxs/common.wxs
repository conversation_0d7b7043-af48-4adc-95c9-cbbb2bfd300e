var moneyFormat = function (value) {
  if(!value && value !== 0){
    return '暂无价格'
  }
  if (!value || value == 0) return 0
  value = value.toString()
  if (value.indexOf('.') === -1) {
    value = value + ".00"
  } else {
    var decimal = value.split('.')
    if (decimal[1].length < 2) {
      value = decimal[0] + "." + (decimal[1] + '0')
    }
    //TODO可能家里要加else 只保留俩位小数
  }
  var arr = value.split('.')
  var leftVal = arr[0]
  leftVal = leftVal.replace(getRegExp('\B(?=(\d{3})+$)', 'g'), ",");
  return leftVal + '.' + arr[1]
}
// 获取整数和小数点
var moneyFormatInt = function (value,type) {
  if(!value && value !== 0 ){
    if(type == 'int'){
      return '暂无价格'
    }else{
      return ''
    }
  }
  value = value.toString()
  if(type == 'int'){
    value = value.split('.')[0]
    value = value.replace(getRegExp('\B(?=(\d{3})+$)', 'g'), ",");
  }else{
    var decimal = value.split('.')
    if(decimal[1]){
      if (decimal[1].length < 2) {
        value = "." + (decimal[1] + '0')
      }else if(decimal[1].length == 2){
        value = "." + decimal[1]
      } else {
        value = "." + decimal[1].slice(0, 2)
      }
    }else{
      value = ".00"
    }
  }
  return value
}
var limitArrLength = function (data, limitLength) {
  if (data && data.length > limitLength) {
    data.length = limitLength
  }
  return data
}
var dataDiff = function (statData, endData) {
  if(statData&&endData){
    // 2021-09-16换为2021/09/16 兼容IOS库,格式化时间
    var data1 = getDate(statData.replace(getRegExp('-', 'g'),'/'))
    var data2 = getDate(endData.replace(getRegExp('-', 'g'),'/'))
    return data2 - data1
  }else{
    return 0
  }
}
var formatColor = function (statu) {
  var color = '#0C92E0'
  if (statu == 1) {
    color = '#0C92E0'
  } else if (statu == 2) {
    color = '#F6A52C'
  } else if (statu == 3) {
    color = '#F97D4E'
  } else if (statu == 4) {
    color = 'rgba(0,0,0,0.45)'
  }
  return color
}
var formatItemCode = function (itemCode) {
  if (itemCode && itemCode.length > 10) {
    return itemCode.replace('0000000000', '')
  }
  return itemCode
}
var formatLabel = function (activeIndex,item) {
  // '1'全部  '2'待审核  '5'待发货  '98'待签收 '99'已完成
  var defaultLabel = '订购数：'+ item.applyQty
  switch (activeIndex) {
    case '1':
    case '99':
      defaultLabel = '订购数：'+item.applyQty
      break;
    case '2':
      defaultLabel = '待审数：'+item.toAuditQty
      break;
    case '5':
      defaultLabel = '待发数：'+item.toDeliveryQty
      break;
    case '98':
      defaultLabel = '待签数：'+item.signedQty
      break;
    default:
      break;
  }
  // return defaultLabel // 与后端确认后，订单列表各订单状态只展示总数量
  return 'x' + item.applyQty
}
var subString10 = function (txt) {
  if (txt && txt.length > 10) {
    var tempString = txt.substring(0, 10)
    return tempString+"..."
  }
  return txt
}
var fileIconName = function (fileType){
  console.log(fileType)
  if(includes(['xls','xlsx'], fileType)){
    return '/asset/imgs/excel.png'
  }else if(includes(['txt'],fileType)){
    return "/asset/imgs/txt.png"
  }else if(includes(['png','jpg','bmp', 'gif'],fileType)){
    return "/asset/imgs/pic.png"
  }else if(includes(['zip','rer','arj'],fileType)){
    return "/asset/imgs/zip.png"
  }else if(includes(['ppt','pptx'],fileType)){
    return "/asset/imgs/ppt.png"
  }else if(includes(['doc','docx'],fileType)){
    return "/asset/imgs/word.png"
  }else if(includes(['pdf'], fileType)){
    return "/asset/imgs/pdf.png"
  }
}
var formatGoodList = function(isShow, list, len) {
  if(!list || list.length == 0) {
    return []
  } else if(list.length < len) {
    return list
  } else {
    if(isShow) {
      var arr = list.slice(0,len)
      return arr
    } else {
      return list
    }
  }
}
var totalAmount = function(list) {
  var total = 0
  if(!list || list.length == 0) {
    return 0
  }
  for(var i = 0; i< list.length; i++) {
    var amount = parseFloat(list[i].applyPrice || 0) * parseFloat(list[i].applyQty)
    total += amount
  }
  return total
}
var formatState = function(value, list) {
  if(!value || !list || list.length == 0) {
    return ''
  }
  var statName = ''
  for(var i = 0; i< list.length; i++) {
    if(list[i].value == value) {
      statName = list[i].name
    }
  }
  return statName
}
var formatShipMode = function(value, list) {
  if(!value || !list || list.length == 0) {
    return ''
  }
  var shipModeName = ''
  for(var i = 0; i< list.length; i++) {
    if(list[i].value == value) {
      shipModeName = list[i].name
    }
  }
  return shipModeName
}
var retailOrderTotalAmount = function(sourceList, checkedList) {
  if(sourceList.length == 0 || checkedList.length == 0) {
    return 0
  }
  var total = 0
  var checkedItems = []
  for(var j = 0; j< sourceList.length; j++) {
    if(checkedList.indexOf(sourceList[j].itemId) != -1) {
      checkedItems.push(sourceList[j])
    }
  }
  for(var i = 0; i< checkedItems.length; i++) {
    var amount = (checkedItems[i].price || 0) * (checkedItems[i].qty || 0)
    total += amount
  }
  return total
}
var retailOrderTotalQty = function(sourceList, checkedList) {
  if(sourceList.length == 0 || checkedList.length == 0) {
    return 0
  }
  var total = 0
  var checkedItems = []
  for(var j = 0; j< sourceList.length; j++) {
    if(checkedList.indexOf(sourceList[j].itemId) != -1) {
      checkedItems.push(sourceList[j])
    }
  }
  for(var i = 0; i< checkedItems.length; i++) {
    total += (parseInt(checkedItems[i].qty) || 0)
  }
  return total
}
var formatPayType = function(list, value) {
  if(!value || !list || list.length == 0) {
    return ''
  }
  console.log(JSON.stringify(list), value)
  var payTypeName = ''
  for(var i = 0;i< list.length; i++) {
    if(list[i].value == value) {
      payTypeName = list[i].name
    }
  }
  return payTypeName
}
var formatBillType = function(value) {
  if(!value) {
    return '数量'
  }
  var typeText = ''
  // 销售出库单、零售出库单、其他出库单、销售退货单、零售退货单、显示出库数
  if(value == '0204' || value == '0205' || value == '0299' || value == '0297' || value == '0296' || value == '0206' || value == '0207') {
    typeText = '出库数'
  } else if(value == '0101' || value == '0199' || value == '0197' || value == '0196' || value == '0102') {
    // 采购入库单、其他入库单、采购退货单，显示入库数
    typeText = '入库数'
  }
  return typeText
}
includes = function includes(arr, value) {
  return arr.indexOf(value) >= 0;
}
getDictName = function (mapValue, dictItemValue) {
  console.log('result2222',mapValue, dictItemValue);
  var result = mapValue ? mapValue.filter(function(v) {return v.value == dictItemValue}) : []
  console.log('result',result);
  return result.length > 0 ? result[0].name : ''
}
var subStringLen = function (txt,length) {
  length = length ? length : 10;
  if (txt && txt.length > length) {
    var tempString = txt.substring(0, length)
    return tempString+"..."
  }
  return txt
}

var formatShopVendorInfo = function(vendorName, saleOrgName, channelName) {
  var tempChannelName = ''
  if(channelName&&channelName.indexOf('/')>-1){
    var splitArr = channelName.split('/')
    tempChannelName=" | "+splitArr[splitArr.length-1]
  }
  return vendorName+" | "+ saleOrgName +tempChannelName
}
var formatPercent = function (value,uom) {
 var tempValPercent = value?(value*100).toFixed(2):0
  return tempValPercent+uom
}
module.exports = {
  moneyFormat: moneyFormat,
  limitArrLength: limitArrLength,
  dataDiff: dataDiff,
  formatColor: formatColor,
  formatItemCode: formatItemCode,
  formatLabel:formatLabel,
  subString10:subString10,
  fileIconName: fileIconName,
  includes: includes,
  getDictName: getDictName,
  formatGoodList: formatGoodList,
  totalAmount: totalAmount,
  formatState: formatState,
  formatShipMode: formatShipMode,
  subStringLen: subStringLen,
  moneyFormatInt: moneyFormatInt,
  retailOrderTotalAmount: retailOrderTotalAmount,
  retailOrderTotalQty: retailOrderTotalQty,
  formatPayType: formatPayType,
  formatShopVendorInfo:formatShopVendorInfo,
  formatBillType: formatBillType,
  formatPercent:formatPercent
};