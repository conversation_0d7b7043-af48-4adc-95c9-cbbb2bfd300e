/**app.wxss**/
/* 主题颜色 */
Page {
  --ThemeColor: #00b9c3;
  --SuccessColor: #52bb26;
  --WarningColor: #f6a52c;
  --ErrorColor: #f73d47;
  --TextColor: #333333;
  --BorderColor: #f3f4f6;
  --AssistTextColor: #999999;
  --UatColor:#FFFF00;
  --button-info-background-color:#00b9c3;
  --button-info-border-color:#00b9c3;
  --checkbox-checked-icon-color:#00b9c3;
  --checkbox-checked-icon-color:#00b9c3
}
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  color: rgba(0,0,0,0.85);
}
.page-full {
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  color: rgba(0,0,0,0.85);
} 
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex_center {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex_1 {
  flex: 1;
  -webkit-flex: 1;
  display: block;
  min-width: 0;
}

.flex_2 {
  flex: 2;
  -webkit-flex: 2;
  display: block;
  min-width: 0;
}

.flexbox {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

/* 列 */
.horizontal {
  -webkit-box-orient: horizontal;
  -moz-box-orient: horizontal;
  -ms-box-orient: horizontal;
  -o-box-orient: horizontal;
  box-orient: horizontal;
}

/* 行 */
.column {
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  -o-flex-direction: column;
  flex-direction: column;
}

/* 水平 */
.justify-content-center {
  -ms-flex-pack: center;
  justify-content: center;
}

/* 垂直 */
.align-items-center {
  -ms-flex-align: center;
  align-items: center;
}

/* 底部对齐 */
.align-items-end {
  -ms-flex-align: flex-end;
  align-items: flex-end;
}

/* 水平两边对齐 */
.justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

/* 水平右对齐 */
.justify-content-end {
  justify-content: flex-end;
}

/* 水平左对齐 */
.justify-content-start {
  justify-content: flex-start;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}
.m-t-64{
  margin-top: 64rpx;
}
.m-t-32{
  margin-top: 32rpx;
}
.m-t-24{
  margin-top: 24rpx;
}
.m-t-16{
  margin-top: 16rpx;
}
.m-t-12{
  margin-top: 12rpx;
}
.m-t-8{
  margin-top: 8rpx;
}
.m-t-25p{
  margin-top: 25%;
}
.m-l-24{
  margin-left: 24rpx;
}
.m-l-32{
  margin-left: 32rpx;
}
.m-r-8{
  margin-right: 8rpx;
}
.m-r-16{
  margin-right: 16rpx;
}
.m-r-24{
  margin-right: 24rpx;
}
.bg-04{
  background: rgba(0,0,0,0.04);
}
.van-cell:after{
  left: 0 !important;
  right: 0  !important;
  border-bottom-color: #E8E9EA !important;
}

.font-main{
  font-family: PingFangSC-Medium;
  font-size: 28rpx;
  color: #242424;
  line-height: 44rpx;
}

.font-sub{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #242424;
  line-height: 44rpx;
}
.font-sub-hint{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #8A8A8A;
  line-height: 44rpx;
}
.font-s30-lh48{
  font-family: PingFangSC-Medium;
  font-size: 30rpx;
  color: rgba(0,0,0,0.85);
  line-height: 48rpx;
  font-weight: 500;
}
.font-s28-lh44{
  font-family: PingFangSC-Medium;
  font-size: 28rpx;
  color: #242424;
  line-height: 44rpx;
  font-weight: 500;
}
.font-s32-lh48{
  font-family:  PingFangSC-Medium, PingFang SC;
  font-size: 32rpx;
  color: #242424;
  line-height: 48rpx;
  font-weight: 500;
}
.font-s24-lh44{
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #242424 ;
  line-height: 44rpx;
  font-weight: 400;
}

.arrow-img{
  width: 48rpx;
  height: 48rpx;
}

.line{
  width: 100%;
  height: 1rpx;
  background-color: #E6E6E6;
  -webkit-transform-origin: 0 0; 
  transform-origin: 0 0; 
  -webkit-transform: scaleY(0.5); 
  transform: scaleY(0.5)
}
.single-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.two-line-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* qutoprefixer: off */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap:break-word;
  word-break: break-all;
  white-space: normal;
}
.van-tabs{
  border-bottom: 1rpx solid #E6E6E6;
}
.van-tabs__wrap,
.van-tabs__wrap scroll-view,
.van-tabs__wrap .van-tabs__nav,
.van-tabs__wrap .van-ellipsis,
.van-tabs__wrap .van-tab {
  height: 96rpx !important;
  line-height: 96rpx;
}
.van-ellipsis{
  font-size: 32rpx;
  color: #616161;
}
.van-tab--active .van-ellipsis{
  font-size: 32rpx;
  font-weight: 500;
  color: #00b9c3;
}
 .van-tabs__line{
  min-width: 80rpx;
  height: 6rpx;
  bottom: 6rpx;
  border-radius: 4rpx;
  background: #00b9c3 !important;
}
.van-dropdown-menu{
  box-shadow: 0 0 0 rgb(255,255, 255) !important;
}
.van-dropdown-menu__title{
  height: 44rpx !important;
  font-size: 26rpx !important;
  font-family: PingFangSC-Regular !important;
  color: rgba(0,0,0,0.75) !important;
  line-height: 44rpx !important;
}
.van-cell{
  padding: 24rpx !important;
} 
